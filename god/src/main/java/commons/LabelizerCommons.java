package commons;

import dao.BaseDao;
import entry.LabelizerEntry;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.ChatGPTResponse;
import pojo.LabelizerRequest;

import java.util.*;

public class LabelizerCommons {

    private static final Logger LOGGER = LoggerFactory.getLogger(LabelizerCommons.class.getName());

    public static List<LabelizerEntry> toEntries(List<LabelizerRequest> requests) throws Exception {
        List<LabelizerEntry> entries = new ArrayList<>();

        for (LabelizerRequest request : requests) {
            entries.add(toEntry(request));
        }

        return entries;
    }

    public static LabelizerEntry toEntry(LabelizerRequest request) throws Exception {
        LabelizerEntry entry = new LabelizerEntry();
        entry.setRequest(request);

        int totalTokens = 0;
        if (request.getChatGptResponses() != null && !request.getChatGptResponses().isEmpty()) {
            for (ObjectId oid : request.getChatGptResponses()) {
                ChatGPTResponse gptResponse = BaseDao.getDocumentById(oid, ChatGPTResponse.class);
                if (gptResponse != null) {
                    if (entry.getResponses() == null) {
                        entry.setResponses(new ArrayList<>());
                    }
                    entry.getResponses().add(gptResponse);
                    if (gptResponse.getUsage() != null && gptResponse.getUsage().getTotal_tokens() > 0) {
                        totalTokens += gptResponse.getUsage().getTotal_tokens();
                    }
                }
            }
        }
        entry.setTotalTokens(totalTokens);

        // mapping responses
        Map<String, ChatGPTResponse> responsesMap = new HashMap<>();
        if (request.getFilePaths() != null && !request.getFilePaths().isEmpty()) {
            if (entry.getResponses() != null && !entry.getResponses().isEmpty()) {
                for (String filePath : request.getFilePaths()) {
                    for (ChatGPTResponse response : entry.getResponses()) {
                        if (StringUtils.equals(filePath, response.getPath())) {
                            responsesMap.put(filePath, response);
                            break;
                        }
                    }
                }
            }
        }
        entry.setResponsesMap(responsesMap);

        // Calculate execution time
        String executionTime = calculateExecutionTime(request.getStart(), request.getEnd());
        entry.setExecutionTime(executionTime);

        return entry;
    }

    private static String calculateExecutionTime(Date start, Date end) {
        if (start == null || end == null) {
            return "N.A.";
        }

        long durationMillis = end.getTime() - start.getTime();
        long durationSeconds = durationMillis / 1000;

        long minutes = durationSeconds / 60;
        long seconds = durationSeconds % 60;

        return String.format("%02d:%02d", minutes, seconds);
    }
}
