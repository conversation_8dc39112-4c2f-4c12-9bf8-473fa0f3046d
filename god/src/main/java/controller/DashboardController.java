package controller;

import core.Core;
import core.Pages;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class DashboardController {

    private static final Logger LOGGER = LoggerFactory.getLogger(DashboardController.class.getName());

    public static TemplateViewRoute be_dashboard = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes);
        if (user == null) {
            return Core.render(Pages.BE_LOGIN, attributes, request);
        }

        return Core.render(Pages.BE_DASHBOARD, attributes, request);
    };

}
