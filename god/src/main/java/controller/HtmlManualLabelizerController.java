package controller;

import com.google.gson.Gson;
import commons.LabelizerCommons;
import core.Core;
import core.Pages;
import dao.BaseDao;
import dao.DaoFilters;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jetty.http.HttpStatus;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.LabelizerRequest;
import pojo.QueryOptions;
import pojo.User;
import enums.ProfileType;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.TemplateViewRoute;
import utils.HtmlLabelReplacer;
import utils.HtmlTextExtractor;
import utils.RequestUtils;
import utils.UploadedFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

/**
 * Controller for manual HTML labeling
 * <AUTHOR> Assistant
 */
public class HtmlManualLabelizerController {

    private static final Logger LOGGER = LoggerFactory.getLogger(HtmlManualLabelizerController.class.getName());

    /**
     * Display the manual labelizer page
     */
    public static TemplateViewRoute be_manual_labelizer = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);

        return null;
        // return Core.render(Pages.BE_MANUAL_LABELIZER, attributes, request);
    };

    /**
     * Scan an HTML file and return potential text to label
     */
    public static Route be_manual_labelizer_scan = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);

        String filePath = params.get("path");
        if (StringUtils.isBlank(filePath)) {
            response.status(HttpStatus.BAD_REQUEST_400);
            return "Path non valido.";
        }

        try {
            Map<String, String> textLabels = HtmlLabelReplacer.scanHtmlForLabels(filePath);

            JSONArray results = new JSONArray();
            for (Map.Entry<String, String> entry : textLabels.entrySet()) {
                JSONObject item = new JSONObject();
                item.put("text", entry.getKey());
                item.put("label", entry.getValue());
                results.put(item);
            }

            JSONObject result = new JSONObject();
            result.put("items", results);
            result.put("count", textLabels.size());

            response.type("application/json");
            return result.toString(2);
        } catch (Exception e) {
            LOGGER.error("Errore durante la scansione dell'HTML: " + e.getMessage(), e);
            response.status(HttpStatus.INTERNAL_SERVER_ERROR_500);
            return "Errore durante la scansione: " + e.getMessage();
        }
    };

    /**
     * Replace text in an HTML file with a label
     */
    public static Route be_manual_labelizer_replace = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);

        String filePath = params.get("path");
        String originalText = params.get("text");
        String labelKey = params.get("label");

        if (StringUtils.isBlank(filePath)) {
            response.status(HttpStatus.BAD_REQUEST_400);
            return "Path non valido.";
        }
        if (StringUtils.isBlank(originalText)) {
            response.status(HttpStatus.BAD_REQUEST_400);
            return "Testo originale non valido.";
        }
        if (StringUtils.isBlank(labelKey)) {
            response.status(HttpStatus.BAD_REQUEST_400);
            return "Chiave label non valida.";
        }

        try {
            boolean success = HtmlLabelReplacer.replaceTextWithLabel(filePath, originalText, labelKey);

            JSONObject result = new JSONObject();
            result.put("success", success);
            if (!success) {
                result.put("error", "Testo non trovato nel file HTML.");
            }

            response.type("application/json");
            return result.toString();
        } catch (Exception e) {
            LOGGER.error("Errore durante la sostituzione del testo: " + e.getMessage(), e);
            response.status(HttpStatus.INTERNAL_SERVER_ERROR_500);
            return "Errore durante la sostituzione: " + e.getMessage();
        }
    };

    /**
     * Create a labeled HTML file from a labels JSON file
     */
    public static Route be_manual_labelizer_create = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);

        String htmlPath = params.get("htmlPath");
        String jsonPath = params.get("jsonPath");

        if (StringUtils.isBlank(htmlPath)) {
            response.status(HttpStatus.BAD_REQUEST_400);
            return "Path HTML non valido.";
        }
        if (StringUtils.isBlank(jsonPath)) {
            response.status(HttpStatus.BAD_REQUEST_400);
            return "Path JSON non valido.";
        }

        try {
            Path outputPath = HtmlLabelReplacer.createLabeledHtmlFile(htmlPath, jsonPath);

            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("outputPath", outputPath.toString());

            response.type("application/json");
            return result.toString();
        } catch (Exception e) {
            LOGGER.error("Errore durante la creazione del file HTML con label: " + e.getMessage(), e);
            response.status(HttpStatus.INTERNAL_SERVER_ERROR_500);
            return "Errore durante la creazione del file: " + e.getMessage();
        }
    };
}
