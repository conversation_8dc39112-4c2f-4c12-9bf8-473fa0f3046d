package controller;

import core.Routes;
import dao.BaseDao;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.DocumentDescriptor;
import spark.Request;
import spark.Response;
import spark.Route;
import utils.Defaults;
import utils.EnvironmentUtils;
import utils.RequestUtils;
import utils.RoutesUtils;
import utils.UploadedFile;

/**
 *
 * <AUTHOR>
 */
public class ImageController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ImageController.class.getName());

    public static Route image = (Request request, Response response) -> {
        ObjectId oid = RequestUtils.toObjectId(request.queryParams("oid"));

        if (oid != null) {
            DocumentDescriptor document = BaseDao.getDocumentById(oid, DocumentDescriptor.class, "");

            if (document != null) {
                String contentType = document.getMetadata().get("contentType");
                if (StringUtils.isNotBlank(contentType)) {
                    response.type(contentType);
                }

                if (EnvironmentUtils.hasNotResourcesHotDeploy()) {
                    // image caching
                    response.header("Cache-Control", "private, max-age=" + Defaults.STATIC_RESOURCE_EXPIRATION_TIME);
                    response.header("Expires", new Date(System.currentTimeMillis() + (Defaults.STATIC_RESOURCE_EXPIRATION_TIME * 1000)).toString());
                }
                response.header("filename", document.getMetadata().get("originalFilename"));

                try {
                    File file = new File(document.getFilePath());
                    try (FileInputStream fis = new FileInputStream(file);  ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
                        byte[] buffer = new byte[1024];
                        int bytesRead;

                        while ((bytesRead = fis.read(buffer)) != -1) {
                            bos.write(buffer, 0, bytesRead);
                        }

                        document.setContent(bos.toByteArray());
                    }
                    if (document.getContent() != null && document.getContent().length > 0) {
                        response.raw().getOutputStream().write(document.getContent());
                        response.raw().getOutputStream().close();
                    }
                } catch (IOException ex) {
                    LOGGER.error("cannot return image; id " + oid + " exception class is " + ex.getClass().getSimpleName(), ex);
                }

            } else {
                LOGGER.warn("empty image oid " + oid);
            }
        } else {
            LOGGER.warn("empty oid " + oid);
        }

        return "";
    };

    public static Route image_save = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        if (!files.isEmpty()) {
            List<ObjectId> imageIds = BaseDao.saveImages(new ArrayList<>(files.values()), null, "");
            return "{ \"uploaded\": true, \"url\": \"" + RoutesUtils.contextPath(request) + Routes.BE_IMAGE + "?oid=" + StringUtils.join(imageIds, ",") + "\" }";
        } else {
            return "empty files";
        }
    };

    public static Route image_blob = (Request request, Response response) -> {
        ObjectId oid = RequestUtils.toObjectId(request.queryParams("oid"));
        if (oid != null) {
            DocumentDescriptor document = BaseDao.getDocumentById(oid, DocumentDescriptor.class, "");
            if (StringUtils.isNotBlank(document.getFilePath())) {
                File file = new File(document.getFilePath());
                try (FileInputStream fis = new FileInputStream(file);  ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
                    byte[] buffer = new byte[1024];
                    int bytesRead;

                    while ((bytesRead = fis.read(buffer)) != -1) {
                        bos.write(buffer, 0, bytesRead);
                    }

                    document.setContent(bos.toByteArray());
                }
            }

            return document;
        }

        return null;
    };
}
