package controller;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import core.Core;
import core.Pages;
import dao.BaseDao;
import enums.ProfileType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.HandsonTable;
import pojo.Label;
import pojo.LabelItem;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;
import utils.Defaults;
import utils.RequestUtils;
import utils.UploadedFile;

/**
 *
 * <AUTHOR>
 */
public class LabelController {

    private static final Logger LOGGER = LoggerFactory.getLogger(LabelController.class.getName());

    public static TemplateViewRoute be_labels = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        return Core.render(Pages.BE_LABELS, attributes, request);
    };

    public static Route be_labels_data = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        List<String> availableLanguages = new ArrayList<>(Defaults.AVAILABLE_LANGUAGES);

        List<Label> labels = BaseDao.getDocuments(Label.class);

        JsonArray jsonArray = new JsonArray();

        for (Label label : labels) {
            JsonObject jsonObject = new JsonObject();
            jsonObject.addProperty("key", label.getKey());

            for (LabelItem item : label.getItems()) {
                if (availableLanguages.contains(item.getLanguage())) {
                    jsonObject.addProperty(item.getLanguage(), item.getDescription());
                }
            }
            jsonArray.add(jsonObject);
        }

        // se non c'è nulla metto riga vuota altrimenti non posso fare nulla
        if (jsonArray.isEmpty()) {
            JsonObject jsonObject = new JsonObject();
            jsonArray.add(jsonObject);
        }
        return jsonArray.toString();

    };


    public static Route be_labels_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        // parse table data
        String json = params.get("json");
        HandsonTable table = null;
        if (StringUtils.isNotBlank(json)) {
            table = Core.deserializeFromJson(json, HandsonTable.class);
        }

        if (table != null) {

            // convert labels
            List<Label> labels = null;
            if (table.getData() != null) {
                labels = new ArrayList<>();
                for (String[] row : table.getData()) {
                    if (row.length > 0) {

                        if (row[0] != null) {
                            Label label = new Label();
                            label.setKey(row[0]);
                            label.setItems(new ArrayList<>());

                            for (String language : Defaults.AVAILABLE_LANGUAGES) {
                                int index = Defaults.AVAILABLE_LANGUAGES.indexOf(language);
                                LabelItem item = new LabelItem();
                                item.setLanguage(language);
                                if (row.length >= (index + 1)) {
                                    item.setDescription(row[index + 1]);
                                }
                                label.getItems().add(item);
                            }

                            labels.add(label);
                        }
                    }
                }
            }

            // clear pre-existing labels
            BaseDao.deleteCollection(Label.class);
            BaseDao.insertDocuments(labels);
            // clear labels cache
//            LabelFunction.clear();
        } else {
            Spark.halt(HttpStatus.BAD_REQUEST_400, "Labels not updated");
        }

        return "ok";
    };

}
