package controller;

import com.google.gson.Gson;
import commons.LabelizerCommons;
import core.Core;
import core.Pages;
import dao.BaseDao;
import dao.DaoFilters;
import entry.LabelizerEntry;
import org.bson.types.ObjectId;
import pojo.ChatGPTResponse;
import enums.ProfileType;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jetty.http.HttpStatus;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.LabelizerRequest;
import pojo.QueryOptions;
import utils.FileChunkingUtils;
import pojo.User;
import spark.*;
import utils.*;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class LabelizerController {

    private static final Logger LOGGER = LoggerFactory.getLogger(LabelizerController.class.getName());
    private static final String GEMINI_API_KEY = "AIzaSyClYWqpprmroLGu1PffV7PCD6JDdTMmxVA";

    // Gemini 2.5 Flash token limits and chunking configuration
    private static final int MAX_INPUT_TOKENS = 1048576; // 1M tokens for Gemini 2.5 Flash
    private static final int SAFE_CHUNK_SIZE_TOKENS = 30000; // Leave room for prompt and safety margin
    private static final int CHARS_PER_TOKEN = 4; // Approximate ratio for token estimation
    private static final int SAFE_CHUNK_SIZE_CHARS = SAFE_CHUNK_SIZE_TOKENS * CHARS_PER_TOKEN;

    // In-memory map to track progress of running labelizer requests
    private static final Map<String, LabelizerProgress> progressMap = new ConcurrentHashMap<>();

    // Progress tracking class
    public static class LabelizerProgress {
        private int totalFiles;
        private int processedFiles;
        private String currentFile;
        private boolean isCompleted;
        private boolean hasError;
        private String errorMessage;
        private int totalTokens;

        public LabelizerProgress(int totalFiles) {
            this.totalFiles = totalFiles;
            this.processedFiles = 0;
            this.currentFile = "";
            this.isCompleted = false;
            this.hasError = false;
            this.totalTokens = 0;
            this.totalChunks = 0;
            this.processedChunks = 0;
            this.currentChunk = "";
        }

        // Getters and setters
        public int getTotalFiles() { return totalFiles; }
        public int getProcessedFiles() { return processedFiles; }
        public void setProcessedFiles(int processedFiles) { this.processedFiles = processedFiles; }
        public String getCurrentFile() { return currentFile; }
        public void setCurrentFile(String currentFile) { this.currentFile = currentFile; }
        public boolean isCompleted() { return isCompleted; }
        public void setCompleted(boolean completed) { this.isCompleted = completed; }
        public boolean hasError() { return hasError; }
        public void setHasError(boolean hasError) { this.hasError = hasError; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        public int getTotalTokens() { return totalTokens; }
        public void setTotalTokens(int totalTokens) { this.totalTokens = totalTokens; }

        // Chunking support
        private int totalChunks;
        private int processedChunks;
        private String currentChunk;

        public int getTotalChunks() { return totalChunks; }
        public void setTotalChunks(int totalChunks) { this.totalChunks = totalChunks; }
        public int getProcessedChunks() { return processedChunks; }
        public void setProcessedChunks(int processedChunks) { this.processedChunks = processedChunks; }
        public String getCurrentChunk() { return currentChunk; }
        public void setCurrentChunk(String currentChunk) { this.currentChunk = currentChunk; }

        public int getPercentage() {
            return totalFiles > 0 ? (processedFiles * 100) / totalFiles : 0;
        }

        public String getDetailedProgress() {
            if (totalChunks > 1) {
                return String.format("File %d/%d, Chunk %d/%d", processedFiles + 1, totalFiles, processedChunks + 1, totalChunks);
            }
            return String.format("File %d/%d", processedFiles + 1, totalFiles);
        }
    }

    public static TemplateViewRoute be_labelizer = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // logged user
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(null, 0, 5, "start", "desc");
        List<LabelizerRequest> requests = BaseDao.getDocumentsByFilters(LabelizerRequest.class, queryOptions);
        List<LabelizerEntry> entries = LabelizerCommons.toEntries(requests);
        entries.sort(Comparator.comparing(LabelizerEntry::getRequest, Comparator.comparing(LabelizerRequest::getStart)).reversed());
        attributes.put("entries", entries);

        return Core.render(Pages.BE_LABELIZER, attributes, request);
    };

    public static Route be_labelizer_run = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String name = params.get("name");
        String path = params.get("path");
        String languageParam = params.get("languages");

        if (StringUtils.isBlank(name)) {
            // se il nome non viene specificato ne creiamo uno casuale
            name = UUID.randomUUID().toString();
        }
        if (StringUtils.isBlank(path)) {
            response.status(HttpStatus.BAD_REQUEST_400);
            return "Path non valido.";
        }
        if (StringUtils.isBlank(languageParam)) {
            response.status(HttpStatus.BAD_REQUEST_400);
            return "Selezionare almeno una lingua.";
        }
        if (StringUtils.isBlank(GEMINI_API_KEY)) {
            response.status(HttpStatus.INTERNAL_SERVER_ERROR_500);
            return "Gemini API KEY Mancante (LabelizerController.GEMINI_API_KEY).";
        }
        try {
            List<String> languages = Arrays.asList(languageParam.split(","));
            Path basePath = Paths.get(path);

            // Find all HTML files first to set total count
            List<Path> htmlFiles = findHtmlFiles(basePath);

            if (htmlFiles.isEmpty()) {
                response.status(HttpStatus.BAD_REQUEST_400);
                return "Nessun file HTML trovato nel percorso specificato.";
            }

            // Create labelizer request and save to database
            LabelizerRequest labelizerRequest = new LabelizerRequest();
            labelizerRequest.setName(name);
            labelizerRequest.setPath(path);
            labelizerRequest.setStart(new Date());
            labelizerRequest.setLanguages(languages);
            labelizerRequest.setTotalFiles(htmlFiles.size());
            labelizerRequest.setProcessedFiles(0);
            labelizerRequest.setFilePaths(new ArrayList<>());

            ObjectId requestId = BaseDao.insertDocument(labelizerRequest);
            labelizerRequest.setId(requestId);

            // Create progress tracker
            LabelizerProgress progress = new LabelizerProgress(htmlFiles.size());
            progressMap.put(requestId.toString(), progress);

            // Start async processing
            CompletableFuture.runAsync(() -> {
                processLabelizerAsync(requestId.toString(), labelizerRequest, htmlFiles, languages);
            });

            // Return immediately with request ID
            JSONObject result = new JSONObject();
            result.put("requestId", requestId.toString());
            response.type("application/json");
            return result.toString();

        } catch (Exception e) {
            LOGGER.error("Errore durante l'avvio del labelizer: " + e.getMessage(), e);
            response.status(HttpStatus.INTERNAL_SERVER_ERROR_500);
            return "Errore durante l'avvio del labelizer: " + e.getMessage();
        }
    };

    public static Route be_labelizer_progress = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);

        String requestId = params.get("requestId");
        if (StringUtils.isBlank(requestId)) {
            response.status(HttpStatus.BAD_REQUEST_400);
            return "Request ID mancante.";
        }

        try {
            // Check progress map first
            LabelizerProgress progress = progressMap.get(requestId);
            if (progress == null) {
                response.status(HttpStatus.NOT_FOUND_404);
                return "Richiesta non trovata o completata.";
            }

            JSONObject progressData = new JSONObject();
            progressData.put("totalFiles", progress.getTotalFiles());
            progressData.put("processedFiles", progress.getProcessedFiles());
            progressData.put("currentFile", progress.getCurrentFile());
            progressData.put("isCompleted", progress.isCompleted());
            progressData.put("percentage", progress.getPercentage());
            progressData.put("hasError", progress.hasError());

            // Add chunking information
            progressData.put("totalChunks", progress.getTotalChunks());
            progressData.put("processedChunks", progress.getProcessedChunks());
            progressData.put("currentChunk", progress.getCurrentChunk());
            progressData.put("detailedProgress", progress.getDetailedProgress());

            if (progress.hasError()) {
                progressData.put("errorMessage", progress.getErrorMessage());
            }
            if (progress.isCompleted()) {
                progressData.put("totalTokens", progress.getTotalTokens());
            }

            response.type("application/json");
            return progressData.toString();

        } catch (Exception e) {
            LOGGER.error("Errore durante il recupero del progresso: " + e.getMessage(), e);
            response.status(HttpStatus.INTERNAL_SERVER_ERROR_500);
            return "Errore durante il recupero del progresso: " + e.getMessage();
        }
    };

    private static void processLabelizerAsync(String requestId, LabelizerRequest labelizerRequest, List<Path> htmlFiles, List<String> languages) {
        LabelizerProgress progress = progressMap.get(requestId);
        if (progress == null) {
            LOGGER.error("Progress tracker not found for request: " + requestId);
            return;
        }

        try {
            int totalTokens = 0;
            int processedCount = 0;

            for (Path htmlFile : htmlFiles) {
                // Update progress
                progress.setCurrentFile(htmlFile.getFileName().toString());
                progress.setProcessedFiles(processedCount);

                String content = Files.readString(htmlFile);

                // Process file with chunking support
                int tokens = processFileWithChunking(content, htmlFile, labelizerRequest, languages, progress);
                totalTokens += tokens;
                labelizerRequest.getFilePaths().add(htmlFile.toString());

                processedCount++;
                BaseDao.updateDocument(labelizerRequest);
            }

            // Mark as completed
            labelizerRequest.setEnd(new Date());
            labelizerRequest.setProcessedFiles(processedCount);
            labelizerRequest.setCurrentFile("");
            BaseDao.updateDocument(labelizerRequest);

            // Update progress tracker
            progress.setProcessedFiles(processedCount);
            progress.setCurrentFile("");
            progress.setTotalTokens(totalTokens);
            progress.setCompleted(true);

            // Remove from progress map after a delay to allow final status check
            CompletableFuture.runAsync(() -> {
                try {
                    Thread.sleep(30000); // Keep for 30 seconds after completion
                    progressMap.remove(requestId);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });

        } catch (Exception e) {
            LOGGER.error("Errore durante l'elaborazione asincrona del labelizer: " + e.getMessage(), e);
            progress.setHasError(true);
            progress.setErrorMessage(e.getMessage());
            progress.setCompleted(true);
        }
    }

    public static List<Path> findHtmlFiles(Path startPath) throws IOException {
        List<Path> htmlFiles = new ArrayList<>();
        Files.walk(startPath)
                .filter(path -> Files.isRegularFile(path) && path.toString().endsWith(".html") && !path.toString().endsWith("_translated.html"))
                .forEach(htmlFiles::add);
        return htmlFiles;
    }

    public static String callGemini(String htmlContent, List<String> languages) throws IOException, InterruptedException {
        StringBuilder jsonFields = new StringBuilder();
        for (String lang : languages) {
            jsonFields.append("       \"").append(lang).append("\": \"...\",\n");
        }

        String prompt =
                "Ricevi un file HTML. Per ogni testo visibile (no tag <script>, <style>, <meta>):\n" +
                        "1. Identifica ogni blocco di testo continuo come una singola unità di traduzione, anche se contiene tag HTML di formattazione come <br>, <span>, <strong>, <em>, ecc.\n" +
                        "2. Per ogni unità di traduzione:\n" +
                        "   - Genera una chiave di localizzazione leggibile basata SOLO sul contenuto testuale (ignorando i tag HTML per la chiave)\n" +
                        "   - Esempio: \"Vivi Monte Isola con<br>Ristorante La Foresta\" → chiave: \"live.monte.isola.restaurant.foresta\"\n" +
                        "   - Sostituisci l'intero contenuto (testo + tag HTML interni) con la funzione label e la chiave di localizzazione per pebble es. {{ label('live.monte.isola.restaurant.foresta') | raw }}\n" +
                        "   - Nel campo 'original' del JSON, mantieni il testo completo inclusi i tag HTML di formattazione\n" +
                        "   - Metti come prefisso il nome.pagina (presente nel campo 'pageName' del JSON)\n" +
                        "   - Scrivi sempre le label in inglese\n" +
                        "   - La label non ha mai più di 7 parole separate da punto, senza caratteri speciali (solo lettere dalla A alla Z in minuscolo)\n" +
                        "   - Esempio: pageName 'homepage' + testo 'Invia il tuo messaggio' → 'homepage.send.your.message'\n" +
                        "3. Regole per i tag HTML:\n" +
                        "   - Tag di formattazione (<br>, <span>, <strong>, <em>, <i>, <b>) vanno inclusi nel contenuto originale\n" +
                        "   - Tag strutturali (contenitori come <div>, <p>, <h1>, <a>) definiscono i confini delle unità di traduzione\n" +
                        "   - Se un tag contiene solo formattazione interna e non contiene altri tag strutturali, trattalo come un'unica unità\n" +
                        "4. Genera un JSON con struttura:\n" +
                        "   [\n" +
                        "     {\n" +
                        "       \"label\": \"live.monte.isola.restaurant.foresta\",\n" +
                        "       \"original\": \"Vivi Monte Isola con<br>Ristorante La Foresta\",\n" +
                        jsonFields.toString() +
                        "     }\n" +
                        "   ]\n" +
                        "5. Per ogni lingua richiesta fornisci la traduzione reale mantenendo la stessa struttura HTML del testo originale.\n" +
                        "6. Le traduzioni devono preservare i tag HTML di formattazione nella stessa posizione del testo originale.\n" +
                        "\n" +
                        "Restituisci prima l'HTML modificato (solo l'HTML), poi il JSON separato da una riga contenente solo:\n" +
                        "===TRANSLATIONS===";

        String fullPrompt = prompt + "\n\n" + htmlContent;

        String requestBody =
                "{\n" +
                        "  \"contents\": [\n" +
                        "    {\n" +
                        "      \"parts\": [\n" +
                        "        {\"text\": " + JSONObject.quote(fullPrompt) + "}\n" +
                        "      ]\n" +
                        "    }\n" +
                        "  ],\n" +
                        "  \"generationConfig\": {\n" +
                        "    \"temperature\": 0.2\n" +
                        "  }\n" +
                        "}";

        HttpClient client = HttpClient.newHttpClient();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent"))
                .header("Content-Type", "application/json")
                .header("x-goog-api-key", GEMINI_API_KEY)
                .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                .build();

        HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

        // Check for MAX_TOKENS error in response
        String responseBody = response.body();
        if (isMaxTokensError(responseBody)) {
            throw new IOException("MAX_TOKENS: Content too large for Gemini API. Response: " + responseBody);
        }

        return responseBody;
    }

    /**
     * Checks if the Gemini API response indicates a MAX_TOKENS error
     */
    private static boolean isMaxTokensError(String responseBody) {
        if (responseBody == null) return false;

        try {
            JSONObject json = new JSONObject(responseBody);

            // Check for error in response
            if (json.has("error")) {
                JSONObject error = json.getJSONObject("error");
                String message = error.optString("message", "").toLowerCase();
                String code = error.optString("code", "");

                // Check for various MAX_TOKENS error patterns
                return message.contains("max_tokens") ||
                       message.contains("maximum") && message.contains("token") ||
                       message.contains("too large") ||
                       message.contains("exceeds") && message.contains("limit") ||
                       "INVALID_ARGUMENT".equals(code) && message.contains("token");
            }

            // Check for blocked content due to size
            if (json.has("candidates")) {
                JSONArray candidates = json.getJSONArray("candidates");
                for (int i = 0; i < candidates.length(); i++) {
                    JSONObject candidate = candidates.getJSONObject(i);
                    if (candidate.has("finishReason")) {
                        String finishReason = candidate.getString("finishReason");
                        if ("MAX_TOKENS".equals(finishReason) || "LENGTH".equals(finishReason)) {
                            return true;
                        }
                    }
                }
            }
        } catch (Exception e) {
            // If we can't parse the response, check for text patterns
            String lowerResponse = responseBody.toLowerCase();
            return lowerResponse.contains("max_tokens") ||
                   lowerResponse.contains("maximum") && lowerResponse.contains("token") ||
                   lowerResponse.contains("too large") ||
                   lowerResponse.contains("exceeds") && lowerResponse.contains("limit");
        }

        return false;
    }

    /**
     * Processes a file with automatic chunking support when content is too large
     */
    private static int processFileWithChunking(String content, Path htmlFile, LabelizerRequest labelizerRequest,
                                             List<String> languages, LabelizerProgress progress) throws IOException, InterruptedException {

        // Check if content needs chunking
        if (!FileChunkingUtils.needsChunking(content, SAFE_CHUNK_SIZE_TOKENS)) {
            // Content is small enough, process normally
            progress.setTotalChunks(1);
            progress.setProcessedChunks(0);
            progress.setCurrentChunk("Processing single file");

            try {
                String geminiResponse = callGemini(content, languages);
                int tokens = saveResponseToFiles(geminiResponse, htmlFile, labelizerRequest);

                progress.setProcessedChunks(1);
                progress.setCurrentChunk("");

                return tokens;
            } catch (IOException e) {
                if (e.getMessage() != null && e.getMessage().startsWith("MAX_TOKENS:")) {
                    LOGGER.warn("File {} hit MAX_TOKENS limit, falling back to chunking", htmlFile.getFileName());
                    // Fall through to chunking logic
                } else {
                    throw e; // Re-throw other IOExceptions
                }
            }
        }

        // Content needs chunking or we got MAX_TOKENS error
        LOGGER.info("Processing file {} with chunking due to size: {} characters",
                   htmlFile.getFileName(), content.length());

        List<FileChunkingUtils.HtmlChunk> chunks = FileChunkingUtils.chunkHtmlContent(content, SAFE_CHUNK_SIZE_CHARS);
        progress.setTotalChunks(chunks.size());
        progress.setProcessedChunks(0);

        List<String> chunkResults = new ArrayList<>();
        int totalTokens = 0;

        for (int i = 0; i < chunks.size(); i++) {
            FileChunkingUtils.HtmlChunk chunk = chunks.get(i);
            progress.setCurrentChunk(String.format("Chunk %d/%d (%d tokens)",
                                                  i + 1, chunks.size(), chunk.getEstimatedTokens()));

            try {
                String chunkResponse = callGemini(chunk.getContent(), languages);
                chunkResults.add(chunkResponse);

                // Extract token count from chunk response
                JSONObject json = new JSONObject(chunkResponse);
                if (json.has("usageMetadata")) {
                    JSONObject usageMetadata = json.getJSONObject("usageMetadata");
                    if (usageMetadata.has("totalTokenCount")) {
                        totalTokens += usageMetadata.getInt("totalTokenCount");
                    }
                }

                progress.setProcessedChunks(i + 1);

                LOGGER.info("Successfully processed chunk {}/{} for file {}",
                           i + 1, chunks.size(), htmlFile.getFileName());

            } catch (IOException e) {
                if (e.getMessage() != null && e.getMessage().startsWith("MAX_TOKENS:")) {
                    LOGGER.error("Chunk {}/{} still too large for file {}: {}",
                               i + 1, chunks.size(), htmlFile.getFileName(), e.getMessage());
                    throw new IOException("File chunk still too large even after chunking. Consider reducing chunk size or simplifying content.");
                } else {
                    throw e;
                }
            }
        }

        // Merge chunk results
        String mergedResponse = FileChunkingUtils.mergeChunkResults(chunkResults);

        // Save merged response
        int savedTokens = saveResponseToFiles(mergedResponse, htmlFile, labelizerRequest);

        progress.setCurrentChunk("");

        LOGGER.info("Successfully processed file {} with {} chunks, total tokens: {}",
                   htmlFile.getFileName(), chunks.size(), totalTokens);

        return Math.max(totalTokens, savedTokens); // Return the higher count
    }

    public static int saveResponseToFiles(String apiResponse, Path originalHtmlFile, LabelizerRequest labelizerRequest) throws IOException {
        // 1. Parse JSON response e prendi solo il contenuto del messaggio
        JSONObject json = new JSONObject(apiResponse);
        JSONArray candidates = json.getJSONArray("candidates");
        if (candidates.isEmpty()) {
            throw new IOException("Nessuna risposta da Gemini.");
        }
        JSONObject content = candidates.getJSONObject(0).getJSONObject("content");
        JSONArray parts = content.getJSONArray("parts");
        if (parts.isEmpty()) {
            throw new IOException("Nessun contenuto nelle parti della risposta Gemini.");
        }
        String contentText = parts.getJSONObject(0).getString("text").trim();

        // 2. Rimuovi eventuali blocchi ```html e ```json
        contentText = contentText.replaceAll("(?s)```html\\s*", "")
                .replaceAll("(?s)```json\\s*", "")
                .replaceAll("(?s)```", "") // chiusura blocchi markdown
                .trim();

        // 3. Separazione tra HTML e JSON
        String delimiter = "===TRANSLATIONS===";
        String[] translationsParts = contentText.split(Pattern.quote(delimiter));

        if (translationsParts.length != 2) {
            throw new IOException("Formato risposta non valido: delimitatore '===TRANSLATIONS===' mancante o errato.");
        }

        String html = translationsParts[0].trim();
        String jsonText = translationsParts[1].trim();

        // 4. Salvataggio file
        Path htmlOut = Paths.get(originalHtmlFile.toString().replace(".html", "_translated.html"));
        Path jsonOut = Paths.get(originalHtmlFile.toString().replace(".html", "_labels.json"));

        Files.writeString(htmlOut, html);
        Files.writeString(jsonOut, jsonText);

        // salvataggio su mongo (using existing ChatGPTResponse POJO for compatibility)
        Gson gson = new Gson();
        JSONObject jsonWithoutId = new JSONObject(apiResponse);
        jsonWithoutId.remove("id");
        String sanitizedResponse = jsonWithoutId.toString();
        ChatGPTResponse geminiResponse = gson.fromJson(sanitizedResponse, ChatGPTResponse.class);
        if (geminiResponse != null) {
            geminiResponse.setPath(originalHtmlFile.toString());
            try {
                ObjectId oid = BaseDao.insertDocument(geminiResponse);
                if (oid != null) {
                    if (labelizerRequest.getChatGptResponses() == null) {
                        labelizerRequest.setChatGptResponses(new ArrayList<>());
                    }
                    labelizerRequest.getChatGptResponses().add(oid);
                }
            } catch (Exception ex) {
                LOGGER.error("Errore salvataggio su mongo: " + ex.getMessage());
                ex.printStackTrace();
            }
        }

        // Gemini response format for token usage
        int totalTokens = 0;
        if (json.has("usageMetadata")) {
            JSONObject usageMetadata = json.getJSONObject("usageMetadata");
            if (usageMetadata.has("totalTokenCount")) {
                totalTokens = usageMetadata.getInt("totalTokenCount");
            }
        }
        return totalTokens;
    }

    public static Route be_labelizer_export = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);

        String requestId = params.get("requestId");
        if (StringUtils.isBlank(requestId)) {
            response.status(HttpStatus.BAD_REQUEST_400);
            return "Request ID mancante.";
        }

        try {
            // Recupera la richiesta dal database
            LabelizerRequest labelizerRequest = BaseDao.getDocumentById(new ObjectId(requestId), LabelizerRequest.class);
            if (labelizerRequest == null) {
                response.status(HttpStatus.NOT_FOUND_404);
                return "Richiesta non trovata.";
            }

            // Crea la struttura JSON per MongoDB
            JSONArray mongoExport = new JSONArray();

            if (labelizerRequest.getFilePaths() != null) {
                for (String filePath : labelizerRequest.getFilePaths()) {
                    // Cerca il file JSON delle traduzioni
                    String jsonFilePath = filePath.replace(".html", "_labels.json");
                    Path jsonPath = Paths.get(jsonFilePath);

                    if (Files.exists(jsonPath)) {
                        try {
                            String jsonContent = Files.readString(jsonPath);
                            JSONArray translations = new JSONArray(jsonContent);

                            // Crea un documento per ogni traduzione
                            for (int i = 0; i < translations.length(); i++) {
                                JSONObject translationObj = translations.getJSONObject(i);
                                String label = translationObj.optString("label", "unknown_" + i);

                                JSONObject mongoDoc = new JSONObject();

                                // Creation and lastUpdate dates in MongoDB format
                                JSONObject creationDate = new JSONObject();
                                creationDate.put("$date", labelizerRequest.getStart().toInstant().toString());
                                mongoDoc.put("creation", creationDate);

                                JSONObject lastUpdateDate = new JSONObject();
                                lastUpdateDate.put("$date", labelizerRequest.getStart().toInstant().toString());
                                mongoDoc.put("lastUpdate", lastUpdateDate);

                                mongoDoc.put("key", label);

                                // Create items array with language translations
                                JSONArray items = new JSONArray();
                                for (String lang : labelizerRequest.getLanguages()) {
                                    String translation = translationObj.optString(lang, "");
                                    if (!translation.isEmpty()) {
                                        JSONObject item = new JSONObject();
                                        item.put("language", lang);
                                        item.put("description", translation);
                                        items.put(item);
                                    }
                                }
                                mongoDoc.put("items", items);

                                mongoExport.put(mongoDoc);
                            }
                        } catch (Exception e) {
                            LOGGER.warn("Errore lettura file JSON: " + jsonFilePath + " - " + e.getMessage());
                        }
                    }
                }
            }

            // Imposta headers per il download
            response.header("Content-Type", "application/json");
            response.header("Content-Disposition", "attachment; filename=\"translations_" + labelizerRequest.getName() + "_" + requestId + ".json\"");

            return mongoExport.toString(2); // Pretty print con indentazione

        } catch (Exception e) {
            LOGGER.error("Errore durante l'export: " + e.getMessage(), e);
            response.status(HttpStatus.INTERNAL_SERVER_ERROR_500);
            return "Errore durante l'export: " + e.getMessage();
        }
    };
}
