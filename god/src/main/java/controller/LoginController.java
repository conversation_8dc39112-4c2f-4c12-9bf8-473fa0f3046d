package controller;

import core.Core;
import core.Pages;
import core.Routes;
import dao.UserDao;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.TemplateViewRoute;
import utils.Defaults;
import utils.PasswordHash;
import utils.RequestUtils;
import utils.RoutesUtils;
import utils.UploadedFile;

/**
 *
 * <AUTHOR>
 */
public class LoginController {

    private static final Logger LOGGER = LoggerFactory.getLogger(LoginController.class.getName());

    public static TemplateViewRoute be_login = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);

        if (params.containsKey("wrongUsernamePassword")) {
            attributes.put("wrongUsernamePassword", true);
        }

        return Core.render(Pages.BE_LOGIN, attributes, request);
    };

    public static TemplateViewRoute be_dashboard = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes);
        if (user == null) {
            return Core.render(Pages.BE_LOGIN, attributes, request);
        }

        return Core.render(Pages.BE_DASHBOARD, attributes, request);
    };

    public static Route be_login_do = (Request request, Response response) -> {
        // TODO: fare parte di be_login -> LoginController con Manager.createSession e Manager.putSession
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);

        String username = params.get("username");
        String password = params.get("password");
        User user = UserDao.loadUserByUsername(username);
        if (user != null) {
            // check password
            if (isPasswordCorrect(user, password)) {

                String token = PasswordHash.getCookieSafeSecureRandom(32);
                Core.createSession(request, response, token);
                Core.addValueToSession(token, "user", user);

                response.redirect(RoutesUtils.contextPath(request) + Defaults.FIRST_PAGE);
                return null;
            }
        }
        response.redirect(RoutesUtils.contextPath(request) + Routes.BE_LOGIN + "?wrongUsernamePassword=true");
        return null;
    };

    public static TemplateViewRoute be_signup = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        return Core.render(Pages.BE_SIGNUP, attributes, request);
    };

    public static Route be_signup_save = (Request request, Response response) -> {
        // TODO: fare parte di be_login -> LoginController con Manager.createSession e Manager.putSession
//        User user = Core.getUserFromRequest(request);
//        if (user == null) {
//            response.redirect(request + Routes.BE_LOGIN);
//            return null;
//        }
//        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
//                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString()) &&
//                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.vendor.toString())) {
//            response.redirect(RouteUtils.contextPath(request) + Paths.HOME) ;
//            return Manager.renderEmpty();
//        }

        Map<String, UploadedFile> files = new HashMap<>();
        Map<String, String> params = new HashMap<>();
        RequestUtils.parseRequest(request, params, files);

        User newUser = RequestUtils.createFromParams(params, User.class);
        newUser.setActive(true);
        newUser.setPassword(PasswordHash.createHash(newUser.getPassword()));
        ObjectId insertedId = UserDao.insertDocument(newUser);

        if (insertedId != null) {
            response.redirect(RoutesUtils.contextPath(request) + Routes.BE_LOGIN);
            return true;
        } else {
            response.redirect(RoutesUtils.contextPath(request) + Routes.BE_SIGNUP);
            return false;
        }
    };

    public static TemplateViewRoute be_logout = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes);
        if (user != null) {
            Core.destroySession(request, response);
        }

        return Core.render(Pages.BE_LOGIN, attributes, request);
    };

    public static boolean isPasswordCorrect(User user, String password) {
        try {
            return PasswordHash.validatePassword(password, user.getPassword());
        } catch (NoSuchAlgorithmException | InvalidKeySpecException ex) {
            LOGGER.error("Unable to check user password", ex);
        }

        return false;
    }
}
