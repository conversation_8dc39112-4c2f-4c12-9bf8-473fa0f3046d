package controller;

import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import enums.LogType;
import enums.ProfileType;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Project;
import pojo.Server;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;
import utils.DateTimeUtils;
import utils.FileUtils;
import utils.RequestUtils;
import utils.RoutesUtils;
import utils.UploadedFile;

/**
 *
 * <AUTHOR>
 */
public class ProjectController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProjectController.class.getName());

    public static TemplateViewRoute be_project_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_PROJECT_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_project = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // logged user
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("projectId"));
        if (oid != null) {
            Project loadedProject = BaseDao.getDocumentById(oid, Project.class);
            attributes.put("project", loadedProject);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Project loadedProject = BaseDao.getDocumentByParentId(parentId, Project.class);
                if (loadedProject != null) {
                    attributes.put("project", loadedProject);
                }
            }
        }

        return Core.render(Pages.BE_PROJECT, attributes, request);
    };

    public static Route be_project_data = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Project> projectList;
        if (loadArchived) {
            projectList = BaseDao.getArchivedDocuments(Project.class);
        } else {
            projectList = BaseDao.getDocuments(Project.class);
        }

        StringBuilder json = new StringBuilder("{ \"data\": [");
        if (!projectList.isEmpty()) {
            for (Project project : projectList) {
                json.append("[");
                json.append("\"\",");
                json.append("\"<a projectId='").append(project.getId()).append("' href='").append(RoutesUtils.contextPath(request)).append(Routes.BE_PROJECT + "?projectId=").append(project.getId()).append("'>").append(project.getName()).append("</a>\",");
                // json.append("\"").append(project.getIp()).append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(project.getLastUpdate(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append(StringUtils.defaultIfBlank(project.getGitName(), "N.A.")).append("\",");
                json.append("\"<a projectId='").append(project.getId()).append("' class='btn btn-primary btn-create-script w-100' script='backup'>").append("CREA SCRIPT").append("</a><br/><a serverId='").append(project.getServerId()).append("' command='/opt/script/backup-mongo-storage-").append(project.getName().toLowerCase()).append(".sh' class='cursor-pointer text-center d-block btn-run-command'>Esegui Backup</a>\",");
                json.append("\"<a projectId='").append(project.getId()).append("' class='btn btn-secondary btn-create-script w-100' script='deploy'>").append("CREA SCRIPT").append("</a><br/><a serverId='").append(project.getServerId()).append("' command='/opt/script/deploy-").append(project.getName().toLowerCase()).append(".sh --war' class='cursor-pointer text-center d-block btn-run-command'>Deploy War</a><a serverId='").append(project.getServerId()).append("' command='/opt/script/deploy-").append(project.getName().toLowerCase()).append(".sh --static' class='cursor-pointer text-center d-block btn-run-command'>Deploy Static</a>\",");
                json.append("\"").append("Azioni").append("\",");
                json.append("\"").append("\""); // ultima colonna vuota
                json.append("],");
            }
            json.deleteCharAt(json.length() - 1); // rimuovo ultima virgola array
        }
        json.append("]}");

        return json.toString();
    };

    public static Route be_project_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("projectId"));
        Project newProject;
        if (oid != null) {
            newProject = BaseDao.getDocumentById(oid, Project.class);
            RequestUtils.mergeFromParams(params, newProject);
        } else {
            newProject = RequestUtils.createFromParams(params, Project.class);
        }

        if (newProject != null) {
            if (oid == null) {
                oid = BaseDao.insertDocument(newProject);
                newProject.setId(oid);

                BaseDao.insertLog(user, newProject, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newProject);
                BaseDao.insertLog(user, newProject, LogType.UPDATE);
            }

//            if (!files.isEmpty()) {
//                BaseDao.deleteImages(newProject, "imageIds");
//                BaseDao.saveImages(new ArrayList<>(files.values()), newProject, "imageIds");
//            }
        }

        // se errore ritorno Spark.halt()
        response.redirect(RoutesUtils.contextPath(request) + Routes.BE_PROJECT_COLLECTION);
        return oid;
    };

    public static Route be_project_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String projects = params.get("projectIds");
        String operation = params.get("operation");
        Boolean isArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("fromArchived")));
        if (StringUtils.isNotBlank(projects) && StringUtils.isNotBlank(operation)) {
            Map<ObjectId, String> projectIds = new HashMap<>();
            if (StringUtils.contains(projects, ",")) {
                List<String> ids = Arrays.asList(StringUtils.split(projects, ","));
                for (String id : ids) {
                    List<String> parts = Arrays.asList(StringUtils.split(id, "|"));
                    if (parts.size() == 2) {
                        projectIds.put(RequestUtils.toObjectId(parts.get(0)), parts.get(1));
                    }
                }
            } else {
                List<String> parts = Arrays.asList(StringUtils.split(projects, "|"));
                if (parts.size() == 2) {
                    projectIds.put(RequestUtils.toObjectId(parts.get(0)), parts.get(1));
                }
            }

            if (!projectIds.isEmpty()) {
                for (ObjectId projectId : projectIds.keySet()) {
                    Project projectToArchive;
                    if (isArchived) {
                        projectToArchive = BaseDao.getArchivedDocumentById(projectId, Project.class);
                    } else {
                        projectToArchive = BaseDao.getDocumentById(projectId, Project.class);
                    }
                    if (projectToArchive != null) {
                        if (StringUtils.equalsIgnoreCase(operation, "archive")) {
                            projectToArchive.setArchived(true);
                        } else if (StringUtils.equalsIgnoreCase(operation, "delete")) {
                            projectToArchive.setCancelled(true);
                        }
                        BaseDao.updateDocument(projectToArchive);
                    }
                }
            }
        }

        return "ok";
    };

    public static Route be_project_create_script = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId projectId = RequestUtils.toObjectId(params.get("projectId"));
        String script = params.get("script");
        if (projectId != null && StringUtils.isNotBlank(script)) {
            Project project = BaseDao.getDocumentById(projectId, Project.class);
            if (project != null) {
                if (project.getServerId() != null) {
                    Server server = BaseDao.getDocumentById(project.getServerId(), Server.class);
                    if (server != null) {
                        if (StringUtils.equalsIgnoreCase(script, "backup")) {
                            File file = FileUtils.getFileFromResource("public/be/scripts/backup-mongo-storage.sh");
                            if (file != null) {
                                String content = Files.readString(Paths.get(file.getPath()));
                                if (StringUtils.isNotBlank(content)) {
                                    try {
                                        createAndUploadBackupScript(project, content);
                                    } catch (Exception e) {
                                        throw Spark.halt(HttpStatus.INTERNAL_SERVER_ERROR_500, "Error creating backup script: " + e.getMessage());
                                    }
                                } else {
                                    throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Server not found");
                                }
                            }
                        } else if (StringUtils.equalsIgnoreCase(script, "deploy")) {
                            File file = FileUtils.getFileFromResource("public/be/scripts/deploy.sh");
                            if (file != null) {
                                String content = Files.readString(Paths.get(file.getPath()));
                                if (StringUtils.isNotBlank(content)) {
                                    /*
                                    DEPLOY:
                                        - {{projectName}}
                                        - {{gitName}}
                                        - {{tomcatUsername}}:{{tomcatPassword}}
                                     */
                                    content = StringUtils.replace(content, "{{projectName}}", project.getName().toLowerCase());
                                    content = StringUtils.replace(content, "{{gitName}}", project.getGitName());
                                    content = StringUtils.replace(content, "{{tomcatUsername}}", server.getTomcatUsername());
                                    content = StringUtils.replace(content, "{{tomcatPassword}}", server.getTomcatPassword());

                                    File fileToUpload = FileUtils.createTempFileWithContent(content);
                                    FileUtils.uploadFileToSFTP(fileToUpload, server.getIp(), StringUtils.defaultIfBlank(server.getUsernameFtp(), server.getUsername()), StringUtils.defaultIfBlank(server.getPasswordFtp(), server.getPassword()), 22, "/opt/script/deploy-" + project.getName().toLowerCase() + ".sh");
                                    ServerController.executeServerCommand(project.getServerId(), "chmod +x /opt/script/deploy-" + project.getName().toLowerCase() + ".sh");
                                    // sitemap + minifier
                                    FileUtils.uploadFileToSFTP(FileUtils.getFileFromResource("public/be/scripts/generate-sitemap.sh"), server.getIp(), StringUtils.defaultIfBlank(server.getUsernameFtp(), server.getUsername()), StringUtils.defaultIfBlank(server.getPasswordFtp(), server.getPassword()), 22, "/opt/script/generate-sitemap.sh");
                                    ServerController.executeServerCommand(project.getServerId(), "chmod +x /opt/script/generate-sitemap.sh");
                                    FileUtils.uploadFileToSFTP(FileUtils.getFileFromResource("public/be/scripts/html-minifier.conf"), server.getIp(), StringUtils.defaultIfBlank(server.getUsernameFtp(), server.getUsername()), StringUtils.defaultIfBlank(server.getPasswordFtp(), server.getPassword()), 22, "/opt/script/html-minifier.conf");
                                } else {
                                    throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Server not found");
                                }
                            }
                        }
                    } else {
                        throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Server not found");
                    }
                } else {
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400, "ServerId is null");
                }
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Project not found");
            }
        }

        return "ok";
    };

    /**
     * Create and upload backup script for a project
     * This method can be used both by the web interface and the scheduler
     *
     * @param project the project to create backup script for
     * @param content the backup script template content
     * @throws Exception if there's an error during script creation or upload
     */
    public static void createAndUploadBackupScript(Project project, String content) throws Exception {
        Server server = BaseDao.getDocumentById(project.getServerId(), Server.class);
        if (server == null) {
            throw new Exception("Server not found for project: " + project.getName());
        }

        /*
        BACKUP:
            - {{projectName}}
            - {{serverIp}}
            - {{serverUsername}}:{{serverPassword}}
         */
        content = StringUtils.replace(content, "{{projectName}}", project.getName().toLowerCase());
        content = StringUtils.replace(content, "{{serverIp}}", server.getIp());
        content = StringUtils.replace(content, "{{serverUsername}}", StringUtils.defaultIfBlank(server.getUsernameFtp(), server.getUsername()));
        content = StringUtils.replace(content, "{{serverPassword}}", StringUtils.defaultIfBlank(server.getPasswordFtp(), server.getPassword()));

        File fileToUpload = FileUtils.createTempFileWithContent(content);
        FileUtils.uploadFileToSFTP(fileToUpload, server.getIp(), StringUtils.defaultIfBlank(server.getUsernameFtp(), server.getUsername()), StringUtils.defaultIfBlank(server.getPasswordFtp(), server.getPassword()), 22, "/opt/script/backup-mongo-storage-" + project.getName().toLowerCase() + ".sh");
        ServerController.executeServerCommand(project.getServerId(), "chmod +x /opt/script/backup-mongo-storage-" + project.getName().toLowerCase() + ".sh");

        // sitemap + minifier
        FileUtils.uploadFileToSFTP(FileUtils.getFileFromResource("public/be/scripts/generate-sitemap.sh"), server.getIp(), StringUtils.defaultIfBlank(server.getUsernameFtp(), server.getUsername()), StringUtils.defaultIfBlank(server.getPasswordFtp(), server.getPassword()), 22, "/opt/script/generate-sitemap.sh");
        ServerController.executeServerCommand(project.getServerId(), "chmod +x /opt/script/generate-sitemap.sh");
        FileUtils.uploadFileToSFTP(FileUtils.getFileFromResource("public/be/scripts/html-minifier.conf"), server.getIp(), StringUtils.defaultIfBlank(server.getUsernameFtp(), server.getUsername()), StringUtils.defaultIfBlank(server.getPasswordFtp(), server.getPassword()), 22, "/opt/script/html-minifier.conf");
    }
}
