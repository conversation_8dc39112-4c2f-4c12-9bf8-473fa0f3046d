package controller;

import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import enums.LogType;
import enums.ProfileType;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.SSHSessionPool;
import pojo.Server;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.TemplateViewRoute;
import utils.DateTimeUtils;
import utils.RequestUtils;
import utils.RoutesUtils;
import utils.UploadedFile;

/**
 *
 * <AUTHOR>
 */
public class ServerController {

    private static final Map<ObjectId, SSHSessionPool> serverPools = new ConcurrentHashMap<>();

    private static final Logger LOGGER = LoggerFactory.getLogger(ServerController.class.getName());

    public static TemplateViewRoute be_server_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_SERVER_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_server = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // logged user
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("serverId"));
        if (oid != null) {
            Server loadedServer = BaseDao.getDocumentById(oid, Server.class);
            attributes.put("server", loadedServer);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Server loadedServer = BaseDao.getDocumentByParentId(parentId, Server.class);
                if (loadedServer != null) {
                    attributes.put("server", loadedServer);
                }
            }
        }

        return Core.render(Pages.BE_SERVER, attributes, request);
    };

    public static Route be_server_data = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Server> serverList;
        if (loadArchived) {
            serverList = BaseDao.getArchivedDocuments(Server.class);
        } else {
            serverList = BaseDao.getDocuments(Server.class);
        }

        StringBuilder json = new StringBuilder("{ \"data\": [");
        if (serverList != null && !serverList.isEmpty()) {
            for (Server server : serverList) {
                json.append("[");
                json.append("\"\",");
                json.append("\"<a serverId='").append(server.getId()).append("' href='").append(RoutesUtils.contextPath(request)).append(Routes.BE_SERVER + "?serverId=").append(server.getId()).append("'>").append(server.getName()).append("</a>\",");
                json.append("\"").append(server.getIp()).append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(server.getLastUpdate(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append("Azioni").append("\",");
                json.append("\"").append("\""); // ultima colonna vuota
                json.append("],");
            }
            json.deleteCharAt(json.length() - 1); // rimuovo ultima virgola array
        }
        json.append("]}");

        return json.toString();
    };

    public static Route be_server_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("serverId"));
        Server newServer;
        if (oid != null) {
            newServer = BaseDao.getDocumentById(oid, Server.class);
            RequestUtils.mergeFromParams(params, newServer);
        } else {
            newServer = RequestUtils.createFromParams(params, Server.class);
        }

        if (newServer != null) {
            if (oid == null) {
                oid = BaseDao.insertDocument(newServer);
                newServer.setId(oid);

                BaseDao.insertLog(user, newServer, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newServer);
                BaseDao.insertLog(user, newServer, LogType.UPDATE);
            }

//            if (!files.isEmpty()) {
//                BaseDao.deleteImages(newServer, "imageIds");
//                BaseDao.saveImages(new ArrayList<>(files.values()), newServer, "imageIds");
//            }
        }

        // se errore ritorno Spark.halt()
        response.redirect(RoutesUtils.contextPath(request) + Routes.BE_SERVER_COLLECTION);
        return oid;
    };

    public static Route be_server_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String servers = params.get("serverIds");
        String operation = params.get("operation");
        Boolean isArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("fromArchived")));
        if (StringUtils.isNotBlank(servers) && StringUtils.isNotBlank(operation)) {
            Map<ObjectId, String> serverIds = new HashMap<>();
            if (StringUtils.contains(servers, ",")) {
                List<String> ids = Arrays.asList(StringUtils.split(servers, ","));
                for (String id : ids) {
                    List<String> parts = Arrays.asList(StringUtils.split(id, "|"));
                    if (parts.size() == 2) {
                        serverIds.put(RequestUtils.toObjectId(parts.get(0)), parts.get(1));
                    }
                }
            } else {
                List<String> parts = Arrays.asList(StringUtils.split(servers, "|"));
                if (parts.size() == 2) {
                    serverIds.put(RequestUtils.toObjectId(parts.get(0)), parts.get(1));
                }
            }

            if (!serverIds.isEmpty()) {
                for (ObjectId serverId : serverIds.keySet()) {
                    Server serverToArchive;
                    if (isArchived) {
                        serverToArchive = BaseDao.getArchivedDocumentById(serverId, Server.class);
                    } else {
                        serverToArchive = BaseDao.getDocumentById(serverId, Server.class);
                    }
                    if (serverToArchive != null) {
                        if (StringUtils.equalsIgnoreCase(operation, "archive")) {
                            serverToArchive.setArchived(true);
                        } else if (StringUtils.equalsIgnoreCase(operation, "delete")) {
                            serverToArchive.setCancelled(true);
                        }
                        BaseDao.updateDocument(serverToArchive);
                    }
                }
            }
        }

        return "ok";
    };

    public static Route be_server_execute_command = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        ObjectId serverId = RequestUtils.toObjectId(params.get("serverId"));
        String command = params.get("command");

        return executeServerCommand(serverId, command);
    };
    
    public static String executeServerCommand(ObjectId serverId, String command) {
        if (serverId != null && StringUtils.isNotBlank(command)) {
            SSHSessionPool pool = serverPools.computeIfAbsent(serverId, k -> new SSHSessionPool());
            return pool.executeCommand(serverId, command);
        } else {
            return "ko";
        }
    }
}
