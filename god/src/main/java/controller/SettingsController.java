package controller;

import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import enums.ProfileType;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Settings;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.TemplateViewRoute;
import utils.Defaults;
import utils.RequestUtils;
import utils.RoutesUtils;
import utils.UploadedFile;

/**
 *
 * <AUTHOR>
 */
public class SettingsController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SettingsController.class.getName());

    public static TemplateViewRoute be_settings_languages = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Settings settings = BaseDao.getDocumentByClass(Settings.class);
        attributes.put("settings", settings);

        return Core.render(Pages.BE_SETTINGS_LANGUAGES, attributes, request);
    };

    public static Route be_settings_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        Settings settings = BaseDao.getDocumentByClass(Settings.class);
        boolean exists = settings != null;

        if (settings != null) {
            RequestUtils.mergeFromParams(params, settings);
        } else {
            settings = RequestUtils.createFromParams(params, Settings.class);
        }

        if (settings != null) {
            if (params.containsKey("languages")) {
                List<String> availableLanguages = new ArrayList<>(Arrays.asList(StringUtils.split(params.get("languages"), "|")));
                if (!availableLanguages.isEmpty()) {
                    settings.setAvailableLanguages(availableLanguages);
                    Defaults.AVAILABLE_LANGUAGES = availableLanguages;
                }
            }
            if (params.containsKey("visibleLanguages")) {
                List<String> visibleLanguages = new ArrayList<>(Arrays.asList(StringUtils.split(params.get("visibleLanguages"), "|")));
                if (!visibleLanguages.isEmpty()) {
                    settings.setVisibleLanguages(visibleLanguages);
                    Defaults.VISIBLE_LANGUAGES = visibleLanguages;
                }
            }

            List<String> visibleLanguages = settings.getVisibleLanguages();
            if (visibleLanguages != null) {
                Iterator<String> iterator = visibleLanguages.iterator();
                while (iterator.hasNext()) {
                    String visibleLanguage = iterator.next();
                    if (settings.getAvailableLanguages() == null || !settings.getAvailableLanguages().contains(visibleLanguage)) {
                        iterator.remove(); // Rimuovi l'elemento corrente dalla lista
                    }
                }
            }

            if (!exists) {
                BaseDao.insertDocument(settings);
            } else {
                BaseDao.updateDocument(settings);
            }
        }

        response.redirect(RoutesUtils.contextPath(request) + Routes.BE_SETTINGS_LANGUAGES);
        return null;
    };

}