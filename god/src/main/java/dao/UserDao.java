package dao;

import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Filters.or;
import static com.mongodb.client.model.Filters.regex;
import core.Core;
import pojo.User;
import java.security.InvalidParameterException;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;

/**
 *
 * <AUTHOR>
 */
public class UserDao extends BaseDao {
    
    public static User loadUserByUsername(String username) throws Exception {
        if (StringUtils.isBlank(username)) {
            throw new InvalidParameterException("empty username");
        }
        
        MongoCollection<Document> collection = Core.mongoDatabase.getCollection("user");
        Document doc = collection.find(and(or(eq("active", true), eq("profileType", "system")), ne("cancelled", true), regex("username", Pattern.compile("(\\s|^)"+ username +"(\\s|$)", Pattern.CASE_INSENSITIVE)))).first();
        
        User user = null;
        if (doc != null) {
            user = Core.fromDocument(doc, User.class);
        }
        return user;
    }
}
