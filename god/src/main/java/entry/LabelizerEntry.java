package entry;

import pojo.ChatGPTResponse;
import pojo.LabelizerRequest;

import java.util.List;
import java.util.Map;

public class LabelizerEntry {

    private LabelizerRequest request;
    private List<ChatGPTResponse> responses;
    private Integer totalTokens;
    private Map<String, ChatGPTResponse> responsesMap;
    private String executionTime;

    public LabelizerRequest getRequest() {
        return request;
    }

    public void setRequest(LabelizerRequest request) {
        this.request = request;
    }

    public List<ChatGPTResponse> getResponses() {
        return responses;
    }

    public void setResponses(List<ChatGPTResponse> responses) {
        this.responses = responses;
    }

    public Integer getTotalTokens() {
        return totalTokens;
    }

    public void setTotalTokens(Integer totalTokens) {
        this.totalTokens = totalTokens;
    }

    public Map<String, ChatGPTResponse> getResponsesMap() {
        return responsesMap;
    }

    public void setResponsesMap(Map<String, ChatGPTResponse> responsesMap) {
        this.responsesMap = responsesMap;
    }

    public String getExecutionTime() {
        return executionTime;
    }

    public void setExecutionTime(String executionTime) {
        this.executionTime = executionTime;
    }
}
