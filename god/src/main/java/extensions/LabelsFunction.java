package extensions;

import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import dao.BaseDao;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Label;
import pojo.LabelItem;
import utils.Defaults;

/**
 *
 * <AUTHOR>
 */
public class LabelsFunction implements Function {

    private static final Logger LOGGER = LoggerFactory.getLogger(LabelsFunction.class.getName());

    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("key");
        names.add("language");
        return names;
    }

    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {

        String key = (String) args.get("key");
        String language = (String) args.get("language");

        // language
        if (StringUtils.isBlank(language)) {
            language = (String) context.getVariable("language");
        }

        String localizedKey = key;
        if (Defaults.AVAILABLE_LANGUAGES.size() > 1) {
            localizedKey += "_" + language;
        }
        localizedKey = StringUtils.upperCase(localizedKey);

        Map<String, String> cacheLabels = labels();
        if (cacheLabels != null) {
            String label = labels().get(localizedKey);
            return label;
        } else {
            return "";
        }
    }

    private static Map<String, String> labels;

    private static Map<String, String> labels() {
        if (labels == null) {
            labels = new HashMap<>();
            try {
                List<Label> databaseLabel = BaseDao.getDocuments(Label.class);

                if (databaseLabel != null && !databaseLabel.isEmpty()) {
                    for (Label label : databaseLabel) {
                        if (label.getItems() != null && !label.getItems().isEmpty()) {
                            for (LabelItem labelInLanguage : label.getItems()) {
                                if (Defaults.AVAILABLE_LANGUAGES.size() == 1) {
                                    // niente prefisso della lingua
                                    if (label.getItems().size() == 1) {
                                        labels.put(label.getKey(), labelInLanguage.getDescription());
                                    }
                                } else {
                                    labels.put(StringUtils.upperCase(label.getKey() + "_" + labelInLanguage.getLanguage()), labelInLanguage.getDescription());
                                }
                            }
                        }
                    }
                }
            } catch (Exception ex) {
                LOGGER.error("unable to load labels", ex);
            }
        }

        return labels;
    }
}
