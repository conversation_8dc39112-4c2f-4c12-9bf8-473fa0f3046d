package extensions;

import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import dao.BaseDao;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class TagListFunction implements Function {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("table");
        names.add("language");
        return names;
    }

    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        String table = (String) args.get("table");
        String language = (String) args.get("language");

        return loadTagList(table, language);
    }

    private List<String> loadTagList(String table, String language) {
        List<String> items = null;
        try {
            items = BaseDao.loadTagList(Class.forName("pojo." + table), language);
        } catch (Exception ex) {
            logger.error("suppressed", ex);
        }
        return items;
    }
}
