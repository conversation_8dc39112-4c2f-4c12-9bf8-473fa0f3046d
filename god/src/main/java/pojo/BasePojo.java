package pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import utils.Defaults;

/**
 *
 * <AUTHOR>
 */
public class BasePojo {

    // oid
    @JsonProperty("_id") // serve per mongo
    private ObjectId id;

    private String language;
    private List<String> availableLanguages;    // lista di lingue in cui l'entità è presente. NB: il campo è duplicato per tutte le lingue
    private String parentId;                    // random UUID usato per identificare lo stesso prodotto in altre lingue

    private Date creation;
    private Date lastUpdate;

    private Boolean archived;
    private Boolean cancelled;

    public ObjectId getId() {
        return id;
    }

    public void setId(ObjectId id) {
        this.id = id;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public List<String> getAvailableLanguages() {
        if (availableLanguages == null) {
            return null;
        }
        List<String> filteredLanguages = new ArrayList<>();
        for (String availableLanguage : availableLanguages) {
            if (Defaults.AVAILABLE_LANGUAGES.contains(availableLanguage)) {
                filteredLanguages.add(availableLanguage);
            }
        }
        return filteredLanguages;
    }

    // used for settings
    public List<String> getAvailableLanguages(boolean skipChecks) {
        if (skipChecks) {
            return availableLanguages;
        } else {
            return getAvailableLanguages();
        }
    }

    public void setAvailableLanguages(List<String> availableLanguages) {
        this.availableLanguages = availableLanguages;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public Date getCreation() {
        return creation;
    }

    public void setCreation(Date creation) {
        this.creation = creation;
    }

    public Date getLastUpdate() {
        return lastUpdate;
    }

    public void setLastUpdate(Date lastUpdate) {
        this.lastUpdate = lastUpdate;
    }

    public Boolean getArchived() {
        return archived;
    }

    public void setArchived(Boolean archived) {
        this.archived = archived;
    }

    public Boolean getCancelled() {
        return cancelled;
    }

    public void setCancelled(Boolean cancelled) {
        this.cancelled = cancelled;
    }
}
