package pojo;

import java.util.List;

public class ChatGPTResponse extends BasePojo {

    private String object;
    private long created;
    private String model;
    private List<ChatGPTResponseChoice> choices;
    private ChatGPTResponseUsage usage;
    private String service_tier;
    private String system_fingerprint;

    // extra fields
    private String path;

    public String getObject() {
        return object;
    }

    public void setObject(String object) {
        this.object = object;
    }

    public long getCreated() {
        return created;
    }

    public void setCreated(long created) {
        this.created = created;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public List<ChatGPTResponseChoice> getChoices() {
        return choices;
    }

    public void setChoices(List<ChatGPTResponseChoice> choices) {
        this.choices = choices;
    }

    public ChatGPTResponseUsage getUsage() {
        return usage;
    }

    public void setUsage(ChatGPTResponseUsage usage) {
        this.usage = usage;
    }

    public String getService_tier() {
        return service_tier;
    }

    public void setService_tier(String service_tier) {
        this.service_tier = service_tier;
    }

    public String getSystem_fingerprint() {
        return system_fingerprint;
    }

    public void setSystem_fingerprint(String system_fingerprint) {
        this.system_fingerprint = system_fingerprint;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }
}
