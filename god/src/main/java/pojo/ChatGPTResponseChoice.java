package pojo;

public class ChatGPTResponseChoice {
    private int index;
    private ChatGPTResponseMessage message;
    private Object logprobs;
    private String finish_reason;

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public ChatGPTResponseMessage getMessage() {
        return message;
    }

    public void setMessage(ChatGPTResponseMessage message) {
        this.message = message;
    }

    public Object getLogprobs() {
        return logprobs;
    }

    public void setLogprobs(Object logprobs) {
        this.logprobs = logprobs;
    }

    public String getFinish_reason() {
        return finish_reason;
    }

    public void setFinish_reason(String finish_reason) {
        this.finish_reason = finish_reason;
    }
}
