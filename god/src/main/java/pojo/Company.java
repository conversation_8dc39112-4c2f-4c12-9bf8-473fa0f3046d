package pojo;

import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class Company extends BasePojo {

    private String fullname;
    private String phoneNumber;
    private String email;
    private String tin;
    private String vatNumber;
    private ObjectId logoImageId;

    public String getFullname() {
        return fullname;
    }

    public void setFullname(String fullname) {
        this.fullname = fullname;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTin() {
        return tin;
    }

    public void setTin(String tin) {
        this.tin = tin;
    }

    public String getVatNumber() {
        return vatNumber;
    }

    public void setVatNumber(String vatNumber) {
        this.vatNumber = vatNumber;
    }

    public ObjectId getLogoImageId() {
        return logoImageId;
    }

    public void setLogoImageId(ObjectId logoImageId) {
        this.logoImageId = logoImageId;
    }
}
