package pojo;

import java.util.Map;
import java.util.UUID;

public class ExtractedText {
    private String id;
    private String originalText;
    private Map<String, String> translations;

    public ExtractedText(String originalText) {
        this.id = UUID.randomUUID().toString();
        this.originalText = originalText;
        this.translations = null;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOriginalText() {
        return originalText;
    }

    public void setOriginalText(String originalText) {
        this.originalText = originalText;
    }

    public Map<String, String> getTranslations() {
        return translations;
    }

    public void setTranslations(Map<String, String> translations) {
        this.translations = translations;
    }
}
