package pojo;

import org.bson.types.ObjectId;

import java.util.Date;
import java.util.List;

public class LabelizerRequest extends BasePojo {

    private String name;
    private String path;
    private List<String> languages;
    private Date start, end;
    private List<ObjectId> chatGptResponses;
    private List<String> filePaths;
    private Integer totalFiles;
    private Integer processedFiles;
    private String currentFile;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public List<String> getLanguages() {
        return languages;
    }

    public void setLanguages(List<String> languages) {
        this.languages = languages;
    }

    public Date getStart() {
        return start;
    }

    public void setStart(Date start) {
        this.start = start;
    }

    public Date getEnd() {
        return end;
    }

    public void setEnd(Date end) {
        this.end = end;
    }

    public List<ObjectId> getChatGptResponses() {
        return chatGptResponses;
    }

    public void setChatGptResponses(List<ObjectId> chatGptResponses) {
        this.chatGptResponses = chatGptResponses;
    }

    public List<String> getFilePaths() {
        return filePaths;
    }

    public void setFilePaths(List<String> filePaths) {
        this.filePaths = filePaths;
    }

    public Integer getTotalFiles() {
        return totalFiles;
    }

    public void setTotalFiles(Integer totalFiles) {
        this.totalFiles = totalFiles;
    }

    public Integer getProcessedFiles() {
        return processedFiles;
    }

    public void setProcessedFiles(Integer processedFiles) {
        this.processedFiles = processedFiles;
    }

    public String getCurrentFile() {
        return currentFile;
    }

    public void setCurrentFile(String currentFile) {
        this.currentFile = currentFile;
    }
}
