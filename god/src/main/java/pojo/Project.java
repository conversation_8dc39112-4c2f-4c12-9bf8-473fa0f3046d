package pojo;

import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class Project extends BasePojo {

    private String name;
    private ObjectId serverId;
    private String gitName;                         // serve per script deploy

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public ObjectId getServerId() {
        return serverId;
    }

    public void setServerId(ObjectId serverId) {
        this.serverId = serverId;
    }

    public String getGitName() {
        return gitName;
    }

    public void setGitName(String gitName) {
        this.gitName = gitName;
    }
}
