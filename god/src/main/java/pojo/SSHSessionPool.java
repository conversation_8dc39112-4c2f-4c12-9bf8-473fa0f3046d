package pojo;

import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelExec;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import dao.BaseDao;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.Map;
import java.util.Queue;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class SSHSessionPool {

    private static final int SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutes in milliseconds

    private final Queue<Session> sessionPool = new LinkedList<>();
    private final Map<Session, Long> sessionTimestamps = new HashMap<>();

    // Execute a command using an SSH session
    public String executeCommand(ObjectId serverId, String command) {
        Session session = getSession(serverId);

        StringBuilder output = new StringBuilder();
        try {
            // Open a channel for executing commands
            Channel channel = session.openChannel("exec");
            // Set the command to execute
            ((ChannelExec) channel).setCommand(command);
            // Get the input stream to read the command's output
            InputStream inputStream = channel.getInputStream();
            // Connect to the channel
            channel.connect();

            // Read the output
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                output.append(new String(buffer, 0, bytesRead));
            }

            // Mark the session timestamp after the command execution
            sessionTimestamps.put(session, System.currentTimeMillis());

            channel.disconnect();
        } catch (JSchException | IOException e) {
            e.printStackTrace();
            return "Error: " + e.getMessage();
        }

        return output.toString();
    }

    // Get an existing session from the pool or create a new one if none exists
    private synchronized Session getSession(ObjectId serverId) {
        cleanUpSessions();

        Session session;
        if (!sessionPool.isEmpty()) {
            session = sessionPool.poll();
        } else {
            session = createNewSession(serverId);
        }

        return session;
    }

    // Create a new SSH session
    private Session createNewSession(ObjectId serverId) {
        Session session = null;
        try {
            Server server = BaseDao.getDocumentById(serverId, Server.class);

            if (server != null) {
                JSch jsch = new JSch();
                session = jsch.getSession(server.getUsername(), server.getIp(), 22);
                session.setPassword(server.getPassword());
                session.setConfig("StrictHostKeyChecking", "no");
                session.connect();
                sessionTimestamps.put(session, System.currentTimeMillis());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return session;
    }

    // Clean up expired sessions from the pool (sessions that are idle for more than 30 minutes)
    private synchronized void cleanUpSessions() {
        long currentTime = System.currentTimeMillis();
        Iterator<Map.Entry<Session, Long>> iterator = sessionTimestamps.entrySet().iterator();

        while (iterator.hasNext()) {
            Map.Entry<Session, Long> entry = iterator.next();
            long lastUsedTime = entry.getValue();
            if (currentTime - lastUsedTime > SESSION_TIMEOUT) {
                Session session = entry.getKey();
                if (session.isConnected()) {
                    session.disconnect();
                }
                iterator.remove();
            }
        }
    }

    // Put the session back into the pool
    public synchronized void returnSession(Session session) {
        if (session.isConnected()) {
            sessionPool.offer(session);
        } else {
            session.disconnect();
        }
    }
}
