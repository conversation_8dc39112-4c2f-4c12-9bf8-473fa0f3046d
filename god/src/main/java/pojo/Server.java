package pojo;

/**
 *
 * <AUTHOR>
 */
public class Server extends BasePojo {

    private String name;
    private String ip;
    private String username, password;
    private String usernameFtp, passwordFtp;
    private String tomcatUsername, tomcatPassword;  // serve per script deploy

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getUsernameFtp() {
        return usernameFtp;
    }

    public void setUsernameFtp(String usernameFtp) {
        this.usernameFtp = usernameFtp;
    }

    public String getPasswordFtp() {
        return passwordFtp;
    }

    public void setPasswordFtp(String passwordFtp) {
        this.passwordFtp = passwordFtp;
    }

    public String getTomcatUsername() {
        return tomcatUsername;
    }

    public void setTomcatUsername(String tomcatUsername) {
        this.tomcatUsername = tomcatUsername;
    }

    public String getTomcatPassword() {
        return tomcatPassword;
    }

    public void setTomcatPassword(String tomcatPassword) {
        this.tomcatPassword = tomcatPassword;
    }
}
