package pojo;

import java.util.List;
import java.util.Map;

/**
 * Contains the result of text extraction from an HTML file
 */
public class TextExtractionResult {
    private String originalHtml;
    private List<ExtractedText> extractedTexts;
    private String processedHtml;

    public TextExtractionResult(String originalHtml) {
        this.originalHtml = originalHtml;
    }

    public String getOriginalHtml() {
        return originalHtml;
    }

    public void setOriginalHtml(String originalHtml) {
        this.originalHtml = originalHtml;
    }

    public List<ExtractedText> getExtractedTexts() {
        return extractedTexts;
    }

    public void setExtractedTexts(List<ExtractedText> extractedTexts) {
        this.extractedTexts = extractedTexts;
    }

    public String getProcessedHtml() {
        return processedHtml;
    }

    public void setProcessedHtml(String processedHtml) {
        this.processedHtml = processedHtml;
    }
}
