package pojo;

import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class User extends BasePojo {

    // basic info
    private String email;
    
    // profile
    private String name;
    private String username;
    private String password;
    private String profileType;         // unconfirmed, standard, vendor, head, admin, system
    private String companyCode;
    private ObjectId imageId;
    
    private Boolean active;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getProfileType() {
        return profileType;
    }

    public void setProfileType(String profileType) {
        this.profileType = profileType;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public Boolean getActive() {
        return active;
    }

    public ObjectId getImageId() {
        return imageId;
    }

    public void setImageId(ObjectId imageId) {
        this.imageId = imageId;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }
}
