package utils;

import controller.ProjectController;
import controller.ServerController;
import dao.BaseDao;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Project;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Scheduler for automated daily backups of all projects
 * Only runs in non-local environments (VPS)
 * 
 * <AUTHOR>
 */
public class BackupScheduler {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(BackupScheduler.class.getName());
    private static ScheduledExecutorService scheduler;
    private static boolean initialized = false;
    
    /**
     * Initialize the backup scheduler
     * Only runs in non-local environments
     */
    public static void initialize() {
        // Only initialize once
        if (initialized) {
            return;
        }
        
        // Only run in non-local environments (VPS)
        if (EnvironmentUtils.isNotLocal()) {
            LOGGER.info("Initializing backup scheduler for non-local environment");
            
            // Create a scheduler with a single thread
            scheduler = Executors.newScheduledThreadPool(1);
            
            // Schedule the backup task to run daily at 2:00 AM
            // Calculate initial delay to next 2:00 AM
            long initialDelay = calculateInitialDelay();
            
            // Schedule the task
            scheduler.scheduleAtFixedRate(
                BackupScheduler::runBackupForAllProjects,
                initialDelay,
                24 * 60 * 60, // 24 hours
                TimeUnit.SECONDS
            );
            
            initialized = true;
            LOGGER.info("Backup scheduler initialized. First backup will run in {} seconds", initialDelay);
        } else {
            LOGGER.info("Backup scheduler not initialized - running in local environment");
        }
    }
    
    /**
     * Calculate the initial delay in seconds until the next 2:00 AM
     * 
     * @return seconds until next 2:00 AM
     */
    private static long calculateInitialDelay() {
        // Get current time in seconds since midnight
        long currentTimeSeconds = System.currentTimeMillis() / 1000;
        
        // Calculate seconds since midnight
        long secondsSinceMidnight = currentTimeSeconds % (24 * 60 * 60);
        
        // Target time is 2:00 AM (2 * 60 * 60 seconds since midnight)
        long targetTime = 2 * 60 * 60;
        
        // Calculate delay
        long delay = targetTime - secondsSinceMidnight;
        
        // If it's already past 2:00 AM, schedule for next day
        if (delay < 0) {
            delay += 24 * 60 * 60;
        }
        
        return delay;
    }
    
    /**
     * Run backup for all projects
     * This method is called by the scheduler
     */
    public static void runBackupForAllProjects() {
        try {
            LOGGER.info("Starting scheduled backup for all projects");
            
            // Get all active projects
            List<Project> projects = BaseDao.getDocuments(Project.class);
            
            if (projects == null || projects.isEmpty()) {
                LOGGER.info("No projects found for backup");
                return;
            }
            
            LOGGER.info("Found {} projects to backup", projects.size());
            
            // Process each project
            for (Project project : projects) {
                try {
                    if (project.getServerId() != null) {
                        LOGGER.info("Processing backup for project: {}", project.getName());
                        
                        // First create the backup script (to ensure it exists and is up-to-date)
                        createBackupScript(project);
                        
                        // Then execute the backup
                        executeBackup(project);
                        
                        LOGGER.info("Backup completed for project: {}", project.getName());
                    } else {
                        LOGGER.warn("Skipping backup for project {} - no server ID", project.getName());
                    }
                } catch (Exception e) {
                    LOGGER.error("Error during backup for project {}: {}", project.getName(), e.getMessage(), e);
                }
            }
            
            LOGGER.info("Scheduled backup completed for all projects");
        } catch (Exception e) {
            LOGGER.error("Error during scheduled backup: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Create backup script for a project
     * 
     * @param project the project to create backup script for
     */
    private static void createBackupScript(Project project) {
        try {
            LOGGER.info("Creating backup script for project: {}", project.getName());
            
            // Get the backup script template
            File file = FileUtils.getFileFromResource("public/be/scripts/backup-mongo-storage.sh");
            if (file != null) {
                String content = Files.readString(Paths.get(file.getPath()));
                if (StringUtils.isNotBlank(content)) {
                    // Create and upload the script
                    ProjectController.createAndUploadBackupScript(project, content);
                    LOGGER.info("Backup script created for project: {}", project.getName());
                } else {
                    LOGGER.error("Backup script template is empty for project: {}", project.getName());
                }
            } else {
                LOGGER.error("Backup script template not found for project: {}", project.getName());
            }
        } catch (Exception e) {
            LOGGER.error("Error creating backup script for project {}: {}", project.getName(), e.getMessage(), e);
        }
    }
    
    /**
     * Execute backup for a project
     * 
     * @param project the project to backup
     */
    private static void executeBackup(Project project) {
        try {
            LOGGER.info("Executing backup for project: {}", project.getName());
            
            // Execute the backup command
            String command = "/opt/script/backup-mongo-storage-" + project.getName().toLowerCase() + ".sh";
            String result = ServerController.executeServerCommand(project.getServerId(), command);
            
            if (StringUtils.equalsIgnoreCase(result, "ko")) {
                LOGGER.error("Backup execution failed for project: {}", project.getName());
            } else {
                LOGGER.info("Backup execution successful for project: {}", project.getName());
            }
        } catch (Exception e) {
            LOGGER.error("Error executing backup for project {}: {}", project.getName(), e.getMessage(), e);
        }
    }
    
    /**
     * Shutdown the scheduler
     */
    public static void shutdown() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            initialized = false;
            LOGGER.info("Backup scheduler shutdown");
        }
    }
}
