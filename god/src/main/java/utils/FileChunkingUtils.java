package utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Utility class for chunking large HTML files to handle Gemini API token limits.
 * Provides smart chunking that preserves HTML structure and doesn't break in the middle of tags.
 */
public class FileChunkingUtils {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(FileChunkingUtils.class);
    
    // Token estimation constants
    private static final int CHARS_PER_TOKEN = 4; // Approximate ratio for token estimation
    private static final int DEFAULT_MAX_CHUNK_SIZE_TOKENS = 800000; // Safe chunk size with margin
    private static final int DEFAULT_MAX_CHUNK_SIZE_CHARS = DEFAULT_MAX_CHUNK_SIZE_TOKENS * CHARS_PER_TOKEN;
    
    // HTML tag patterns for smart chunking
    private static final Pattern HTML_TAG_PATTERN = Pattern.compile("<[^>]+>");
    private static final Pattern BLOCK_ELEMENT_PATTERN = Pattern.compile(
        "<(div|p|h[1-6]|section|article|header|footer|main|nav|aside|ul|ol|li|table|tr|td|th|form)[^>]*>",
        Pattern.CASE_INSENSITIVE
    );
    
    /**
     * Represents a chunk of HTML content with metadata
     */
    public static class HtmlChunk {
        private final String content;
        private final int chunkIndex;
        private final int totalChunks;
        private final int estimatedTokens;
        
        public HtmlChunk(String content, int chunkIndex, int totalChunks, int estimatedTokens) {
            this.content = content;
            this.chunkIndex = chunkIndex;
            this.totalChunks = totalChunks;
            this.estimatedTokens = estimatedTokens;
        }
        
        public String getContent() { return content; }
        public int getChunkIndex() { return chunkIndex; }
        public int getTotalChunks() { return totalChunks; }
        public int getEstimatedTokens() { return estimatedTokens; }
        
        public String getChunkIdentifier() {
            return String.format("chunk_%d_of_%d", chunkIndex + 1, totalChunks);
        }
    }
    
    /**
     * Estimates the number of tokens in a text string
     */
    public static int estimateTokens(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }
        return Math.max(1, text.length() / CHARS_PER_TOKEN);
    }
    
    /**
     * Checks if content needs to be chunked based on token estimation
     */
    public static boolean needsChunking(String content) {
        return needsChunking(content, DEFAULT_MAX_CHUNK_SIZE_TOKENS);
    }
    
    /**
     * Checks if content needs to be chunked based on token estimation
     */
    public static boolean needsChunking(String content, int maxTokens) {
        int estimatedTokens = estimateTokens(content);
        boolean needs = estimatedTokens > maxTokens;
        
        if (needs) {
            LOGGER.info("Content needs chunking: estimated {} tokens, max allowed: {}", 
                       estimatedTokens, maxTokens);
        }
        
        return needs;
    }
    
    /**
     * Chunks HTML content into smaller pieces while preserving structure
     */
    public static List<HtmlChunk> chunkHtmlContent(String htmlContent) {
        return chunkHtmlContent(htmlContent, DEFAULT_MAX_CHUNK_SIZE_CHARS);
    }
    
    /**
     * Chunks HTML content into smaller pieces while preserving structure
     */
    public static List<HtmlChunk> chunkHtmlContent(String htmlContent, int maxChunkSizeChars) {
        if (htmlContent == null || htmlContent.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<HtmlChunk> chunks = new ArrayList<>();
        
        // If content is small enough, return as single chunk
        if (htmlContent.length() <= maxChunkSizeChars) {
            chunks.add(new HtmlChunk(htmlContent, 0, 1, estimateTokens(htmlContent)));
            return chunks;
        }
        
        LOGGER.info("Chunking HTML content of {} characters into chunks of max {} characters", 
                   htmlContent.length(), maxChunkSizeChars);
        
        // Find good split points (block elements)
        List<Integer> splitPoints = findGoodSplitPoints(htmlContent, maxChunkSizeChars);
        
        // Create chunks
        int startIndex = 0;
        for (int i = 0; i < splitPoints.size(); i++) {
            int endIndex = splitPoints.get(i);
            String chunkContent = htmlContent.substring(startIndex, endIndex);
            
            // Ensure chunk has proper HTML structure
            chunkContent = ensureValidHtmlChunk(chunkContent);
            
            chunks.add(new HtmlChunk(
                chunkContent, 
                i, 
                splitPoints.size(), 
                estimateTokens(chunkContent)
            ));
            
            startIndex = endIndex;
        }
        
        // Add final chunk if there's remaining content
        if (startIndex < htmlContent.length()) {
            String finalChunk = htmlContent.substring(startIndex);
            finalChunk = ensureValidHtmlChunk(finalChunk);
            
            chunks.add(new HtmlChunk(
                finalChunk, 
                chunks.size(), 
                chunks.size() + 1, 
                estimateTokens(finalChunk)
            ));
        }
        
        // Update total chunks count for all chunks
        int totalChunks = chunks.size();
        List<HtmlChunk> updatedChunks = new ArrayList<>();
        for (int i = 0; i < chunks.size(); i++) {
            HtmlChunk oldChunk = chunks.get(i);
            updatedChunks.add(new HtmlChunk(
                oldChunk.getContent(), 
                i, 
                totalChunks, 
                oldChunk.getEstimatedTokens()
            ));
        }
        
        LOGGER.info("Created {} chunks from HTML content", totalChunks);
        return updatedChunks;
    }
    
    /**
     * Finds good split points in HTML content, preferring block element boundaries
     */
    private static List<Integer> findGoodSplitPoints(String htmlContent, int maxChunkSize) {
        List<Integer> splitPoints = new ArrayList<>();
        int currentPosition = 0;
        
        while (currentPosition < htmlContent.length()) {
            int targetEnd = Math.min(currentPosition + maxChunkSize, htmlContent.length());
            
            // Try to find a good split point near the target end
            int splitPoint = findBestSplitPoint(htmlContent, currentPosition, targetEnd);
            
            if (splitPoint > currentPosition) {
                splitPoints.add(splitPoint);
                currentPosition = splitPoint;
            } else {
                // Fallback: split at target end even if not ideal
                splitPoints.add(targetEnd);
                currentPosition = targetEnd;
            }
        }
        
        return splitPoints;
    }
    
    /**
     * Finds the best split point between start and target end positions
     */
    private static int findBestSplitPoint(String content, int start, int targetEnd) {
        // Look for block element boundaries within a reasonable range before target end
        int searchStart = Math.max(start, targetEnd - 1000); // Look back up to 1000 chars
        
        Matcher matcher = BLOCK_ELEMENT_PATTERN.matcher(content);
        int bestSplit = -1;
        
        // Find the last block element before target end
        while (matcher.find() && matcher.start() >= searchStart && matcher.start() < targetEnd) {
            bestSplit = matcher.end();
        }
        
        if (bestSplit > start) {
            return bestSplit;
        }
        
        // Fallback: look for any tag boundary
        matcher = HTML_TAG_PATTERN.matcher(content);
        while (matcher.find() && matcher.start() >= searchStart && matcher.start() < targetEnd) {
            bestSplit = matcher.end();
        }
        
        if (bestSplit > start) {
            return bestSplit;
        }
        
        // Last resort: split at target end
        return targetEnd;
    }
    
    /**
     * Ensures a chunk has valid HTML structure by adding necessary opening/closing tags
     */
    private static String ensureValidHtmlChunk(String chunk) {
        // For now, return as-is. In a more sophisticated implementation,
        // we could analyze unclosed tags and add proper opening/closing tags
        // to ensure each chunk is valid HTML.
        return chunk.trim();
    }
    
    /**
     * Merges translation results from multiple chunks back into a single result
     */
    public static String mergeChunkResults(List<String> chunkResults) {
        if (chunkResults == null || chunkResults.isEmpty()) {
            return "";
        }
        
        if (chunkResults.size() == 1) {
            return chunkResults.get(0);
        }
        
        StringBuilder mergedResult = new StringBuilder();
        
        for (int i = 0; i < chunkResults.size(); i++) {
            String chunkResult = chunkResults.get(i);
            
            if (chunkResult != null && !chunkResult.trim().isEmpty()) {
                // Parse each chunk result and extract the parts
                try {
                    // Split by delimiter to get HTML and JSON parts
                    String delimiter = "===TRANSLATIONS===";
                    String[] parts = chunkResult.split(Pattern.quote(delimiter));
                    
                    if (parts.length >= 2) {
                        String htmlPart = parts[0].trim();
                        String jsonPart = parts[1].trim();
                        
                        // Append HTML part
                        if (!htmlPart.isEmpty()) {
                            mergedResult.append(htmlPart);
                            if (i < chunkResults.size() - 1) {
                                mergedResult.append("\n");
                            }
                        }
                        
                        // For JSON parts, we'll need to merge them properly
                        // This is a simplified approach - in production you might want
                        // more sophisticated JSON merging
                        if (i == 0) {
                            mergedResult.append("\n").append(delimiter).append("\n");
                            mergedResult.append(jsonPart);
                        } else {
                            // Append additional JSON entries (remove array brackets and merge)
                            String cleanJsonPart = jsonPart.trim();
                            if (cleanJsonPart.startsWith("[") && cleanJsonPart.endsWith("]")) {
                                cleanJsonPart = cleanJsonPart.substring(1, cleanJsonPart.length() - 1).trim();
                                if (!cleanJsonPart.isEmpty()) {
                                    mergedResult.append(",\n").append(cleanJsonPart);
                                }
                            }
                        }
                    } else {
                        // Fallback: just append the chunk result
                        mergedResult.append(chunkResult);
                        if (i < chunkResults.size() - 1) {
                            mergedResult.append("\n");
                        }
                    }
                } catch (Exception e) {
                    LOGGER.warn("Error merging chunk result {}: {}", i, e.getMessage());
                    // Fallback: just append the chunk result
                    mergedResult.append(chunkResult);
                    if (i < chunkResults.size() - 1) {
                        mergedResult.append("\n");
                    }
                }
            }
        }
        
        // Close the JSON array if we have JSON content
        if (mergedResult.toString().contains("===TRANSLATIONS===")) {
            mergedResult.append("\n]");
        }
        
        return mergedResult.toString();
    }
}
