package utils;

import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.StandardOpenOption;
import java.security.InvalidParameterException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class FileUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(FileUtils.class.getName());
    public static final String ROOT = "/opt/" + Defaults.PROJECT_NAME + "/storage";

    public enum FileType {
        img(ROOT + "/img");

        private final String path;

        private FileType(String path) {
            this.path = path;
        }

        public String getPath() {
            return path;
        }
    }

    public static String composeFilename(FileType type, String filename, String extension) {
        if (type == null) {
            throw new InvalidParameterException("empty type");
        }
        Date now = new Date();
        DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd-hhmmssSSS", Locale.ITALIAN);

        if (StringUtils.isNotBlank(extension)) {
            extension = "." + extension;
        }
        return type + "-" + sanitizeFilename(filename) + "-" + dateFormat.format(now) + (StringUtils.isNotBlank(extension) ? extension : "");
    }

    public static String getPath(FileType type, Date date, String filename) {
        if (type == null) {
            return null;
        }
        if (date == null) {
            return null;
        }
        if (StringUtils.isBlank(filename)) {
            return null;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        return type.getPath() + "/"
                + StringUtils.right("0000" + calendar.get(Calendar.YEAR), 4) + "/"
                + StringUtils.right("00" + calendar.get(Calendar.MONTH), 2) + "/"
                + filename;
    }

    private static String sanitizeFilename(String name) {
        return StringUtils.replace(StringUtils.replacePattern(name, "[\\\\/:*?\"<>|]", ""), " ", "_");
    }

    public static File getFileFromResource(String fileName) {
        // String fileName = "public/be/scripts/backup-mongo-storage-giovannicasellato.sh";

        ClassLoader classLoader = FileUtils.class.getClassLoader();
        URL resource = classLoader.getResource(fileName);

        if (resource == null) {
            LOGGER.error("Resource not found: " + fileName);
            return null;
        }

        try {
            return new File(resource.toURI());
        } catch (URISyntaxException ex) {
            LOGGER.error("unable to get file from resource", ex);
            return null;
        }
    }

    public static File createTempFileWithContent(String content) throws IOException {
        content = content.replace("\r\n", "\n").replace("\r", "\n");
        
        File tempFile = Files.createTempFile("tmp-", ".sh").toFile();
        Files.writeString(tempFile.toPath(), content, StandardOpenOption.WRITE, StandardOpenOption.TRUNCATE_EXISTING);
        // System.out.println("File created: " + tempFile.getAbsolutePath());
        return tempFile;
    }

    public static void uploadFileToSFTP(File file, String host, String username, String password, int port, String remotePath) {
        JSch jsch = new JSch();
        Session session = null;
        ChannelSftp sftpChannel = null;

        try {
            // Create an SSH session
            session = jsch.getSession(username, host, port);
            session.setPassword(password);
            session.setConfig("StrictHostKeyChecking", "no"); // Disable strict checking

            // System.out.println("Connecting to SFTP...");
            session.connect(); // Connect to the SFTP server

            // Open SFTP channel
            Channel channel = session.openChannel("sftp");
            channel.connect();
            sftpChannel = (ChannelSftp) channel;

            // Extract remote directory and filename
            String remoteDir = remotePath.substring(0, remotePath.lastIndexOf('/'));
            // String fileName = remotePath.substring(remotePath.lastIndexOf('/') + 1);

            // Ensure the remote directory exists
            ensureRemoteDirectoryExists(sftpChannel, remoteDir);

            // Upload the file
            try ( FileInputStream fis = new FileInputStream(file)) {
                sftpChannel.put(fis, remotePath);
                // System.out.println("File uploaded successfully to: " + remotePath);
            }

        } catch (JSchException | SftpException | IOException ex) {
            LOGGER.error("Unable to upload file", ex);
        } finally {
            // Cleanup
            if (sftpChannel != null) {
                sftpChannel.exit();
            }
            if (session != null) {
                session.disconnect();
            }
        }
    }

    private static void ensureRemoteDirectoryExists(ChannelSftp sftpChannel, String remoteDir) {
        try {
            sftpChannel.cd(remoteDir); // Try to change to the directory
        } catch (SftpException e) {
            System.out.println("Directory does not exist. Creating: " + remoteDir);
            String[] folders = remoteDir.split("/");
            String path = "";
            for (String folder : folders) {
                if (!folder.isEmpty()) {
                    path += "/" + folder;
                    try {
                        sftpChannel.cd(path);
                    } catch (SftpException ex) {
                        try {
                            sftpChannel.mkdir(path);
                            sftpChannel.cd(path);
                        } catch (SftpException ex2) {
                            LOGGER.error("Unable to create base dir", ex2);
                        }
                    }
                }
            }
        }
    }
}
