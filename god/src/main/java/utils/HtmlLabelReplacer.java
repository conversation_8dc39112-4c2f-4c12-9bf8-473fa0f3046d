package utils;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Utility for manually replacing HTML text with labels
 */
public class HtmlLabelReplacer {
    private static final Logger LOGGER = LoggerFactory.getLogger(HtmlLabelReplacer.class.getName());

    /**
     * Replace a text in an HTML file with a label
     * 
     * @param htmlFilePath Path to the HTML file
     * @param originalText The text to replace (can include HTML tags)
     * @param labelKey The label key to use in the replacement
     * @return true if replacement was successful
     */
    public static boolean replaceTextWithLabel(String htmlFilePath, String originalText, String labelKey) throws IOException {
        if (StringUtils.isBlank(htmlFilePath) || StringUtils.isBlank(originalText) || StringUtils.isBlank(labelKey)) {
            return false;
        }

        Path path = Paths.get(htmlFilePath);
        if (!Files.exists(path)) {
            throw new IOException("File non trovato: " + htmlFilePath);
        }

        String htmlContent = Files.readString(path);
        String replacement = "{{ label('" + labelKey + "') | raw }}";

        // Use literal text replacement if possible
        String modifiedHtml = htmlContent.replace(originalText, replacement);

        if (modifiedHtml.equals(htmlContent)) {
            // Try with regex to handle slight variations in whitespace
            String escapedText = Pattern.quote(originalText);
            // Allow for flexible whitespace
            escapedText = escapedText.replaceAll("\\\\s+", "\\\\s+");
            Pattern pattern = Pattern.compile(escapedText, Pattern.DOTALL);
            Matcher matcher = pattern.matcher(htmlContent);

            if (matcher.find()) {
                modifiedHtml = matcher.replaceAll(replacement);
            } else {
                return false; // Text not found
            }
        }

        // Create a backup of the original file
        Path backupPath = Paths.get(htmlFilePath + ".backup");
        if (!Files.exists(backupPath)) {
            Files.copy(path, backupPath);
        }

        // Save the modified content
        Files.writeString(path, modifiedHtml);

        // Also add to labels file if it exists
        Path labelsPath = Paths.get(htmlFilePath.replace(".html", "_labels.json"));
        if (Files.exists(labelsPath)) {
            try {
                String labelsContent = Files.readString(labelsPath);
                org.json.JSONArray labels = new org.json.JSONArray(labelsContent);

                // Check if label already exists
                boolean labelExists = false;
                for (int i = 0; i < labels.length(); i++) {
                    org.json.JSONObject label = labels.getJSONObject(i);
                    if (label.has("label") && label.getString("label").equals(labelKey)) {
                        labelExists = true;
                        break;
                    }
                }

                // Add new label if it doesn't exist
                if (!labelExists) {
                    org.json.JSONObject newLabel = new org.json.JSONObject();
                    newLabel.put("label", labelKey);
                    newLabel.put("original", originalText);
                    labels.put(newLabel);

                    Files.writeString(labelsPath, labels.toString(2));
                }
            } catch (Exception e) {
                LOGGER.error("Errore durante l'aggiornamento del file labels: " + e.getMessage(), e);
            }
        }

        return true;
    }

    /**
     * Scan HTML file and create a map of text content to suggested label keys
     * 
     * @param htmlFilePath Path to the HTML file
     * @return Map of text content to suggested label keys
     */
    public static Map<String, String> scanHtmlForLabels(String htmlFilePath) throws IOException {
        Path path = Paths.get(htmlFilePath);
        if (!Files.exists(path)) {
            throw new IOException("File non trovato: " + htmlFilePath);
        }

        String htmlContent = Files.readString(path);
        Document doc = Jsoup.parse(htmlContent);

        // Remove script and style elements
        doc.select("script, style, noscript, iframe").remove();

        Map<String, String> textToLabel = new HashMap<>();

        // Find text in common elements
        Elements textElements = doc.select("p, h1, h2, h3, h4, h5, h6, li, td, th, div, span, a, button, label");
        // Extract page name from the document title to send to AI
        String pageName = "page";
        Element titleElement = doc.select("title").first();
        if (titleElement != null) {
            pageName = titleElement.text().trim();
        }

        for (Element element : textElements) {
            String text = element.text().trim();
            if (!StringUtils.isBlank(text) && text.length() >= 3 && !containsOnlyNumbers(text)) {
                // We'll use a temporary key here - the actual label will be generated by the AI
                String temporaryKey = "temp_" + UUID.randomUUID().toString().replace("-", "");
                textToLabel.put(element.outerHtml(), temporaryKey);
            }
        }

        return textToLabel;
    }

    private static boolean containsOnlyNumbers(String text) {
        return text.matches("^[\\d\\s.,]+$");
    }

    /**
     * Create an HTML file with original text replaced by labels
     * 
     * @param originalHtmlPath Path to the original HTML file
     * @param labelsJsonPath Path to the JSON file with labels
     * @return Path to the created file
     */
    public static Path createLabeledHtmlFile(String originalHtmlPath, String labelsJsonPath) throws IOException {
        Path htmlPath = Paths.get(originalHtmlPath);
        Path jsonPath = Paths.get(labelsJsonPath);

        if (!Files.exists(htmlPath) || !Files.exists(jsonPath)) {
            throw new IOException("File non trovato");
        }

        String htmlContent = Files.readString(htmlPath);
        String jsonContent = Files.readString(jsonPath);

        org.json.JSONArray labels = new org.json.JSONArray(jsonContent);

        // Replace each original text with its label
        for (int i = 0; i < labels.length(); i++) {
            org.json.JSONObject label = labels.getJSONObject(i);
            String originalText = label.getString("original");
            String labelKey = label.getString("label");
            String replacement = "{{ label('" + labelKey + "') | raw }}";

            htmlContent = htmlContent.replace(originalText, replacement);
        }

        // Save to new file
        Path outputPath = Paths.get(originalHtmlPath.replace(".html", "_labeled.html"));
        Files.writeString(outputPath, htmlContent);

        return outputPath;
    }
}
