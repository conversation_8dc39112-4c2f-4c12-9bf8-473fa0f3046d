package utils;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.jsoup.parser.Parser;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.ExtractedText;
import pojo.TextExtractionResult;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * Utility class for extracting text from HTML files
 */
public class HtmlTextExtractor {
    private static final Logger LOGGER = LoggerFactory.getLogger(HtmlTextExtractor.class.getName());

    // Skip these tags for translation
    private static final Pattern SKIP_TAGS = Pattern.compile("script, style, meta, link, iframe, noscript, code, svg", Pattern.CASE_INSENSITIVE);
    // Consider these tags as text containers but preserve their formatting
    private static final Pattern FORMAT_TAGS = Pattern.compile("br, span, strong, em, i, b, a, u, sup, sub", Pattern.CASE_INSENSITIVE);
    // Tags that define boundaries of translation units
    private static final Pattern BLOCK_TAGS = Pattern.compile("div, a, p, h1, h2, h3, h4, h5, h6, li, td, th, figcaption, button, label", Pattern.CASE_INSENSITIVE);

    /**
     * Extract text from HTML content
     * @param htmlContent Original HTML content
     * @return TextExtractionResult with extracted text and placeholders
     */
    public static TextExtractionResult extractText(String htmlContent) {
        TextExtractionResult result = new TextExtractionResult(htmlContent);
        List<ExtractedText> extractedTexts = new ArrayList<>();

        // Use Parser.htmlParser() with specific settings to prevent adding html/head/body tags
        Document doc = Jsoup.parse(htmlContent, "", Parser.htmlParser());
        doc.outputSettings().prettyPrint(true);
        doc.outputSettings().indentAmount(4);
        doc.outputSettings().charset(StandardCharsets.UTF_8);
        doc.outputSettings().syntax(Document.OutputSettings.Syntax.html);

        // Set to false to prevent auto-detection and normalization of the document structure
        doc.quirksMode(Document.QuirksMode.noQuirks);

        // Skip certain elements completely
        /*Elements elementsToSkip = doc.select(SKIP_TAGS.pattern());
        for (Element element : elementsToSkip) {
            element.remove();
        }*/

        // Process block elements
        int previousSize;
        int iterations = 0;
        do {
            previousSize = extractedTexts.size();
            Elements blockElements = doc.select(BLOCK_TAGS.pattern());
            for (Element blockElement : blockElements) {
                processBlockElement(blockElement, extractedTexts);
            }
            iterations++;
        } while (previousSize < extractedTexts.size() && iterations < 10);

        // Also process direct text in body or other container elements
        // that might not be inside block elements
        /*Elements rootElements = doc.select("body, html");
        for (Element rootElement : rootElements) {
            processDirectTextNodes(rootElement, extractedTexts);
        }*/

        result.setExtractedTexts(extractedTexts);
        result.setProcessedHtml(doc.outerHtml());

        return result;
    }

    /**
     * Process a block element to extract text units
     */
    private static void processBlockElement(Element element, List<ExtractedText> extractedTexts) {
        if (SKIP_TAGS.matcher(element.tagName()).matches()) {
            return;
        }

        // Check if this element contains text (directly or in formatting elements)
        String text = element.text().trim();
        if (StringUtils.isBlank(text)) {
            return;
        }

        // Skip if it contains nested block elements, as those will be processed separately
        Elements nestedBlockElements = element.select(BLOCK_TAGS.pattern());
        if (nestedBlockElements.size() > 1) {
            // se sono due elementi e il testo contiene "[[" e "]]" togliere quella parte e proseguire
            if (nestedBlockElements.size() == 2 && text.contains("[[") && text.contains("]]")) {
                String textBefore = text.substring(0, text.indexOf("[["));
                String textAfter = text.substring(text.indexOf("]]") + 2);
                // solo uno dei 2 deve essere popolato
                if (!StringUtils.isBlank(textBefore) && StringUtils.isBlank(textAfter)) {
                    text = textBefore.trim();
                } else if (!StringUtils.isBlank(textAfter) && StringUtils.isBlank(textBefore)) {
                    text = textAfter.trim();
                } else {
                    return;
                }
            } else {
                return;
            }
        }
        if (StringUtils.isBlank(text)) {
            return;
        }
        // provo a togliere i caratteri speciali
        if (text.length() <= 1) {
            return;
        }
        // se inizia con "[[" e finisce con "]]" allora è già stato processato
        if (text.startsWith("[[") && text.endsWith("]]")) {
            return;
        }
        // se inizia con "{{" e finisce con "}}" allora è già stato processato
        if (text.startsWith("{{") && text.endsWith("}}")) {
            return;
        }

        // Create extraction result
        ExtractedText extractedText = new ExtractedText(text);
        extractedTexts.add(extractedText);

        // Use Pattern.quote to escape special regex characters in the text
        // element.html(element.html().replaceAll(Pattern.quote(text), "[[" + extractedText.getId() + "]]"));
        element.html("[[" + extractedText.getId() + "]]");
    }

    /**
     * Process direct text nodes in an element that aren't wrapped in blocks
     */
    private static void processDirectTextNodes(Element element, List<ExtractedText> extractedTexts) {
        if (SKIP_TAGS.matcher(element.tagName()).matches()) {
            return;
        }

        List<Node> toProcess = new ArrayList<>();
        for (Node node : element.childNodes()) {
            if (node instanceof TextNode) {
                String text = ((TextNode) node).text().trim();
                if (!StringUtils.isBlank(text)) {
                    toProcess.add(node);
                }
            } else if (node instanceof Element && FORMAT_TAGS.matcher(((Element) node).tagName()).matches()) {
                toProcess.add(node);
            }
        }

        if (!toProcess.isEmpty()) {
            StringBuilder combinedHtml = new StringBuilder();
            for (Node node : toProcess) {
                if (node instanceof TextNode) {
                    combinedHtml.append(((TextNode) node).text());
                } else if (node instanceof Element) {
                    combinedHtml.append(((Element) node).outerHtml());
                }
                // node.remove();
            }

            String html = combinedHtml.toString().trim();

            if (!StringUtils.isBlank(html)) {
                ExtractedText extractedText = new ExtractedText(html);
                extractedTexts.add(extractedText);
            }
        }
    }

    /**
     * Reconstruct the HTML file, replacing placeholders with labels
     * @param extractionResult The extraction result
     * @return The HTML with placeholders replaced by labels
     */
    public static String reconstructHtmlWithLabels(TextExtractionResult extractionResult) {
        String html = extractionResult.getProcessedHtml();

        for (ExtractedText text : extractionResult.getExtractedTexts()) {
            if (text != null && text.getTranslations() != null && !text.getTranslations().isEmpty()) {
                String placeholder = "[[" + text.getId() + "]]";
                String labelKey = text.getTranslations().get("label");
                String replacement = "{{ label('" + labelKey + "') | raw }}";
                html = html.replace(placeholder, replacement);
            }
        }

        // If the original HTML didn't have html/head/body tags, remove them from the output
        String originalHtml = extractionResult.getOriginalHtml();
        if (!originalHtml.toLowerCase().contains("<html")) {
            // Remove html tags if they were added
            html = html.replaceAll("(?i)<html[^>]*>(.*)</html>", "$1").trim();
        }
        if (!originalHtml.toLowerCase().contains("<head")) {
            // Remove head tags if they were added
            html = html.replaceAll("(?i)<head[^>]*>(.*)</head>", "").trim();
        }
        if (!originalHtml.toLowerCase().contains("<body")) {
            // Remove body tags if they were added
            html = html.replaceAll("(?i)<body[^>]*>(.*)</body>", "$1").trim();
        }

        return html;
    }
}