package utils;

import java.io.File;
import java.util.List;
import java.util.Properties;
import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Smtp;

/**
 *
 * <AUTHOR>
 */
public class MailUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(MailUtils.class.getName());

    public static boolean sendMail(String to, String object, String content, List<File> attachments, Smtp smtp) throws Exception {
        boolean done = false;

        // smtp sarà solo in lingua base
        if (smtp != null) {
            Properties prop = new Properties();
            prop.put("mail.smtp.auth", BooleanUtils.isTrue(smtp.getAuthentication()));
            prop.put("mail.smtp.ssl.enable", BooleanUtils.isTrue(smtp.getEncryption()));
            prop.put("mail.smtp.starttls.enable", BooleanUtils.isTrue(smtp.getStartTls()));
            prop.put("mail.smtp.host", smtp.getHostname());
            prop.put("mail.smtp.port", smtp.getPort());
            prop.put("mail.smtp.timeout", "10000");
            prop.put("mail.debug", "true");

            Session session = Session.getInstance(prop, new Authenticator() {
                @Override
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(smtp.getUsername(), smtp.getPassword());
                }
            });

            try {
                Message message = new MimeMessage(session);
                message.setFrom(new InternetAddress(smtp.getSender()));
                message.setRecipients(
                        Message.RecipientType.TO,
                        InternetAddress.parse(to));
                message.setSubject(object);

                // Creazione del corpo del messaggio, valido anche contenuto HTML
                BodyPart messageBodyPart = new MimeBodyPart();
                if (content.contains("<") && content.contains(">")) { // se trovo tag HTML
                    messageBodyPart.setContent(content, "text/html");
                } else {
                    messageBodyPart.setText(content);
                }

                // Creazione del multipart per unire corpo del messaggio e allegato
                Multipart multipart = new MimeMultipart();
                multipart.addBodyPart(messageBodyPart);

                if (attachments != null && !attachments.isEmpty()) {
                    for (File attachment : attachments) {
                        if (attachment.exists()) {
                            BodyPart attachmentBodyPart = new MimeBodyPart();
                            DataSource source = new FileDataSource(attachment.getPath());
                            attachmentBodyPart.setDataHandler(new DataHandler(source));
                            attachmentBodyPart.setFileName(FilenameUtils.getName(attachment.getPath()));
                            multipart.addBodyPart(attachmentBodyPart);
                        } else {
                            LOGGER.warn("Attachment doesn't exists at path: " + attachment.getPath());
                        }
                    }
                }

                // Impostazione del multipart come contenuto del messaggio
                message.setContent(multipart);

                // Invio dell'email con l'allegato
                Transport.send(message);
                done = true;
            } catch (MessagingException ex) {
                LOGGER.error("Can't send email. Error is ", ex);
            }
        }

        return done;
    }
}
