package utils;

import java.lang.reflect.Field;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.CharUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.ObjectDifference;

/**
 *
 * <AUTHOR>
 */
public class ObjectUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(ObjectUtils.class.getName());

    public static List<ObjectDifference> getDifference(Object previous, Object after) {
        if (previous == null) {
            throw new InvalidParameterException("previous is null. Can't continue");
        }
        if (after == null) {
            throw new InvalidParameterException("after is null. Can't continue");
        }

        List<ObjectDifference> differences = null;
        List<Field> fieldToCheck = new ArrayList<>();
        fieldToCheck.addAll(Arrays.asList(previous.getClass().getSuperclass().getDeclaredFields())); // prima i campi in comune per tutti (BasePojo)
        fieldToCheck.addAll(Arrays.asList(previous.getClass().getDeclaredFields())); // poi tutti gli altri
        for (Field field : fieldToCheck) {
            field.setAccessible(true);

            ObjectDifference difference = getFieldDifference(field, previous, after);
            if (difference != null) {
                if (differences == null) {
                    differences = new ArrayList<>();
                }

                differences.add(difference);
            }
        }

        return differences;
    }

    private static ObjectDifference getFieldDifference(Field field, Object previous, Object after) {
        if (field == null) {
            throw new InvalidParameterException("field is null. Can't continue");
        }
        if (previous == null) {
            throw new InvalidParameterException("previous is null. Can't continue");
        }
        if (after == null) {
            throw new InvalidParameterException("after is null. Can't continue");
        }

        try {
            if (Collection.class.isAssignableFrom(field.getType())) {
                // gestione liste
                List<Object> valueBefore = (List<Object>) field.get(previous);
                List<Object> valueAfter = (List<Object>) field.get(after);

                boolean isSameList = isSameList(valueBefore, valueAfter);
                if (!isSameList) {
                    StringBuilder valueBeforeString = new StringBuilder();
                    if (valueBefore != null && !valueBefore.isEmpty()) {
                        for (Object value : valueBefore) {
                            if (!StringUtils.isEmpty(valueBeforeString)) {
                                valueBeforeString.append(",");
                            }
                            valueBeforeString.append(value);
                        }
                    }
                    StringBuilder valueAfterString = new StringBuilder();
                    if (valueAfter != null && !valueAfter.isEmpty()) {
                        for (Object value : valueAfter) {
                            if (!StringUtils.isEmpty(valueAfterString)) {
                                valueAfterString.append(",");
                            }
                            valueAfterString.append(value);
                        }
                    }

                    ObjectDifference difference = new ObjectDifference();
                    difference.setFieldName(field.getName());
                    difference.setFieldType("List");
                    difference.setValueBefore(valueBeforeString.toString());
                    difference.setValueAfter(valueAfterString.toString());
                    return difference;
                }
            } else if (String.class.isAssignableFrom(field.getType())) {
                String valueBefore = (String) field.get(previous);
                String valueAfter = (String) field.get(after);

                if (!StringUtils.equals(valueBefore, valueAfter)) {
                    ObjectDifference difference = new ObjectDifference();
                    difference.setFieldName(field.getName());
                    difference.setFieldType("String");
                    difference.setValueBefore(valueBefore == null ? "" : valueBefore);
                    difference.setValueAfter(valueAfter == null ? "" : valueAfter);
                    return difference;
                }
            } else if (Integer.class.isAssignableFrom(field.getType())) {
                int valueBefore = field.getInt(previous);
                int valueAfter = field.getInt(after);

                if (valueBefore != valueAfter) {
                    ObjectDifference difference = new ObjectDifference();
                    difference.setFieldName(field.getName());
                    difference.setFieldType("Integer");
                    difference.setValueBefore(String.valueOf(valueBefore));
                    difference.setValueAfter(String.valueOf(valueAfter));
                    return difference;
                }
            } else if (Long.class.isAssignableFrom(field.getType())) {
                long valueBefore = field.getLong(previous);
                long valueAfter = field.getLong(after);

                if (valueBefore != valueAfter) {
                    ObjectDifference difference = new ObjectDifference();
                    difference.setFieldName(field.getName());
                    difference.setFieldType("Long");
                    difference.setValueBefore(String.valueOf(valueBefore));
                    difference.setValueAfter(String.valueOf(valueAfter));
                    return difference;
                }
            } else if (Double.class.isAssignableFrom(field.getType())) {
                double valueBefore = field.getDouble(previous);
                double valueAfter = field.getInt(after);

                if (valueBefore != valueAfter) {
                    ObjectDifference difference = new ObjectDifference();
                    difference.setFieldName(field.getName());
                    difference.setFieldType("Double");
                    difference.setValueBefore(String.valueOf(valueBefore));
                    difference.setValueAfter(String.valueOf(valueAfter));
                    return difference;
                }
            } else if (ObjectId.class.isAssignableFrom(field.getType()) && !StringUtils.equals(field.getName(), "id")) {
                ObjectId valueBefore = (ObjectId) field.get(previous);
                ObjectId valueAfter = (ObjectId) field.get(after);

                if ((valueBefore == null && valueAfter != null)
                        || (valueBefore != null && valueAfter == null)
                        || (valueBefore != null && valueAfter != null && !valueBefore.equals(valueAfter))) {
                    ObjectDifference difference = new ObjectDifference();
                    difference.setFieldName(field.getName());
                    difference.setFieldType("ObjectId");
                    difference.setValueBefore(valueBefore == null ? "" : String.valueOf(valueBefore));
                    difference.setValueAfter(valueAfter == null ? "" : String.valueOf(valueAfter));
                    return difference;
                }
            } else if (Date.class.isAssignableFrom(field.getType()) && !StringUtils.equals(field.getName(), "lastUpdate")) {
                Date valueBefore = (Date) field.get(previous);
                Date valueAfter = (Date) field.get(after);

                if ((valueBefore == null && valueAfter != null)
                        || (valueBefore != null && valueAfter == null)
                        || (valueBefore != null && valueAfter != null && !valueBefore.equals(valueAfter))) {
                    ObjectDifference difference = new ObjectDifference();
                    difference.setFieldName(field.getName());
                    difference.setFieldType("Date");
                    difference.setValueBefore(valueBefore == null ? "" : String.valueOf(valueBefore));
                    difference.setValueAfter(valueAfter == null ? "" : String.valueOf(valueAfter));
                    return difference;
                }
            } else if (Float.class.isAssignableFrom(field.getType())) {
                float valueBefore = field.getFloat(previous);
                float valueAfter = field.getFloat(after);

                if (valueBefore != valueAfter) {
                    ObjectDifference difference = new ObjectDifference();
                    difference.setFieldName(field.getName());
                    difference.setFieldType("Float");
                    difference.setValueBefore(String.valueOf(valueBefore));
                    difference.setValueAfter(String.valueOf(valueAfter));
                }
            } else if (Boolean.class.isAssignableFrom(field.getType())) {
                boolean valueBefore = field.getBoolean(previous);
                boolean valueAfter = field.getBoolean(after);

                if (valueBefore != valueAfter) {
                    ObjectDifference difference = new ObjectDifference();
                    difference.setFieldName(field.getName());
                    difference.setFieldType("Boolean");
                    difference.setValueBefore(String.valueOf(valueBefore));
                    difference.setValueAfter(String.valueOf(valueAfter));
                    return difference;
                }
            } else if (Byte.class.isAssignableFrom(field.getType())) {
                byte valueBefore = field.getByte(previous);
                byte valueAfter = field.getByte(after);

                if (valueBefore != valueAfter) {
                    ObjectDifference difference = new ObjectDifference();
                    difference.setFieldName(field.getName());
                    difference.setFieldType("Byte");
                    difference.setValueBefore(String.valueOf(valueBefore));
                    difference.setValueAfter(String.valueOf(valueAfter));
                    return difference;
                }
            } else if (Character.class.isAssignableFrom(field.getType())) {
                char valueBefore = field.getChar(previous);
                char valueAfter = field.getChar(after);

                if (CharUtils.compare(valueBefore, valueAfter) != 0) {
                    ObjectDifference difference = new ObjectDifference();
                    difference.setFieldName(field.getName());
                    difference.setFieldType("Char");
                    difference.setValueBefore(String.valueOf(valueBefore));
                    difference.setValueAfter(String.valueOf(valueAfter));
                    return difference;
                }
            } else {
                LOGGER.warn("Field not parsable. Type is " + field.getGenericType().getTypeName());
            }
        } catch (IllegalAccessException | IllegalArgumentException ex) {
            LOGGER.error("Error on getFieldDifference.", ex);
        }

        return null;
    }

    private static boolean isSameList(List<Object> list1, List<Object> list2) {
        if (list1 == null && list2 == null) {
            return true;
        }
        if ((list1 == null && list2 != null) || (list1 != null && list2 == null)) {
            return false;
        }

        if (list1 != null && list2 != null) {
            if (list1.size() != list2.size()) {
                return false;
            }

            for (int i = 0; i < list1.size(); i++) {
                if (!Objects.equals(list1.get(i), list2.get(i))) {
                    return false;
                }
            }
        }
        return true;
    }
}
