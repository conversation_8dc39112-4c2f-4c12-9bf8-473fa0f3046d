const Labelizer = function () {
    // Initialization of components
    const init = function () {
        _componentSelect2();
        _componentValidate();
        _componentMaxlength();
    };

    // Select2
    const _componentSelect2 = function () {
        if (!$().select2) {
            console.warn('Warning - select2.min.js is not loaded.');
            return;
        }

        // Default initialization
        $('.select').select2({
            language: "it"
        });


        // Format icon
        function iconFormat(icon) {
            if (!icon.id) {
                return icon.text;
            }
            var $icon = '<i class="ph-' + $(icon.element).data('icon') + '"></i>' + icon.text;

            return $icon;
        }

        // Initialize with options
        $('.select-icons').select2({
            templateResult: iconFormat,
            minimumResultsForSearch: Infinity,
            templateSelection: iconFormat,
            escapeMarkup: function (m) {
                return m;
            }
        });

        // Format country
        function formatLanguage(state) {
            if (!state.id) {
                return state.text;
            }
            var baseUrl = pageVariables.get("contextPath") + '/be/images/lang/';
            var $state = $(
                    '<span><img src="' + baseUrl + '/' + state.element.value.toLowerCase() + '.svg" class="img-flag" /> ' + state.text + '</span>');
            return $state;
        }
        ;

        $(".select-language").select2({
            templateResult: formatLanguage,
            templateSelection: formatLanguage
        });

    };

    // Validation config
    const _componentValidate = function () {
        if (!$().validate) {
            console.warn('Warning - validate.min.js is not loaded.');
            return;
        }

        // Initialize
        const validator = $('.form-validate-jquery').validate({
            ignore: 'input[type=hidden], .select2-search__field', // ignore hidden fields
            errorClass: 'validation-invalid-label',
            successClass: 'validation-valid-label',
            validClass: 'validation-valid-label',
            highlight: function (element, errorClass) {
                $(element).removeClass(errorClass);
            },
            unhighlight: function (element, errorClass) {
                $(element).removeClass(errorClass);
            },
            // Different components require proper error label placement
            errorPlacement: function (error, element) {

                // Input with icons and Select2
                if (element.hasClass('ckeditor')) {
                    error.appendTo(element.parent());
                }

                // Input with icons and Select2
                else if (element.hasClass('select')) {
                    error.appendTo(element.parent());
                }

                // Input group, form checks and custom controls
                else if (element.parents().hasClass('form-control-feedback') || element.parents().hasClass('form-check') || element.parents().hasClass('input-group')) {
                    error.appendTo(element.parent().parent());
                }

                // Other elements
                else {
                    error.insertAfter(element);
                }
            }
        });

        // custom url validation
        $.validator.addMethod('identifier', function (value) {
            return /^[a-z0-9-_]+$/.test(value);
        }, 'URL non valido. L\'identificatore deve contenere solo lettere minuscole, numeri, trattini e sottolineature.');

    };

    // Maxlength
    const _componentMaxlength = function () {
        if (!$().maxlength) {
            console.warn('Warning - maxlength.min.js is not loaded.');
            return;
        }

        // Basic example
        $('.maxlength').maxlength({
            placement: document.dir === "rtl" ? 'top-left-inside' : 'top-right-inside',
            warningClass: 'bootstrap-maxlength text-muted form-text m-0',
            limitReachedClass: 'bootstrap-maxlength text-danger form-text m-0'
        });

    };

    // Return objects assigned to module
    return {
        init: init
    };
}();

// Initialize module
// ------------------------------
document.addEventListener('DOMContentLoaded', function () {
    Labelizer.init();

    submitLabelizer();
    initExportButtons();

    // Add test function to window for debugging
    window.testProgressDisplay = function() {
        console.log('Testing progress display...');
        $('#progress-container').show();
        updateProgressDisplay(50, 5, 10, '50% (5 di 10 file)', 'test.html');
    };
});

function submitLabelizer() {
    // Form submission handling
    $('#labelizer').submit(function (e) {
        if ($('#labelizer').valid()) {
            e.preventDefault();
            const formData = new FormData(this);
            // aggiunta parametro "languages"
            let languages = [];
            $("input[type='checkbox']:checked").each(function () {
                languages.push($(this).attr("value"));
            });
            formData.append('languages', languages.join(','));

            // Show progress container and hide form submit button
            console.log('Showing progress container...');
            const $progressContainer = $('#progress-container');
            console.log('Progress container element found:', $progressContainer.length > 0);
            $progressContainer.show();
            console.log('Progress container is visible:', $progressContainer.is(':visible'));
            $('#labelizer button[type="submit"]').prop('disabled', true);

            // Reset progress display
            console.log('Resetting progress display...');
            updateProgressDisplay(0, 0, 0, 'Inizializzazione...', '');

            $.ajax({
                url: $(this).attr("action"),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    console.log('Form submission response:', response);
                    try {
                        if (response.requestId) {
                            console.log('Starting progress polling for request:', response.requestId);
                            // Start polling for progress
                            startProgressPolling(response.requestId);
                        } else {
                            // Fallback for old response format
                            console.log('Using fallback for old response format');
                            handleLabelizerSuccess(response);
                        }
                    } catch (e) {
                        console.log('Parse error, using fallback:', e);
                        // Fallback for old response format (just total tokens)
                        handleLabelizerSuccess(response);
                    }
                },
                error: function (error) {
                    handleLabelizerError(error);
                }
            });
        }
    });
}

function startProgressPolling(requestId) {
    const progressUrl = appRoutes.get('BE_LABELIZER_PROGRESS') + '?requestId=' + requestId;

    const pollProgress = function() {
        $.ajax({
            url: progressUrl,
            type: 'GET',
            success: function(progressData) {
                try {
                    console.log('Progress data:', progressData);

                    const percentage = progressData.percentage || 0;
                    const processedFiles = progressData.processedFiles || 0;
                    const totalFiles = progressData.totalFiles || 0;
                    const currentFile = progressData.currentFile || '';
                    const isCompleted = progressData.isCompleted || false;
                    const hasError = progressData.hasError || false;

                    // Chunking information
                    const totalChunks = progressData.totalChunks || 0;
                    const processedChunks = progressData.processedChunks || 0;
                    const currentChunk = progressData.currentChunk || '';
                    const detailedProgress = progressData.detailedProgress || '';

                    // Update progress display with chunking info
                    let progressText = `${percentage}% (${processedFiles} di ${totalFiles} file)`;
                    if (totalChunks > 1) {
                        progressText += ` - ${processedChunks}/${totalChunks} chunks`;
                    }

                    let displayCurrentFile = currentFile;
                    if (currentChunk && currentChunk.trim() !== '') {
                        displayCurrentFile = `${currentFile} - ${currentChunk}`;
                    }

                    updateProgressDisplay(percentage, processedFiles, totalFiles, progressText, displayCurrentFile, detailedProgress);

                    if (hasError) {
                        // Process failed
                        console.log('Process failed with error:', progressData.errorMessage);
                        const errorMessage = progressData.errorMessage || 'Errore sconosciuto durante l\'elaborazione';
                        handleLabelizerError({responseText: errorMessage});
                    } else if (isCompleted) {
                        // Process completed successfully
                        console.log('Process completed successfully');
                        const totalTokens = progressData.totalTokens || 0;
                        handleLabelizerSuccess(totalTokens);
                    } else {
                        // Continue polling
                        console.log('Continuing polling...');
                        setTimeout(pollProgress, 2000); // Poll every 2 seconds
                    }
                } catch (e) {
                    console.error('Error parsing progress response:', e);
                    setTimeout(pollProgress, 2000); // Continue polling even on parse error
                }
            },
            error: function(error) {
                console.error('Error polling progress:', error);
                if (error.status === 404) {
                    // Request not found, might be completed and cleaned up
                    handleLabelizerError({responseText: 'Richiesta non trovata. Il processo potrebbe essere già completato.'});
                } else {
                    // Continue polling even on error, but with longer interval
                    setTimeout(pollProgress, 5000);
                }
            }
        });
    };

    // Start polling
    setTimeout(pollProgress, 1000); // Start after 1 second
}

function updateProgressDisplay(percentage, processedFiles, totalFiles, progressText, currentFile, detailedProgress) {
    console.log('Updating progress:', {percentage, processedFiles, totalFiles, progressText, currentFile, detailedProgress});

    // Update progress bar
    const $progressBar = $('#progress-bar');
    console.log('Progress bar element found:', $progressBar.length > 0);

    $progressBar.css('width', percentage + '%')
                .attr('aria-valuenow', percentage)
                .text(percentage + '%');

    // Update text elements
    $('#progress-text').text(progressText);

    // Show detailed progress if available, otherwise show current file
    let fileDisplayText = '';
    if (detailedProgress && detailedProgress.trim() !== '') {
        fileDisplayText = 'Progresso: ' + detailedProgress;
    } else if (currentFile && currentFile.trim() !== '') {
        fileDisplayText = 'File corrente: ' + currentFile;
    }
    $('#current-file').text(fileDisplayText);

    console.log('Progress display updated successfully');
}

function handleLabelizerSuccess(totalTokens) {
    console.log('Handling success with totalTokens:', totalTokens, typeof totalTokens);
    $('#progress-container').hide();
    $('#labelizer button[type="submit"]').prop('disabled', false);

    // Handle both number and string values for totalTokens
    let tokensText = '';
    if (totalTokens && !isNaN(totalTokens) && totalTokens > 0) {
        tokensText = ' Usati un totale di ' + totalTokens + ' tokens.';
    }

    Swal.fire({
        text: 'Pagine tradotte con successo.' + tokensText,
        icon: 'success',
        timer: 3000,
        toast: true,
        showConfirmButton: false,
        position: 'top-end'
    }).then(function () {
        window.location.reload();
    });
}

function handleLabelizerError(error) {
    console.log('Handling error:', error);
    $('#progress-container').hide();
    $('#labelizer button[type="submit"]').prop('disabled', false);

    const errorText = error.responseText || error.message || 'Errore sconosciuto durante l\'elaborazione';

    Swal.fire({
        text: errorText,
        icon: 'error',
        timer: 5000,
        toast: true,
        showConfirmButton: false,
        position: 'top-end'
    });
    console.error('Error during page translations', error);
}

function initExportButtons() {
    // Handle export to MongoDB button clicks
    $(document).on('click', '.export-mongodb-btn', function(e) {
        e.preventDefault();

        const requestId = $(this).data('request-id');
        if (!requestId) {
            Swal.fire({
                text: 'ID richiesta non trovato.',
                icon: 'error',
                timer: 3000,
                toast: true,
                showConfirmButton: false,
                position: 'top-end'
            });
            return;
        }

        // Show loading state
        const $button = $(this);
        const originalText = $button.html();
        $button.prop('disabled', true).html('<i class="ph-spinner ph-spin me-1"></i>Generando...');

        // Create export URL
        const exportUrl = appRoutes.get('BE_LABELIZER_EXPORT') + '?requestId=' + requestId;

        // Use AJAX to handle the export with proper error handling
        $.ajax({
            url: exportUrl,
            type: 'GET',
            dataType: 'text', // Prevent jQuery from parsing as JSON
            processData: false,
            cache: false,
            success: function(data, status, xhr) {
                // Get filename from Content-Disposition header or create default
                let filename = 'translations_' + requestId + '.json';
                const disposition = xhr.getResponseHeader('Content-Disposition');
                if (disposition && disposition.indexOf('filename=') !== -1) {
                    const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                    const matches = filenameRegex.exec(disposition);
                    if (matches != null && matches[1]) {
                        filename = matches[1].replace(/['"]/g, '');
                    }
                }

                // Create blob URL and trigger download
                const blob = new Blob([data], { type: 'application/json;charset=utf-8' });
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);

                // Reset button state and show success
                $button.prop('disabled', false).html(originalText);
                Swal.fire({
                    text: 'File JSON generato e scaricato con successo.',
                    icon: 'success',
                    timer: 3000,
                    toast: true,
                    showConfirmButton: false,
                    position: 'top-end'
                });
            },
            error: function(xhr, status, error) {
                // Reset button state
                $button.prop('disabled', false).html(originalText);

                // Handle different error types
                let errorMessage = 'Errore durante la generazione del file JSON.';

                if (xhr.status === 404) {
                    errorMessage = 'Richiesta non trovata.';
                } else if (xhr.status === 400) {
                    errorMessage = 'Parametri richiesta non validi.';
                } else if (xhr.status === 500) {
                    errorMessage = 'Errore interno del server.';
                } else if (xhr.responseText) {
                    // Try to get error message from response
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMessage = response.message;
                        }
                    } catch (e) {
                        // If not JSON, use response text directly
                        errorMessage = xhr.responseText;
                    }
                }

                Swal.fire({
                    text: errorMessage,
                    icon: 'error',
                    timer: 5000,
                    toast: true,
                    showConfirmButton: false,
                    position: 'top-end'
                });

                console.error('Export error:', {
                    status: xhr.status,
                    statusText: xhr.statusText,
                    responseText: xhr.responseText,
                    error: error
                });
            },
            timeout: 30000 // 30 second timeout
        });
    });
}