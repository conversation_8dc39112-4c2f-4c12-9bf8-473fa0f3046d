!function(t){var e={};function n(r){if(e[r])return e[r].exports;var i=e[r]={i:r,l:!1,exports:{}};return t[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:r})},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=0)}([function(t,e,n){"use strict";var r=n(1);window.dokaCreate=r.create},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};
/*!
 * Doka 6.1.0
 * Copyright 2019 PQINA Inc - All Rights Reserved
 * Please visit https://pqina.nl/doka/ for further information
 */function i(t){return(i="function"==typeof Symbol&&"symbol"==r(Symbol.iterator)?function(t){return void 0===t?"undefined":r(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":void 0===t?"undefined":r(t)})(t)}function o(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function a(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable}))),r.forEach(function(e){o(t,e,n[e])})}return t}function l(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=[],r=!0,i=!1,o=void 0;try{for(var a,l=t[Symbol.iterator]();!(r=(a=l.next()).done)&&(n.push(a.value),!e||n.length!==e);r=!0);}catch(t){i=!0,o=t}finally{try{r||null==l.return||l.return()}finally{if(i)throw o}}return n}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}function c(t){return function(t){if(Array.isArray(t)){for(var e=0,n=new Array(t.length);e<t.length;e++)n[e]=t[e];return n}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}var u=function(t,e){return e.parentNode.insertBefore(t,e)},s=function(t,e){return e.parentNode.insertBefore(t,e.nextSibling)},f=function(t){return"object"===i(t)&&null!==t},d=function(t,e){for(var n in t)t.hasOwnProperty(n)&&e(n,t[n])},h=function(t){var e={};return d(t,function(n){!function(t,e,n){"function"!=typeof n?Object.defineProperty(t,e,n):t[e]=n}(e,n,t[n])}),e},p=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(null===n)return t.getAttribute(e)||t.hasAttribute(e);t.setAttribute(e,n)},g=["svg","path"],m=function(t){return g.includes(t)},v=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};"object"===i(e)&&(n=e,e=null);var r=m(t)?document.createElementNS("http://www.w3.org/2000/svg",t):document.createElement(t);return e&&(m(t)?p(r,"class",e):r.className=e),d(n,function(t,e){p(r,t,e)}),r},y=function(t){return function(e,n){void 0!==n&&t.children[n]?t.insertBefore(e,t.children[n]):t.appendChild(e)}},E=function(t,e,n,r){var i=n[0]||t.left,o=n[1]||t.top,l=i+t.width,c=o+t.height*(r[1]||1),u={element:a({},t),inner:{left:t.left,top:t.top,right:t.right,bottom:t.bottom},outer:{left:i,top:o,right:l,bottom:c}};return e.filter(function(t){return!t.isRectIgnored()}).map(function(t){return t.rect}).forEach(function(t){_(u.inner,a({},t.inner)),_(u.outer,a({},t.outer))}),w(u.inner),u.outer.bottom+=u.element.marginBottom,u.outer.right+=u.element.marginRight,w(u.outer),u},_=function(t,e){e.top+=t.top,e.right+=t.left,e.bottom+=t.top,e.left+=t.left,e.bottom>t.bottom&&(t.bottom=e.bottom),e.right>t.right&&(t.right=e.right)},w=function(t){t.width=t.right-t.left,t.height=t.bottom-t.top},T=function(t){return"number"==typeof t},x=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiffness,n=void 0===e?.5:e,r=t.damping,i=void 0===r?.75:r,o=t.mass,a=void 0===o?10:o,l=t.delay,c=void 0===l?0:l,u=null,s=null,f=0,d=!1,p=null,g=h({interpolate:function(t){if(null===p&&(p=t),!(t-c<p||d)){if(!T(u)||!T(s))return d=!0,void(f=0);!function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.001;return Math.abs(t-e)<r&&Math.abs(n)<r}(s+=f+=-(s-u)*n/a,u,f*=i)?g.onupdate(s):(s=u,f=0,d=!0,g.onupdate(s),g.oncomplete(s))}},target:{set:function(t){if(T(t)&&!T(s)&&(s=t,p=null),null===u&&(u=t,s=t,p=null),d&&(p=null),s===(u=t)||void 0===u)return d=!0,f=0,p=null,g.onupdate(s),void g.oncomplete(s);d=!1},get:function(){return u}},resting:{get:function(){return d}},onupdate:function(){},oncomplete:function(){},position:{get:function(){return s}}});return g},R=function(t){return t<.5?2*t*t:(4-2*t)*t-1},A={spring:x,tween:function(){var t,e,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=n.duration,i=void 0===r?500:r,o=n.easing,a=void 0===o?R:o,l=n.delay,c=void 0===l?0:l,u=null,s=!0,f=!1,d=null,p=h({interpolate:function(n){s||null===d||(null===u&&(u=n),n-u<c||((t=n-u-c)<i?(e=t/i,p.onupdate((t>=0?a(f?1-e:e):0)*d)):(t=1,e=f?0:1,p.onupdate(e*d),p.oncomplete(e*d),s=!0)))},target:{get:function(){return f?0:d},set:function(t){if(null===d)return d=t,p.onupdate(t),void p.oncomplete(t);t<d?(d=1,f=!0):(f=!1,d=t),s=!1,u=null}},resting:{get:function(){return s}},onupdate:function(){},oncomplete:function(){}});return p}},C=function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];(e=Array.isArray(e)?e:[e]).forEach(function(e){t.forEach(function(t){var o=t,a=function(){return n[t]},l=function(e){return n[t]=e};"object"===i(t)&&(o=t.key,a=t.getter||a,l=t.setter||l),e[o]&&!r||(e[o]={get:a,set:l})})})},I={opacity:1,scaleX:1,scaleY:1,translateX:0,translateY:0,rotateX:0,rotateY:0,rotateZ:0,originX:0,originY:0},O=function(t,e){if(Object.keys(t).length!==Object.keys(e).length)return!0;for(var n in e)if(e[n]!==t[n])return!0;return!1},M=function(t,e){var n=e.opacity,r=e.perspective,i=e.translateX,o=e.translateY,a=e.scaleX,l=e.scaleY,c=e.rotateX,u=e.rotateY,s=e.rotateZ,f=e.originX,d=e.originY,h=e.width,p=e.height,g="",m="";null==f&&null==d||(m+="transform-origin: ".concat(f||0,"px ").concat(d||0,"px;")),null!=r&&(g+="perspective(".concat(r,"px) ")),null==i&&null==o||(g+="translate3d(".concat(i||0,"px, ").concat(o||0,"px, 0) ")),null==a&&null==l||(g+="scale3d(".concat(null!=a?a:1,", ").concat(null!=l?l:1,", 1) ")),null!=s&&(g+="rotateZ(".concat(s,"rad) ")),null!=c&&(g+="rotateX(".concat(c,"rad) ")),null!=u&&(g+="rotateY(".concat(u,"rad) ")),""!=g&&(m+="transform:".concat(g,";")),null!=n&&(m+="opacity:".concat(n,";"),n<1&&(m+="pointer-events:none;"),0===n&&"BUTTON"===t.nodeName&&(m+="visibility:hidden;")),null!=h&&(m+="width:".concat(h,"px;")),null!=p&&(m+="height:".concat(p,"px;"));var v=t.elementCurrentStyle||"";m.length===v.length&&m===v||(t.style.cssText=m,t.elementCurrentStyle=m)},S={styles:function(t){var e=t.mixinConfig,n=t.viewProps,r=t.viewInternalAPI,i=t.viewExternalAPI,o=t.view,l=a({},n),c={};C(e,[r,i],n);var u=function(){return o.rect?E(o.rect,o.childViews,[n.translateX||0,n.translateY||0],[n.scaleX||0,n.scaleY||0]):null};return r.rect={get:u},i.rect={get:u},e.forEach(function(t){n[t]=void 0===l[t]?I[t]:l[t]}),{write:function(){if(O(c,n))return M(o.element,n),Object.assign(c,a({},n)),!0},destroy:function(){}}},listeners:function(t){var e=t.viewExternalAPI,n=t.view,r=[],i=function(t){return function(e,n){t.addEventListener(e,n)}}(n.element),o=function(t){return function(e,n){t.removeEventListener(e,n)}}(n.element);return e.on=function(t,e){r.push({type:t,fn:e}),i(t,e)},e.off=function(t,e){r.splice(r.findIndex(function(n){return n.type===t&&n.fn===e}),1),o(t,e)},{write:function(){return!0},destroy:function(){r.forEach(function(t){o(t.type,t.fn)})}}},animations:function(t){var e=t.mixinConfig,n=t.viewProps,r=t.viewInternalAPI,o=t.viewExternalAPI,l=a({},n),c=[];return d(e,function(t,e){var u=function(t,e,n){var r=t[e]&&"object"===i(t[e][n])?t[e][n]:t[e]||t,o="string"==typeof r?r:r.type,l="object"===i(r)?a({},r):{};return A[o]?A[o](l):null}(e);u&&(u.onupdate=function(e){n[t]=e},u.target=l[t],C([{key:t,setter:function(t){u.target!==t&&(u.target=t)},getter:function(){return n[t]}}],[r,o],n,!0),c.push(u))}),{write:function(t){var e=!0;return c.forEach(function(n){n.resting||(e=!1),n.interpolate(t)}),e},destroy:function(){}}},apis:function(t){var e=t.mixinConfig,n=t.viewProps,r=t.viewExternalAPI;C(e,r,n)}},b=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return e.layoutCalculated||(t.paddingTop=parseInt(n.paddingTop,10)||0,t.marginTop=parseInt(n.marginTop,10)||0,t.marginRight=parseInt(n.marginRight,10)||0,t.marginBottom=parseInt(n.marginBottom,10)||0,t.marginLeft=parseInt(n.marginLeft,10)||0,e.layoutCalculated=!0),t.left=e.offsetLeft||0,t.top=e.offsetTop||0,t.width=e.offsetWidth||0,t.height=e.offsetHeight||0,t.right=t.left+t.width,t.bottom=t.top+t.height,t.scrollTop=e.scrollTop,t.hidden=null===e.offsetParent&&"fixed"!==n.position,t},L="undefined"!=typeof window&&void 0!==window.document,P=function(){return L},G="children"in(P()?v("svg"):{})?function(t){return t.children.length}:function(t){return t.childNodes.length},D=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.tag,n=void 0===e?"div":e,r=t.name,i=void 0===r?null:r,o=t.attributes,l=void 0===o?{}:o,c=t.read,u=void 0===c?function(){}:c,s=t.write,f=void 0===s?function(){}:s,d=t.create,p=void 0===d?function(){}:d,g=t.destroy,m=void 0===g?function(){}:g,_=t.filterFrameActionsForChild,w=void 0===_?function(t,e){return e}:_,T=t.didCreateView,x=void 0===T?function(){}:T,R=t.didWriteView,A=void 0===R?function(){}:R,C=t.shouldUpdateChildViews,I=void 0===C?function(){return!0}:C,O=t.ignoreRect,M=void 0!==O&&O,L=t.ignoreRectUpdate,P=void 0!==L&&L,D=t.mixins,k=void 0===D?[]:D;return function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=v(n,i?"doka--".concat(i):null,l),o=window.getComputedStyle(r,null),c=b(),s=null,d=!1,g=[],_=[],T={},R={},C=[f],O=[u],L=[m],D=function(){return r},U=function(){return[].concat(g)},V=function(){return s||(s=E(c,g,[0,0],[1,1]))},B=function(){return r.layoutCalculated=!1},N={element:{get:D},style:{get:function(){return o}},childViews:{get:U}},z=a({},N,{rect:{get:V},ref:{get:function(){return T}},is:function(t){return i===t},appendChild:y(r),createChildView:function(t){return function(e,n){return e(t,n)}}(t),linkView:function(t){return g.push(t),t},unlinkView:function(t){g.splice(g.indexOf(t),1)},appendChildView:function(t,e){return function(t,n){return void 0!==n?e.splice(n,0,t):e.push(t),t}}(0,g),removeChildView:function(t,e){return function(n){return e.splice(e.indexOf(n),1),n.element.parentNode&&t.removeChild(n.element),n}}(r,g),registerWriter:function(t){return C.push(t)},registerReader:function(t){return O.push(t)},registerDestroyer:function(t){return L.push(t)},invalidateLayout:B,dispatch:t.dispatch,query:t.query}),F={element:{get:D},childViews:{get:U},rect:{get:V},resting:{get:function(){return d}},isRectIgnored:function(){return M},invalidateLayout:B,_read:function(){s=null,I({root:q,props:e})&&g.forEach(function(t){return t._read()}),!(P&&c.width&&c.height)&&b(c,r,o);var t={root:q,props:e,rect:c};O.forEach(function(e){return e(t)})},_write:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=0===n.length;return C.forEach(function(i){!1===i({props:e,root:q,actions:n,timestamp:t})&&(r=!1)}),_.forEach(function(e){!1===e.write(t)&&(r=!1)}),I({props:e,root:q,actions:n,timestamp:t})&&(g.filter(function(t){return!!t.element.parentNode}).forEach(function(e){e._write(t,w(e,n))||(r=!1)}),g.forEach(function(e,i){e.element.parentNode||(q.appendChild(e.element,i),e._read(),e._write(t,w(e,n)),r=!1)})),d=r,A({props:e,root:q,actions:n,timestamp:t}),r},_destroy:function(){_.forEach(function(t){return t.destroy()}),L.forEach(function(t){t({root:q})}),g.forEach(function(t){return t._destroy()})}},W=a({},N,{rect:{get:function(){return c}}});Object.keys(k).sort(function(t,e){return"styles"===t?1:"styles"===e?-1:0}).forEach(function(t){var n=S[t]({mixinConfig:k[t],viewProps:e,viewState:R,viewInternalAPI:z,viewExternalAPI:F,view:h(W)});n&&_.push(n)});var q=h(z);p({root:q,props:e});var H=G(r)||0;return g.forEach(function(t,e){q.appendChild(t.element,H+e)}),x(q),h(F)}},k=function(t,e){return function(n){var r=n.root,i=n.props,o=n.actions,a=void 0===o?[]:o,l=n.timestamp;if(a.filter(function(e){return t[e.type]}).forEach(function(e){return t[e.type]({root:r,props:i,action:e.data,timestamp:l})}),e)return e({root:r,props:i,actions:a,timestamp:l})}},U=function(t){return Array.isArray(t)},V=function(t){return null==t},B=function(t){return t.trim()},N=function(t){return""+t},z=function(t){return"boolean"==typeof t},F=function(t){return"string"==typeof t},W=function(t){return T(t)?t:F(t)?N(t).replace(/[a-z]+/gi,""):0},q=function(t){return parseInt(W(t),10)},H=function(t){return T(t)&&isFinite(t)&&Math.floor(t)===t},Y=function(t){if(H(t))return t;var e=N(t).trim();return/MB$/i.test(e)?(e=e.replace(/MB$i/,"").trim(),1e3*q(e)*1e3):/KB/i.test(e)?(e=e.replace(/KB$i/,"").trim(),1e3*q(e)):q(e)},Z=function(t){return U(t)?"array":function(t){return null===t}(t)?"null":H(t)?"int":/^[0-9]+ ?(?:GB|MB|KB)$/gi.test(t)?"bytes":i(t)},X={array:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:",";return V(t)?[]:U(t)?t:N(t).split(e).map(B).filter(function(t){return t.length})},boolean:function(t){return z(t)?t:"true"===t},int:function(t){return"bytes"===Z(t)?Y(t):q(t)},float:function(t){return parseFloat(W(t))},bytes:Y,string:function(t){return function(t){return"function"==typeof t}(t)?t:N(t)},object:function(t){try{return JSON.parse(function(t){return t.replace(/{\s*'/g,'{"').replace(/'\s*}/g,'"}').replace(/'\s*:/g,'":').replace(/:\s*'/g,':"').replace(/,\s*'/g,',"').replace(/'\s*,/g,'",')}(t))}catch(e){return t}},file:function(t){return t},function:function(t){return function(t){for(var e=self,n=t.split("."),r=null;r=n.shift();)if(!(e=e[r]))return null;return e}(t)}},K=function(t,e,n){if(t===e)return t;var r=Z(t);if(r!==n){var i=function(t,e){return X[e](t)}(t,n);if(r=Z(i),null===i)throw'Trying to assign value with incorrect type, allowed type: "'.concat(n,'"');t=i}return t},j=function(t){var e={};return d(t,function(n){var r=F(t[n])?t[n]:n,i=t[r];e[n]=r===n?function(t,e){var n=t;return{enumerable:!0,get:function(){return n},set:function(r){n=K(r,t,e)}}}(i[0],i[1]):e[r]}),h(e)},Q=function(t){t.file=null,t.activeView=null,t.markup=[],t.markupToolValues={},t.rootRect={x:0,y:0,left:0,top:0,width:0,height:0},t.stage=null,t.stageOffset=null,t.image=null,t.zoomTimeoutId=null,t.instantUpdate=!1,t.filePromise=null,t.fileLoader=null,t.instructions={size:null,crop:null,filter:null,color:null},t.filter=null,t.filterName=null,t.filterValue=null,t.colorValues={},t.colorMatrices={},t.size={width:!1,height:!1,aspectRatioLocked:!0,aspectRatioPrevious:!1},t.crop={rectangle:null,transforms:null,rotation:null,flip:null,aspectRatio:null,isRotating:!1,isDirty:!1,limitToImageBounds:!0,draft:{rectangle:null,transforms:null}}},J=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"-";return t.split(/(?=[A-Z])/).map(function(t){return t.toLowerCase()}).join(e)},$=function(){return Math.random().toString(36).substr(2,9)},tt=function(){var t=[],e=function(e,n){!function(t,e){t.splice(e,1)}(t,t.findIndex(function(t){return t.event===e&&(t.cb===n||!n)}))};return{fire:function(e){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];t.filter(function(t){return t.event===e}).map(function(t){return t.cb}).forEach(function(t){setTimeout(function(){t.apply(void 0,r)},0)})},on:function(e,n){t.push({event:e,cb:n})},onOnce:function(n,r){t.push({event:n,cb:function(){e(n,r),r.apply(void 0,arguments)}})},off:e}},et="boolean",nt="int",rt="number",it="string",ot="array",at="object",lt="function",ct=null,ut=function(){return null===ct&&(ct=(/iPad|iPhone|iPod/.test(navigator.userAgent)||"MacIntel"===navigator.platform&&navigator.maxTouchPoints>1)&&!window.MSStream),ct},st=function(){return a({},dt)},ft=function(t,e){t=function(t){return F(dt[t])?dt[t]:t}(t),dt[t][0]=K(e,dt[t][0],dt[t][1])},dt={id:[null,it],className:[null,it],src:[null,"file"],storageName:["doka",it],maxImagePreviewWidth:[1500,nt],maxImagePreviewHeight:[1500,nt],allowPreviewFitToView:[!0,et],allowButtonCancel:[!0,et],allowButtonConfirm:[!0,et],allowDropFiles:[!1,et],allowBrowseFiles:[!0,et],allowAutoClose:[!0,et],allowAutoDestroy:[!1,et],utils:[["crop","filter","color","markup"],ot],util:[null,it],initialState:[null,at],outputData:[!1,et],outputFile:[!0,et],outputCorrectImageExifOrientation:[!0,et],outputStripImageHead:[!0,et],outputType:[null,it],outputQuality:[null,nt],outputFit:["cover",it],outputUpscale:[!0,et],outputWidth:[null,nt],outputHeight:[null,nt],outputCanvasBackgroundColor:[null,it],outputCanvasMemoryLimit:[P()&&ut()?16777216:null,nt],size:[null,at],sizeMin:[{width:1,height:1},at],sizeMax:[{width:9999,height:9999},at],filter:[null,at],filters:[{original:{label:"Original",matrix:function(){return null}},chrome:{label:"Chrome",matrix:function(){return[1.398,-.316,.065,-.273,.201,-.051,1.278,-.08,-.273,.201,-.051,.119,1.151,-.29,.215,0,0,0,1,0]}},fade:{label:"Fade",matrix:function(){return[1.073,-.015,.092,-.115,-.017,.107,.859,.184,-.115,-.017,.015,.077,1.104,-.115,-.017,0,0,0,1,0]}},mono:{label:"Mono",matrix:function(){return[.212,.715,.114,0,0,.212,.715,.114,0,0,.212,.715,.114,0,0,0,0,0,1,0]}},noir:{label:"Noir",matrix:function(){return[.15,1.3,-.25,.1,-.2,.15,1.3,-.25,.1,-.2,.15,1.3,-.25,.1,-.2,0,0,0,1,0]}}},at],crop:[null,at],cropShowSize:[!1,et],cropZoomTimeout:[null,nt],cropMask:[null,lt],cropMaskInset:[0,nt],cropAllowImageTurnLeft:[!0,et],cropAllowImageTurnRight:[!1,et],cropAllowImageFlipHorizontal:[!0,et],cropAllowImageFlipVertical:[!0,et],cropAllowToggleLimit:[!1,et],cropLimitToImageBounds:[!0,et],cropAllowInstructionZoom:[!1,et],cropAllowRotate:[!0,et],cropResizeMatchImageAspectRatio:[!1,et],cropResizeKeyCodes:[[18,91,92,93],ot],cropResizeScrollRectOnly:[!1,et],cropAspectRatio:[null,it],cropAspectRatioOptions:[null,ot],cropMinImageWidth:[1,nt],cropMinImageHeight:[1,nt],colorBrightness:[0,rt],colorBrightnessRange:[[-.25,.25],ot],colorContrast:[1,rt],colorContrastRange:[[.5,1.5],ot],colorExposure:[1,rt],colorExposureRange:[[.5,1.5],ot],colorSaturation:[1,rt],colorSaturationRange:[[0,2],ot],markup:[null,ot],markupUtil:["select",it],markupFilter:[function(){return!0},lt],markupAllowAddMarkup:[!0,et],markupAllowCustomColor:[!0,et],markupDrawDistance:[4,rt],markupColor:["#000",it],markupColorOptions:[[["White","#fff","#f6f6f6"],["Silver","#9e9e9e"],["Black","#000","#333"],["Red","#f44336"],["Orange","#ff9800"],["Yellow","#ffeb3b"],["Green","#4caf50"],["Blue","#2196f3"],["Violet","#3f51b5"],["Purple","#9c27b0"]],ot],markupFontSize:[.1,rt],markupFontSizeOptions:[[["XL",.15],["L",.125],["M",.1],["S",.075],["XS",.05]],ot],markupFontFamily:["Helvetica, Arial, Verdana",it],markupFontFamilyOptions:[[["Serif","Palatino, 'Times New Roman', serif"],["Sans Serif","Helvetica, Arial, Verdana"],["Monospaced","Monaco, 'Lucida Console', monospaced"]],ot],markupShapeStyle:[[.015,null],ot],markupShapeStyleOptions:[[["Fill",0,null,0],["Outline thick",.025,null,4],["Outline default",.015,null,2],["Outline thin",.005,null,1],["Outline dashed",.005,[.01],1]],ot],markupLineStyle:[[.015,null],ot],markupLineStyleOptions:[[["Thick",.025,null,4],["Default",.015,null,2],["Thin",.005,null,1],["Dashed",.005,[.01],1]],ot],markupLineDecoration:[["arrow-end"],ot],markupLineDecorationOptions:[[["None",[]],["Single arrow",["arrow-end"]],["Double arrow",["arrow-begin","arrow-end"]]],ot],beforeCreateBlob:[null,lt],afterCreateBlob:[null,lt],afterCreateOutput:[null,lt],onconfirm:[null,lt],oncancel:[null,lt],onclose:[null,lt],onloadstart:[null,lt],onload:[null,lt],onloaderror:[null,lt],onupdate:[null,lt],oninit:[null,lt],ondestroy:[null,lt],labelButtonReset:["Reset",it],labelButtonCancel:["Cancel",it],labelButtonConfirm:["Done",it],labelButtonUtilCrop:["Crop",it],labelButtonUtilResize:["Resize",it],labelButtonUtilFilter:["Filter",it],labelButtonUtilColor:["Colors",it],labelButtonUtilMarkup:["Markup",it],labelStatusMissingWebGL:["WebGL is required but is disabled on your browser",it],labelStatusAwaitingImage:["Waiting for image…",it],labelStatusLoadImageError:["Error loading image…",it],labelStatusLoadingImage:["Loading image…",it],labelStatusProcessingImage:["Processing image…",it],labelColorBrightness:["Brightness",it],labelColorContrast:["Contrast",it],labelColorExposure:["Exposure",it],labelColorSaturation:["Saturation",it],labelMarkupTypeRectangle:["Square",it],labelMarkupTypeEllipse:["Circle",it],labelMarkupTypeText:["Text",it],labelMarkupTypeLine:["Arrow",it],labelMarkupSelectFontSize:["Size",it],labelMarkupSelectFontFamily:["Font",it],labelMarkupSelectLineDecoration:["Decoration",it],labelMarkupSelectLineStyle:["Style",it],labelMarkupSelectShapeStyle:["Style",it],labelMarkupRemoveShape:["Remove",it],labelMarkupToolSelect:["Select",it],labelMarkupToolDraw:["Draw",it],labelMarkupToolLine:["Arrow",it],labelMarkupToolText:["Text",it],labelMarkupToolRect:["Square",it],labelMarkupToolEllipse:["Circle",it],labelResizeWidth:["Width",it],labelResizeHeight:["Height",it],labelResizeApplyChanges:["Apply",it],labelCropInstructionZoom:["Zoom in and out with your scroll wheel or touchpad.",it],labelButtonCropZoom:["Zoom",it],labelButtonCropRotateLeft:["Rotate left",it],labelButtonCropRotateRight:["Rotate right",it],labelButtonCropRotateCenter:["Center rotation",it],labelButtonCropFlipHorizontal:["Flip horizontal",it],labelButtonCropFlipVertical:["Flip vertical",it],labelButtonCropAspectRatio:["Aspect ratio",it],labelButtonCropToggleLimit:["Crop selection",it],labelButtonCropToggleLimitEnable:["Limited to image",it],labelButtonCropToggleLimitDisable:["Select outside image",it],pointerEventsPolyfillScope:["root",it],styleCropCorner:["circle",it],styleFullscreenSafeArea:[P()&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream?"bottom":"none",it],styleLayoutMode:[null,it]},ht=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;return Math.min(n,Math.max(e,t))},pt=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return parseFloat(t.toFixed(e))},gt=function(t,e){return{x:pt(t.x,e),y:pt(t.y,e)}},mt=function(t,e){return wt(t.x-e.x,t.y-e.y)},vt=function(t,e){return function(t,e){return t.x*e.x+t.y*e.y}(mt(t,e),mt(t,e))},yt=function(t,e){return Math.sqrt(vt(t,e))},Et=function(t,e){return wt(ht(t.x,e.x,e.x+e.width),ht(t.y,e.y,e.y+e.height))},_t=function(t,e,n){var r=Math.cos(e),i=Math.sin(e),o=wt(t.x-n.x,t.y-n.y);return wt(n.x+r*o.x-i*o.y,n.y+i*o.x+r*o.y)},wt=function(){return{x:arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,y:arguments.length>1&&void 0!==arguments[1]?arguments[1]:0}},Tt=function(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height},xt=function(t,e){return St(t.x+e.x,t.y+e.y,t.width,t.height)},Rt={translate:xt,rotate:function(t,e,n){var r=function(t,e,n){return 0===e?{tl:t.tl,tr:t.tr,br:t.br,bl:t.bl}:{tl:_t(t.tl,e,n),tr:_t(t.tr,e,n),br:_t(t.br,e,n),bl:_t(t.bl,e,n)}}(Mt(t),e,n),i=r.tl,o=r.tr,a=r.br,l=r.bl,c=Math.min(i.x,o.x,a.x,l.x),u=Math.min(i.y,o.y,a.y,l.y),s=Math.max(i.x,o.x,a.x,l.x),f=Math.max(i.y,o.y,a.y,l.y);return St(c,u,s-c,f-u)},scale:function(t,e,n){return St(e*(t.x-n.x)+n.x,e*(t.y-n.y)+n.y,e*t.width,e*t.height)}},At=function(t){return St(t.x,t.y,t.width,t.height)},Ct=function(t){return{top:t.y,right:t.x+t.width,bottom:t.y+t.height,left:t.x}},It=function(t){var e=t.top,n=t.right,r=t.bottom,i=t.left;return{x:i,y:e,width:n-i,height:r-e}},Ot=function(t){return wt(t.x+.5*t.width,t.y+.5*t.height)},Mt=function(t){return{tl:{x:t.x,y:t.y},tr:{x:t.x+t.width,y:t.y},br:{x:t.x+t.width,y:t.y+t.height},bl:{x:t.x,y:t.y+t.height}}},St=function(t,e,n,r){return{x:t,y:e,width:n,height:r}},bt=function(t){if(V(t))return t;if(/:/.test(t)){var e=t.split(":"),n=e[0];return e[1]/n}return parseFloat(t)},Lt=function(t,e){var n=t.width,r=n*e;return r>t.height&&(n=(r=t.height)/e),{x:.5*(t.width-n),y:.5*(t.height-r),width:n,height:r}},Pt=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=t.height/t.width,i=e,o=1,a=r;a>i&&(o=(a=i)/r);var l=Math.max(1/o,i/a),c=t.width/(n*l*o);return{width:c,height:c*e}},Gt=function(t,e){return{x:t,y:e}},Dt=function(t,e){return Gt(t.x-e.x,t.y-e.y)},kt=function(t,e){return Math.sqrt(function(t,e){return function(t,e){return t.x*e.x+t.y*e.y}(Dt(t,e),Dt(t,e))}(t,e))},Ut=function(t,e){var n=t,r=e,i=1.5707963267948966-e,o=Math.sin(1.5707963267948966),a=Math.sin(r),l=Math.sin(i),c=Math.cos(i),u=n/o;return Gt(c*(u*a),c*(u*l))},Vt=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{x:.5,y:.5},i=r.x>.5?1-r.x:r.x,o=r.y>.5?1-r.y:r.y,a=2*i*t.width,l=2*o*t.height,c=function(t,e){var n=t.width,r=t.height,i=Ut(n,e),o=Ut(r,e),a=Gt(t.x+Math.abs(i.x),t.y-Math.abs(i.y)),l=Gt(t.x+t.width+Math.abs(o.y),t.y+Math.abs(o.x)),c=Gt(t.x-Math.abs(o.y),t.y+t.height-Math.abs(o.x));return{width:kt(a,l),height:kt(a,c)}}(e,n);return Math.max(c.width/a,c.height/l)},Bt=function(t,e){var n=e.origin,r=e.translation;return function(t,e,n){return e.reduce(function(t,e){return(0,Rt[e[0]])(t,e[1],n)},t)}(t,[["scale",e.scale],["translate",r]],n)},Nt=function(t,e){var n=t,r=e,i=1.5707963267948966-e,o=Math.sin(1.5707963267948966),a=Math.sin(r),l=Math.sin(i),c=Math.cos(i),u=n/o;return wt(c*(u*a),c*(u*l))},zt=function(t,e){var n=t.width,r=t.height,i=e%(Math.PI/2),o=Nt(n,i),a=Nt(r,i),l=Mt(t);return{tl:wt(l.tl.x+Math.abs(o.x),l.tl.y-Math.abs(o.y)),tr:wt(l.tr.x+Math.abs(a.y),l.tr.y+Math.abs(a.x)),br:wt(l.br.x-Math.abs(o.x),l.br.y+Math.abs(o.y)),bl:wt(l.bl.x-Math.abs(a.y),l.bl.y-Math.abs(a.x))}},Ft=function(t,e,n){var r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],i=n.origin,o=n.translation,a=Bt(t,n),l={x:i.x+o.x,y:i.y+o.y},c=2*Math.PI+n.rotation%(2*Math.PI),u=zt(e,c),s=_t(u.tl,-c,l),f=_t(u.tr,-c,l),d=_t(u.br,-c,l),h={x:Math.min(s.x,f.x,d.x),y:Math.min(s.y,f.y,d.y),width:Math.max(s.x,f.x,d.x)-Math.min(s.x,f.x,d.x),height:Math.max(s.y,f.y,d.y)-Math.min(s.y,f.y,d.y)},p=St(h.x,h.y,h.width,h.height),g=Ot(p),m=e.height/e.width,v={x:(g.x-a.x)/a.width,y:(g.y-a.y)/a.height},y=v.y>.5?1-v.y:v.y,E=2*(v.x>.5?1-v.x:v.x)*a.width,_=2*y*a.height;return{center:v,zoom:r?Math.min(E/p.width,_/p.height):Math.min(a.width/p.width,a.height/p.height),rotation:n.rotation,aspectRatio:m,scaleToFit:r}},Wt=function(t,e){var n=qt(t,e);return{center:{x:pt(n.center.x),y:pt(n.center.y)},rotation:pt(n.rotation),zoom:pt(n.zoom),aspectRatio:pt(n.aspectRatio),flip:a({},e.flip),scaleToFit:n.scaleToFit}},qt=function(t,e){var n=Ft(t,e.rectangle,e.transforms,e.limitToImageBounds);return{center:{x:n.center.x,y:n.center.y},rotation:n.rotation,zoom:n.zoom,aspectRatio:n.aspectRatio,flip:a({},e.flip),scaleToFit:n.scaleToFit}},Ht=function(t,e,n,r){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"width",o=t.width,a=t.height;return o||a?(o=o&&ht(o,e.width,n.width),a=a&&ht(a,e.height,n.height),r?(a?o?"width"===i?a=o/r:"height"===i?o=a*r:(a*r<e.width?a=(o=e.width)/r:o/r<e.height&&(o=(a=e.height)*r),a*r>n.width?a=(o=n.width)/r:o/r>n.height&&(o=(a=n.height)*r)):a=ht(a*r,e.width,n.width)/r:o=ht(o/r,e.height,n.height)*r,{width:o,height:a}):{width:o,height:a}):{width:o,height:a}},Yt=function(t){var e;if(/^#/.test(t)){if(4===t.length){var n=t.split("");t="#"+n[1]+n[1]+n[2]+n[2]+n[3]+n[3]}var r=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(t);e=[parseInt(r[1],16),parseInt(r[2],16),parseInt(r[3],16)]}else/^rgb/.test(t)&&((e=t.match(/\d+/g).map(function(t){return parseInt(t,10)})).length=3);return e},Zt=[],Xt=function(t){var e=[];return d(t,function(t,n){return e.push(n)}),e.length?e.reduce(function(t,e){return function(t,e){var n=new Array(20);return n[0]=t[0]*e[0]+t[1]*e[5]+t[2]*e[10]+t[3]*e[15],n[1]=t[0]*e[1]+t[1]*e[6]+t[2]*e[11]+t[3]*e[16],n[2]=t[0]*e[2]+t[1]*e[7]+t[2]*e[12]+t[3]*e[17],n[3]=t[0]*e[3]+t[1]*e[8]+t[2]*e[13]+t[3]*e[18],n[4]=t[0]*e[4]+t[1]*e[9]+t[2]*e[14]+t[3]*e[19]+t[4],n[5]=t[5]*e[0]+t[6]*e[5]+t[7]*e[10]+t[8]*e[15],n[6]=t[5]*e[1]+t[6]*e[6]+t[7]*e[11]+t[8]*e[16],n[7]=t[5]*e[2]+t[6]*e[7]+t[7]*e[12]+t[8]*e[17],n[8]=t[5]*e[3]+t[6]*e[8]+t[7]*e[13]+t[8]*e[18],n[9]=t[5]*e[4]+t[6]*e[9]+t[7]*e[14]+t[8]*e[19]+t[9],n[10]=t[10]*e[0]+t[11]*e[5]+t[12]*e[10]+t[13]*e[15],n[11]=t[10]*e[1]+t[11]*e[6]+t[12]*e[11]+t[13]*e[16],n[12]=t[10]*e[2]+t[11]*e[7]+t[12]*e[12]+t[13]*e[17],n[13]=t[10]*e[3]+t[11]*e[8]+t[12]*e[13]+t[13]*e[18],n[14]=t[10]*e[4]+t[11]*e[9]+t[12]*e[14]+t[13]*e[19]+t[14],n[15]=t[15]*e[0]+t[16]*e[5]+t[17]*e[10]+t[18]*e[15],n[16]=t[15]*e[1]+t[16]*e[6]+t[17]*e[11]+t[18]*e[16],n[17]=t[15]*e[2]+t[16]*e[7]+t[17]*e[12]+t[18]*e[17],n[18]=t[15]*e[3]+t[16]*e[8]+t[17]*e[13]+t[18]*e[18],n[19]=t[15]*e[4]+t[16]*e[9]+t[17]*e[14]+t[18]*e[19]+t[19],n}(c(t),e)},e.shift()):[]},Kt=function(t){return t.crop.draft.transforms?t.crop.draft.transforms.scale:t.crop.transforms.scale},jt=function(t){var e=t.image.width/t.image.naturalWidth,n=Kt(t);return{width:t.options.cropMinImageWidth*n*e,height:t.options.cropMinImageHeight*n*e}},Qt=function(t){var e=Kt(t);return{width:t.image.width*e,height:t.image.height*e}},Jt=function(t){return t.options.cropAspectRatioOptions?t.options.cropAspectRatioOptions.map(function(t){var e={aspectRatio:null,width:null,height:null};return"number"==typeof t.value&&(e.aspectRatio=t.value),"string"==typeof t.value&&(e.aspectRatio=bt(t.value)),"object"===i(t.value)&&null!==t.value&&(e.width=t.value.width,e.height=t.value.height,e.aspectRatio=e.height/e.width),{label:t.label,value:e}}):null},$t=function(t){return{ALLOW_MANUAL_RESIZE:function(){return t.options.utils.includes("resize")},GET_SIZE:function(){return!1!==t.size.width&&!1!==t.size.height?{width:t.size.width,height:t.size.height}:{width:null,height:null}},GET_SIZE_INPUT:function(){return{width:t.size.width,height:t.size.height}},GET_SIZE_ASPECT_RATIO_LOCK:function(){return t.size.aspectRatioLocked},IS_ACTIVE_VIEW:function(e){return t.activeView===e},GET_ACTIVE_VIEW:function(){return t.activeView},GET_STYLES:function(){return Object.keys(t.options).filter(function(t){return/^style/.test(t)}).map(function(e){return{name:e,value:t.options[e]}})},GET_FILE:function(){return t.file},GET_IMAGE:function(){return t.image},GET_STAGE:function(){return a({},t.stage,t.stageOffset)},GET_ROOT:function(){return t.rootRect},GET_ROOT_SIZE:function(){return{width:t.rootRect.width,height:t.rootRect.height}},GET_MIN_IMAGE_SIZE:function(){return{width:t.options.cropMinImageWidth,height:t.options.cropMinImageHeight}},GET_IMAGE_PREVIEW_SCALE_FACTOR:function(){return t.image.width/t.image.naturalWidth},GET_MIN_PREVIEW_IMAGE_SIZE:function(){var e=t.image.width/t.image.naturalWidth;return{width:t.options.cropMinImageWidth*e,height:t.options.cropMinImageHeight*e}},GET_MIN_CROP_SIZE:function(){return jt(t)},GET_MAX_CROP_SIZE:function(){return Qt(t)},GET_MIN_PIXEL_CROP_SIZE:function(){var e=t.crop.transforms.scale,n=jt(t);return{width:n.width/e,height:n.height/e}},GET_MAX_PIXEL_CROP_SIZE:function(){var e=t.crop.transforms.scale,n=Qt(t);return{width:n.width/e,height:n.height/e}},GET_CROP_ASPECT_RATIO_OPTIONS:function(){return Jt(t)},GET_ACTIVE_CROP_ASPECT_RATIO:function(){var e=t.crop.aspectRatio;return F(e)?bt(e):e},GET_CROP_ASPECT_RATIO:function(){var e=F(t.options.cropAspectRatio)?bt(t.options.cropAspectRatio):t.options.cropAspectRatio,n=Jt(t);return!n||n&&!n.length?e:n.find(function(t){return t.value.aspectRatio===e})?e:n[0].value.aspectRatio},GET_CROP_RECTANGLE_ASPECT_RATIO:function(){var e=t.crop,n=e.rectangle,r=e.aspectRatio;return n?n.width/n.height:r},GET_CROP:function(e,n){var r=Zt[e];if(r&&r.ts===n)return r;var i=re(t);return i&&(i.ts=n,Zt[e]=i),i},GET_COLOR_MATRIX:function(){return Xt(t.colorMatrices)},GET_COLOR_VALUES:function(){return a({exposure:t.options.colorExposure,brightness:t.options.colorBrightness,contrast:t.options.colorContrast,saturation:t.options.colorSaturation},t.colorValues)},GET_MARKUP_TOOL_VALUES:function(){return a({color:t.options.markupColor,fontFamily:t.options.markupFontFamily,fontSize:t.options.markupFontSize,shapeStyle:t.options.markupShapeStyle,lineStyle:t.options.markupLineStyle,lineDecoration:t.options.markupLineDecoration},t.markupToolValues)},GET_PREVIEW_IMAGE_DATA:function(){return t.file.preview},GET_THUMB_IMAGE_DATA:function(){return t.file.thumb},GET_FILTER:function(){return t.filter},GET_UID:function(){return t.uid},GET_MARKUP_BY_ID:function(e){return t.markup.find(function(t){return t[1].id===e})},GET_BACKGROUND_COLOR:function(){var e=t.options.outputCanvasBackgroundColor;if(!e)return ee;if(te[e])return te[e];var n=Yt(e);return te[e]=n.concat(1),te[e]}}},te={},ee=[0,0,0,0],ne=function(t,e){var n=Ot(e);return!function(t,e){return pt(t.x)===pt(e.x)&&pt(t.y)===pt(e.y)}(Ot(t),n)},re=function(t){if(!t.stage||!t.image)return null;var e=t.crop.draft.rectangle||{free:t.crop.rectangle,limited:t.crop.rectangle},n=t.crop.draft.transforms||t.crop.transforms,r=n.origin,i=n.translation,o=n.scale,a=n.interaction,l=t.crop.rotation,c=t.crop.flip,u=!(!t.crop.draft.rectangle&&!t.crop.draft.transforms),s=u||t.instantUpdate,f=ne(e.limited,t.stage),h=t.crop.isDirty||u,p=t.crop.isRotating,g=void 0===t.crop.limitToImageBounds||t.crop.limitToImageBounds,m={width:t.image.naturalWidth,height:t.image.naturalHeight},v=Xt(t.colorMatrices),y=qt(t.image,{rectangle:e.limited,transforms:{origin:r,translation:i,scale:o,rotation:l.main+l.sub},flip:c,limitToImageBounds:t.crop.limitToImageBounds}),E={props:y,crop:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=e.zoom,i=e.rotation,o=e.center,a=e.aspectRatio,l=Pt(t,a,r),c={x:.5*l.width,y:.5*l.height},u={x:0,y:0,width:l.width,height:l.height,center:c},s=r*Vt(t,Lt(u,a),i,n?o:{x:.5,y:.5});return{widthFloat:l.width/s,heightFloat:l.height/s,width:Math.round(l.width/s),height:Math.round(l.height/s)}}(m,y,t.crop.limitToImageBounds),image:function(t,e){var n=function(t){return!1===t.size.width?function(t){return t.options.size?t.options.size.width:null}(t):t.size.width}(t),r=function(t){return!1===t.size.height?function(t){return t.options.size?t.options.size.height:null}(t):t.size.height}(t),i=e.width/e.height;return Ht({width:n,height:r},t.options.sizeMin,t.options.sizeMax,i)}(t,e.limited)},_=E.image,w=E.crop,T=w.width,x=w.height,R=w.widthFloat/w.heightFloat;_.width&&_.height?(T=_.width,x=_.height):_.width&&!_.height?(T=_.width,x=_.width/R):_.height&&!_.width&&(x=_.height,T=_.height*R),E.currentWidth=Math.round(T),E.currentHeight=Math.round(x);var A={x:0,y:0},C=0,I=0;if(s&&a){if(a.translation){var O=a.translation.x-i.x,M=a.translation.y-i.y;A.x=100*Math.sign(O)*Math.log10(1+Math.abs(O)/100),A.y=100*Math.sign(M)*Math.log10(1+Math.abs(M)/100)}if(a.scale){var S=a.scale-o;C=.25*Math.sign(S)*Math.log10(1+Math.abs(S)/.25)}if(a.rotation){var b=a.rotation-(l.main+l.sub);I=.05*Math.sign(b)*Math.log10(1+Math.abs(b)/.05)}}var L={},P=e.free,G=Ct(P),D=Ct(e.limited);return d(G,function(t){var e=G[t]-D[t];L[t]=D[t]+5*Math.sign(e)*Math.log10(1+Math.abs(e)/5)}),{canRecenter:f,canReset:h,isDraft:s,isRotating:p,isLimitedToImageBounds:g,cropRect:{x:L.left,y:L.top,width:L.right-L.left,height:L.bottom-L.top},origin:r,translation:i,translationBand:A,scale:o,scaleBand:C,rotation:l,rotationBand:I,flip:c,interaction:a,cropStatus:E,colorMatrix:v,markup:t.markup}},ie={1:function(){return[1,0,0,1,0,0]},2:function(t){return[-1,0,0,1,t,0]},3:function(t,e){return[-1,0,0,-1,t,e]},4:function(t,e){return[1,0,0,-1,0,e]},5:function(){return[0,1,1,0,0,0]},6:function(t,e){return[0,1,-1,0,e,0]},7:function(t,e){return[0,-1,-1,0,e,t]},8:function(t){return[0,-1,1,0,0,t]}},oe=function(t){t.width=1,t.height=1,t.getContext("2d").clearRect(0,0,1,1)},ae=function(t){return t&&(t.horizontal||t.vertical)},le=function(t,e,n){if(e<=1&&!ae(n))return t.width=t.naturalWidth,t.height=t.naturalHeight,t;var r=document.createElement("canvas"),i=t.naturalWidth,o=t.naturalHeight,a=e>=5&&e<=8;a?(r.width=o,r.height=i):(r.width=i,r.height=o);var l=r.getContext("2d");if(e&&l.transform.apply(l,function(t,e,n){return-1===n&&(n=1),ie[n](t,e)}(i,o,e)),ae(n)){var c=[1,0,0,1,0,0];(!a&&n.horizontal||a&n.vertical)&&(c[0]=-1,c[4]=i),(!a&&n.vertical||a&&n.horizontal)&&(c[3]=-1,c[5]=o),l.transform.apply(l,c)}return l.drawImage(t,0,0,i,o),r};"undefined"!=typeof window&&void 0!==window.document&&(HTMLCanvasElement.prototype.toBlob||Object.defineProperty(HTMLCanvasElement.prototype,"toBlob",{value:function(t,e,n){var r=this.toDataURL(e,n).split(",")[1];setTimeout(function(){for(var n=atob(r),i=n.length,o=new Uint8Array(i),a=0;a<i;a++)o[a]=n.charCodeAt(a);t(new Blob([o],{type:e||"image/png"}))})}}));var ce=function(t,e){return de(t.x*e,t.y*e)},ue=function(t,e){return de(t.x+e.x,t.y+e.y)},se=function(t){var e=Math.sqrt(t.x*t.x+t.y*t.y);return 0===e?{x:0,y:0}:de(t.x/e,t.y/e)},fe=function(t,e,n){var r=Math.cos(e),i=Math.sin(e),o=de(t.x-n.x,t.y-n.y);return de(n.x+r*o.x-i*o.y,n.y+i*o.x+r*o.y)},de=function(){return{x:arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,y:arguments.length>1&&void 0!==arguments[1]?arguments[1]:0}},he=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3?arguments[3]:void 0;return"string"==typeof t?parseFloat(t)*n:"number"==typeof t?t*(r?e[r]:Math.min(e.width,e.height)):void 0},pe=function(t,e,n){var r=t.borderStyle||t.lineStyle||"solid",i=t.backgroundColor||t.fontColor||"transparent",o=t.borderColor||t.lineColor||"transparent",a=he(t.borderWidth||t.lineWidth,e,n);return{"stroke-linecap":t.lineCap||"round","stroke-linejoin":t.lineJoin||"round","stroke-width":a||0,"stroke-dasharray":"string"==typeof r?"":r.map(function(t){return he(t,e,n)}).join(","),stroke:o,fill:i,opacity:t.opacity||1}},ge=function(t){return null!=t},me=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=he(t.x,e,n,"width")||he(t.left,e,n,"width"),i=he(t.y,e,n,"height")||he(t.top,e,n,"height"),o=he(t.width,e,n,"width"),a=he(t.height,e,n,"height"),l=he(t.right,e,n,"width"),c=he(t.bottom,e,n,"height");return ge(i)||(i=ge(a)&&ge(c)?e.height-a-c:c),ge(r)||(r=ge(o)&&ge(l)?e.width-o-l:l),ge(o)||(o=ge(r)&&ge(l)?e.width-r-l:0),ge(a)||(a=ge(i)&&ge(c)?e.height-i-c:0),{x:r||0,y:i||0,width:o||0,height:a||0}},ve=function(t){return t.map(function(t,e){return"".concat(0===e?"M":"L"," ").concat(t.x," ").concat(t.y)}).join(" ")},ye=function(t,e){return Object.keys(e).forEach(function(n){return t.setAttribute(n,e[n])})},Ee=function(t,e){var n=document.createElementNS("http://www.w3.org/2000/svg",t);return e&&ye(n,e),n},_e={contain:"xMidYMid meet",cover:"xMidYMid slice"},we={left:"start",center:"middle",right:"end"},Te=function(t){return function(e){return Ee(t,{id:e.id})}},xe={image:function(t){var e=Ee("image",{id:t.id,"stroke-linecap":"round","stroke-linejoin":"round",opacity:"0"});return e.onload=function(){e.setAttribute("opacity",t.opacity||1)},e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",t.src),e},rect:Te("rect"),ellipse:Te("ellipse"),text:Te("text"),path:Te("path"),line:function(t){var e=Ee("g",{id:t.id,"stroke-linecap":"round","stroke-linejoin":"round"}),n=Ee("line");e.appendChild(n);var r=Ee("path");e.appendChild(r);var i=Ee("path");return e.appendChild(i),e}},Re={rect:function(t){return ye(t,a({},t.rect,t.styles))},ellipse:function(t){var e=t.rect.x+.5*t.rect.width,n=t.rect.y+.5*t.rect.height,r=.5*t.rect.width,i=.5*t.rect.height;return ye(t,a({cx:e,cy:n,rx:r,ry:i},t.styles))},image:function(t,e){ye(t,a({},t.rect,t.styles,{preserveAspectRatio:_e[e.fit]||"none"}))},text:function(t,e,n,r){var i=he(e.fontSize,n,r),o=e.fontFamily||"sans-serif",l=e.fontWeight||"normal",c=we[e.textAlign]||"start";ye(t,a({},t.rect,t.styles,{"stroke-width":0,"font-weight":l,"font-size":i,"font-family":o,"text-anchor":c})),t.text!==e.text&&(t.text=e.text,t.textContent=e.text.length?e.text:" ")},path:function(t,e,n,r){ye(t,a({},t.styles,{fill:"none",d:ve(e.points.map(function(t){return{x:he(t.x,n,r,"width"),y:he(t.y,n,r,"height")}}))}))},line:function(t,e,n,r){ye(t,a({},t.rect,t.styles,{fill:"none"}));var i=t.childNodes[0],o=t.childNodes[1],l=t.childNodes[2],c=t.rect,u={x:t.rect.x+t.rect.width,y:t.rect.y+t.rect.height};if(ye(i,{x1:c.x,y1:c.y,x2:u.x,y2:u.y}),e.lineDecoration){o.style.display="none",l.style.display="none";var s=se({x:u.x-c.x,y:u.y-c.y}),f=he(.05,n,r);if(-1!==e.lineDecoration.indexOf("arrow-begin")){var d=ce(s,f),h=ue(c,d),p=fe(c,2,h),g=fe(c,-2,h);ye(o,{style:"display:block;",d:"M".concat(p.x,",").concat(p.y," L").concat(c.x,",").concat(c.y," L").concat(g.x,",").concat(g.y)})}if(-1!==e.lineDecoration.indexOf("arrow-end")){var m=ce(s,-f),v=ue(u,m),y=fe(u,2,v),E=fe(u,-2,v);ye(l,{style:"display:block;",d:"M".concat(y.x,",").concat(y.y," L").concat(u.x,",").concat(u.y," L").concat(E.x,",").concat(E.y)})}}}},Ae=function(t,e){return xe[t](e)},Ce=function(t,e,n,r,i){"path"!==e&&(t.rect=me(n,r,i)),t.styles=pe(n,r,i),Re[e](t,n,r,i)},Ie=function(t,e){return t[1].zIndex>e[1].zIndex?1:t[1].zIndex<e[1].zIndex?-1:0},Oe=function(){var t={resize:function(t,e){var r=e.mode,a=void 0===r?"contain":r,l=e.upscale,c=void 0!==l&&l,u=e.width,s=e.height,f=e.matrix;if(f=!f||i(f)?null:f,!u&&!s)return o(t,f);if(null===u?u=s:null===s&&(s=u),"force"!==a){var d=u/t.width,h=s/t.height,p=1;if("cover"===a?p=Math.max(d,h):"contain"===a&&(p=Math.min(d,h)),p>1&&!1===c)return o(t,f);u=t.width*p,s=t.height*p}for(var g=t.width,m=t.height,v=Math.round(u),y=Math.round(s),E=t.data,_=new Uint8ClampedArray(v*y*4),w=g/v,T=m/y,x=Math.ceil(.5*w),R=Math.ceil(.5*T),A=0;A<y;A++)for(var C=0;C<v;C++){for(var I=4*(C+A*v),O=0,M=0,S=0,b=0,L=0,P=0,G=0,D=(A+.5)*T,k=Math.floor(A*T);k<(A+1)*T;k++)for(var U=Math.abs(D-(k+.5))/R,V=(C+.5)*w,B=U*U,N=Math.floor(C*w);N<(C+1)*w;N++){var z=Math.abs(V-(N+.5))/x,F=Math.sqrt(B+z*z);if(F>=-1&&F<=1&&(O=2*F*F*F-3*F*F+1)>0){var W=E[3+(z=4*(N+k*g))];G+=O*W,S+=O,W<255&&(O=O*W/250),b+=O*E[z],L+=O*E[z+1],P+=O*E[z+2],M+=O}}_[I]=b/M,_[I+1]=L/M,_[I+2]=P/M,_[I+3]=G/S,f&&n(I,_,f)}return{data:_,width:v,height:y}},filter:o},e=function(e,n){var r=e.transforms,i=null;if(r.forEach(function(t){"filter"===t.type&&(i=t)}),i){var o=null;r.forEach(function(t){"resize"===t.type&&(o=t)}),o&&(o.data.matrix=i.data,r=r.filter(function(t){return"filter"!==t.type}))}n(function(e,n){return e.forEach(function(e){n=t[e.type](n,e.data)}),n}(r,e.imageData))};function n(t,e,n){for(var r=0,i=0,o=0,a=e[t]/255,l=e[t+1]/255,c=e[t+2]/255,u=e[t+3]/255;r<4;r++)o=255*(a*n[i=5*r]+l*n[i+1]+c*n[i+2]+u*n[i+3]+n[i+4]),e[t+r]=Math.max(0,Math.min(o,255))}self.onmessage=function(t){e(t.data.message,function(e){self.postMessage({id:t.data.id,message:e},[e.data.buffer])})};var r=self.JSON.stringify([1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0]);function i(t){return self.JSON.stringify(t||[])===r}function o(t,e){if(!e||i(e))return t;for(var n=t.data,r=n.length,o=e[0],a=e[1],l=e[2],c=e[3],u=e[4],s=e[5],f=e[6],d=e[7],h=e[8],p=e[9],g=e[10],m=e[11],v=e[12],y=e[13],E=e[14],_=e[15],w=e[16],T=e[17],x=e[18],R=e[19],A=0,C=0,I=0,O=0,M=0;A<r;A+=4)C=n[A]/255,I=n[A+1]/255,O=n[A+2]/255,M=n[A+3]/255,n[A]=Math.max(0,Math.min(255*(C*o+I*a+O*l+M*c+u),255)),n[A+1]=Math.max(0,Math.min(255*(C*s+I*f+O*d+M*h+p),255)),n[A+2]=Math.max(0,Math.min(255*(C*g+I*m+O*v+M*y+E),255)),n[A+3]=Math.max(0,Math.min(255*(C*_+I*w+O*T+M*x+R),255));return t}},Me=function(t,e){if(1165519206===t.getUint32(e+4,!1)){e+=4;var n=18761===t.getUint16(e+=6,!1);e+=t.getUint32(e+4,n);var r=t.getUint16(e,n);e+=2;for(var i=0;i<r;i++)if(274===t.getUint16(e+12*i,n))return t.setUint16(e+12*i+8,1,n),!0;return!1}},Se=function(t){return new Promise(function(e){var n=new FileReader;n.onload=function(){return e(function(t){var e=new DataView(t);if(65496!==e.getUint16(0))return null;for(var n,r,i=2,o=!1;i<e.byteLength&&(n=e.getUint16(i,!1),r=e.getUint16(i+2,!1)+2,n>=65504&&n<=65519||65534===n)&&(o||(o=Me(e,i)),!(i+r>e.byteLength));)i+=r;return t.slice(0,i)}(n.result)||null)},n.readAsArrayBuffer(t.slice(0,262144))})},be=function(t,e){var n=window.BlobBuilder=window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder||window.MSBlobBuilder;if(n){var r=new n;return r.append(t),r.getBlob(e)}return new Blob([t],{type:e})},Le=function(t){var e=new Blob(["(",t.toString(),")()"],{type:"application/javascript"}),n=URL.createObjectURL(e),r=new Worker(n),i=[];return{transfer:function(){},post:function(t,e,n){var o=Math.random().toString(36).substr(2,9);i[o]=e,r.onmessage=function(t){var e=i[t.data.id];e&&(e(t.data.message),delete i[t.data.id])},r.postMessage({id:o,message:t},n)},terminate:function(){r.terminate(),URL.revokeObjectURL(n)}}},Pe=function(t,e){return new Promise(function(n){var r={width:t.width,height:t.height},i=t.getContext("2d");(function(t){return t.reduce(function(t,e){return t.then(function(t){return e().then(Array.prototype.concat.bind(t))})},Promise.resolve([]))})(e.sort(Ie).map(function(t){return function(){return new Promise(function(e){ke[t[0]](i,r,t[1],e)&&e()})}})).then(function(){return n(t)})})},Ge=function(t,e){t.beginPath(),t.lineCap=e["stroke-linecap"],t.lineJoin=e["stroke-linejoin"],t.lineWidth=e["stroke-width"],e["stroke-dasharray"].length&&t.setLineDash(e["stroke-dasharray"].split(",")),t.fillStyle=e.fill,t.strokeStyle=e.stroke,t.globalAlpha=e.opacity||1},De=function(t){t.fill(),t.stroke(),t.globalAlpha=1},ke={rect:function(t,e,n){var r=me(n,e),i=pe(n,e);return Ge(t,i),t.rect(r.x,r.y,r.width,r.height),De(t),!0},ellipse:function(t,e,n){var r=me(n,e),i=pe(n,e);Ge(t,i);var o=r.x,a=r.y,l=r.width,c=r.height,u=l/2*.5522848,s=c/2*.5522848,f=o+l,d=a+c,h=o+l/2,p=a+c/2;return t.moveTo(o,p),t.bezierCurveTo(o,p-s,h-u,a,h,a),t.bezierCurveTo(h+u,a,f,p-s,f,p),t.bezierCurveTo(f,p+s,h+u,d,h,d),t.bezierCurveTo(h-u,d,o,p+s,o,p),De(t),!0},image:function(t,e,n,r){var i=me(n,e),o=pe(n,e);Ge(t,o);var a=new Image;a.onload=function(){if("cover"===n.fit){var e=i.width/i.height,o=e>1?a.width:a.height*e,l=e>1?a.width/e:a.height,c=.5*a.width-.5*o,u=.5*a.height-.5*l;t.drawImage(a,c,u,o,l,i.x,i.y,i.width,i.height)}else if("contain"===n.fit){var s=Math.min(i.width/a.width,i.height/a.height),f=s*a.width,d=s*a.height,h=i.x+.5*i.width-.5*f,p=i.y+.5*i.height-.5*d;t.drawImage(a,0,0,a.width,a.height,h,p,f,d)}else t.drawImage(a,0,0,a.width,a.height,i.x,i.y,i.width,i.height);De(t),r()},a.src=n.src},text:function(t,e,n){var r=me(n,e),i=pe(n,e);Ge(t,i);var o=he(n.fontSize,e),a=n.fontFamily||"sans-serif",l=n.fontWeight||"normal",c=n.textAlign||"left";return t.font="".concat(l," ").concat(o,"px ").concat(a),t.textAlign=c,t.fillText(n.text,r.x,r.y),De(t),!0},line:function(t,e,n){var r=me(n,e),i=pe(n,e);Ge(t,i),t.beginPath();var o={x:r.x,y:r.y},a={x:r.x+r.width,y:r.y+r.height};t.moveTo(o.x,o.y),t.lineTo(a.x,a.y);var l=se({x:a.x-o.x,y:a.y-o.y}),c=.04*Math.min(e.width,e.height);if(-1!==n.lineDecoration.indexOf("arrow-begin")){var u=ce(l,c),s=ue(o,u),f=fe(o,2,s),d=fe(o,-2,s);t.moveTo(f.x,f.y),t.lineTo(o.x,o.y),t.lineTo(d.x,d.y)}if(-1!==n.lineDecoration.indexOf("arrow-end")){var h=ce(l,-c),p=ue(a,h),g=fe(a,2,p),m=fe(a,-2,p);t.moveTo(g.x,g.y),t.lineTo(a.x,a.y),t.lineTo(m.x,m.y)}return De(t),!0},path:function(t,e,n){var r=pe(n,e);Ge(t,r),t.beginPath();var i=n.points.map(function(t){return{x:he(t.x,e,1,"width"),y:he(t.y,e,1,"height")}});t.moveTo(i[0].x,i[0].y);for(var o=i.length,a=1;a<o;a++)t.lineTo(i[a].x,i[a].y);return De(t),!0}},Ue=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return new Promise(function(r,i){if(!t||!function(t){return/^image/.test(t.type)}(t))return i({status:"not an image file",file:t});var o=n.stripImageHead,a=n.beforeCreateBlob,l=n.afterCreateBlob,c=n.canvasMemoryLimit,u=e.crop,s=e.size,f=e.filter,d=e.markup,h=e.output,p=e.image&&e.image.orientation?Math.max(1,Math.min(8,e.image.orientation)):null,g=h&&h.quality,m=null===g?null:g/100,v=h&&h.type||null,y=h&&h.background||null,E=[];!s||"number"!=typeof s.width&&"number"!=typeof s.height||E.push({type:"resize",data:s}),f&&20===f.length&&E.push({type:"filter",data:f});var _=function(t){var e=l?l(t):t;Promise.resolve(e).then(r)},w=function(e,n){var r=function(t){var e=document.createElement("canvas");return e.width=t.width,e.height=t.height,e.getContext("2d").putImageData(t,0,0),e}(e),l=d.length?Pe(r,d):r;Promise.resolve(l).then(function(e){(function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return new Promise(function(r){var i=n?n(t):t;Promise.resolve(i).then(function(t){t.toBlob(r,e.type,e.quality)})})})(e,n,a).then(function(n){if(oe(e),o)return _(n);Se(t).then(function(t){null!==t&&(n=new Blob([t,n.slice(20)],{type:n.type})),_(n)})}).catch(i)})};if(/svg/.test(t.type)&&null===v)return function(t,e,n,r){return new Promise(function(i){var o=r.background,a=void 0===o?null:o,l=new FileReader;l.onloadend=function(){var t=l.result,r=document.createElement("div");r.style.cssText="position:absolute;pointer-events:none;width:0;height:0;visibility:hidden;",r.innerHTML=t;var o=r.querySelector("svg");document.body.appendChild(r);var c=o.getBBox();r.parentNode.removeChild(r);var u=r.querySelector("title"),s=o.getAttribute("viewBox")||"",f=o.getAttribute("width")||"",d=o.getAttribute("height")||"",h=parseFloat(f)||null,p=parseFloat(d)||null,g=(f.match(/[a-z]+/)||[])[0]||"",m=(d.match(/[a-z]+/)||[])[0]||"",v=s.split(" ").map(parseFloat),y=v.length?{x:v[0],y:v[1],width:v[2],height:v[3]}:c,E=null!=h?h:y.width,_=null!=p?p:y.height;o.style.overflow="visible",o.setAttribute("width",E),o.setAttribute("height",_);var w="";if(n&&n.length){var T={width:E,height:_};w=n.sort(Ie).reduce(function(t,e){var n=Ae(e[0],e[1]);return Ce(n,e[0],e[1],T),n.removeAttribute("id"),1===n.getAttribute("opacity")&&n.removeAttribute("opacity"),t+"\n"+n.outerHTML+"\n"},""),w="\n\n<g>".concat(w.replace(/&nbsp;/g," "),"</g>\n\n")}var x=e.aspectRatio||_/E,R=E,A=R*x,C=void 0===e.scaleToFit||e.scaleToFit,I=Vt({width:E,height:_},Lt({width:R,height:A},x),e.rotation,C?e.center:{x:.5,y:.5}),O=e.zoom*I,M=e.rotation*(180/Math.PI),S={x:.5*R,y:.5*A},b={x:S.x-E*e.center.x,y:S.y-_*e.center.y},L=["rotate(".concat(M," ").concat(S.x," ").concat(S.y,")"),"translate(".concat(S.x," ").concat(S.y,")"),"scale(".concat(O,")"),"translate(".concat(-S.x," ").concat(-S.y,")"),"translate(".concat(b.x," ").concat(b.y,")")],P=["scale(".concat(e.flip.horizontal?-1:1," ").concat(e.flip.vertical?-1:1,")"),"translate(".concat(e.flip.horizontal?-E:0," ").concat(e.flip.vertical?-_:0,")")],G='<?xml version="1.0" encoding="UTF-8"?>\n<svg width="'.concat(R).concat(g,'" height="').concat(A).concat(m,'" \nviewBox="0 0 ').concat(R," ").concat(A,'" ').concat(a?'style="background:'+a+'" ':"",'\npreserveAspectRatio="xMinYMin"\nxmlns:xlink="http://www.w3.org/1999/xlink"\nxmlns="http://www.w3.org/2000/svg">\n\x3c!-- Generated by PQINA - https://pqina.nl/ --\x3e\n<title>').concat(u?u.textContent:"",'</title>\n<g transform="').concat(L.join(" "),'">\n<g transform="').concat(P.join(" "),'">\n').concat(o.outerHTML).concat(w,"\n</g>\n</g>\n</svg>");i(G)},l.readAsText(t)})}(t,u,d,{background:y}).then(function(t){r(be(t,"image/svg+xml"))});var T=URL.createObjectURL(t);(function(t){return new Promise(function(e,n){var r=new Image;r.onload=function(){e(r)},r.onerror=function(t){n(t)},r.src=t})})(T).then(function(e){URL.revokeObjectURL(T);var n=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=r.canvasMemoryLimit,o=r.background,a=void 0===o?null:o,l=n.zoom||1,c=le(t,e,n.flip),u={width:c.width,height:c.height},s=n.aspectRatio||u.height/u.width,f=Pt(u,s,l);if(i){var d=f.width*f.height;if(d>i){var h=Math.sqrt(i)/Math.sqrt(d);u.width=Math.floor(u.width*h),u.height=Math.floor(u.height*h),f=Pt(u,s,l)}}var p=document.createElement("canvas"),g={x:.5*f.width,y:.5*f.height},m={x:0,y:0,width:f.width,height:f.height,center:g},v=void 0===n.scaleToFit||n.scaleToFit,y=l*Vt(u,Lt(m,s),n.rotation,v?n.center:{x:.5,y:.5});p.width=Math.round(f.width/y),p.height=Math.round(f.height/y),g.x/=y,g.y/=y;var E=g.x-u.width*(n.center?n.center.x:.5),_=g.y-u.height*(n.center?n.center.y:.5),w=p.getContext("2d");a&&(w.fillStyle=a,w.fillRect(0,0,p.width,p.height)),w.translate(g.x,g.y),w.rotate(n.rotation||0),w.drawImage(c,E-g.x,_-g.y,u.width,u.height);var T=w.getImageData(0,0,p.width,p.height);return oe(p),T}(e,p,u,{canvasMemoryLimit:c,background:y}),r={quality:m,type:v||t.type};if(!E.length)return w(n,r);var i=Le(Oe);i.post({transforms:E,imageData:n},function(t){w(function(t){var e;try{e=new ImageData(t.width,t.height)}catch(n){e=document.createElement("canvas").getContext("2d").createImageData(t.width,t.height)}return e.data.set(t.data),e}(t),r),i.terminate()},[n.data.buffer])}).catch(i)})},Ve=function(t,e){if(1165519206!==t.getUint32(e+=2,!1))return-1;var n=18761===t.getUint16(e+=6,!1);e+=t.getUint32(e+4,n);var r=t.getUint16(e,n);e+=2;for(var i=0;i<r;i++)if(274===t.getUint16(e+12*i,n))return t.getUint16(e+12*i+8,n)},Be=function(t){return new Promise(function(e){var n=new FileReader;n.onload=function(){return e(function(t){var e=new DataView(t);if(65496!=e.getUint16(0,!1))return null;for(var n,r=e.byteLength,i=2;i<r;){if(e.getUint16(i+2,!1)<=8)return-1;if(n=e.getUint16(i,!1),i+=2,65505===n)return Ve(e,i);if(65280!=(65280&n))return null;i+=e.getUint16(i,!1)}}(n.result)||-1)},n.readAsArrayBuffer(t.slice(0,262144))})},Ne=1,ze=2,Fe=function(t,e,n){var r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],i=t.center,o=t.zoom,a=t.aspectRatio,l=Ot(e),c={x:l.x-n.width*i.x,y:l.y-n.height*i.y},u=2*Math.PI+t.rotation%(2*Math.PI),s=o*Vt(n,Lt(e,a||n.height/n.width),u,r?i:{x:.5,y:.5});return{origin:{x:i.x*n.width,y:i.y*n.height},translation:c,scale:s,rotation:t.rotation}},We=function(t){return{origin:a({},t.origin),translation:a({},t.translation),rotation:t.rotation,scale:t.scale}},qe=function(t,e,n,r){var i=n.translation,o=n.scale,l=n.rotation,c=n.origin,u={origin:a({},c),translation:a({},i),scale:o,rotation:2*Math.PI+l%(2*Math.PI)},s=t.height/t.width,f={x:c.x+i.x,y:c.y+i.y},d=zt(e,u.rotation),h=_t(d.tl,-u.rotation,f),p=_t(d.tr,-u.rotation,f),g=_t(d.br,-u.rotation,f),m=_t(d.bl,-u.rotation,f),v={x:Math.min(h.x,p.x,g.x,m.x),y:Math.min(h.y,p.y,g.y,m.y),width:Math.max(h.x,p.x,g.x,m.x)-Math.min(h.x,p.x,g.x,m.x),height:Math.max(h.y,p.y,g.y,m.y)-Math.min(h.y,p.y,g.y,m.y)},y=St(v.x,v.y,v.width,v.height),E=Ot(y),_=Ct(y),w=Bt(t,n),T=Ot(w),x={x:w.x,y:w.y},R={x:T.x,y:T.y},A=E.x,C=E.y,I={x:x.x,y:x.y,width:w.width,height:w.height};if(!function(t,e){var n=Ct(t),r=Ct(e);return n.left>=r.left&&n.top>=r.top&&n.bottom<=r.bottom&&n.right<=r.right}(y,w))if("moving"===r){I.y>y.y?I.y=y.y:I.y+I.height<_.bottom&&(I.y=_.bottom-I.height),I.x>y.x?I.x=y.x:I.x+I.width<_.right&&(I.x=_.right-I.width);var O=Bt(t,a({},n,{scale:u.scale})),M=Ot(O);R.x=M.x,R.y=M.y,x.x=O.x,x.y=O.y,I.x=R.x-.5*I.width,I.y=R.y-.5*I.height,I.y>y.y?I.y=y.y:I.y+I.height<_.bottom&&(I.y=_.bottom-I.height),I.x>y.x?I.x=y.x:I.x+I.width<_.right&&(I.x=_.right-I.width);var S={x:I.x-x.x,y:I.y-x.y},b={x:S.x*Math.cos(u.rotation)-S.y*Math.sin(u.rotation),y:S.x*Math.sin(u.rotation)+S.y*Math.cos(u.rotation)};u.translation.x+=b.x,u.translation.y+=b.y}else if("resizing"===r){w.width<y.width&&(I.width=y.width,I.height=I.width*s,I.height<y.height&&(I.height=y.height,I.width=I.height/s)),w.height<y.height&&(I.height=y.height,I.width=I.height/s,I.width<y.width&&(I.width=y.width,I.height=I.width*s)),I.x=R.x-.5*I.width,I.y=R.y-.5*I.height,I.y>y.y?I.y=y.y:I.y+I.height<_.bottom&&(I.y=_.bottom-I.height),I.x>y.x?I.x=y.x:I.x+I.width<_.right&&(I.x=_.right-I.width),u.scale=Vt(t,e,u.rotation,{x:(A-I.x)/I.width,y:(C-I.y)/I.height});var L=Bt(t,a({},n,{scale:u.scale})),P=Ot(L);R.x=P.x,R.y=P.y,x.x=L.x,x.y=L.y,I.x=R.x-.5*I.width,I.y=R.y-.5*I.height,I.y>y.y?I.y=y.y:I.y+I.height<_.bottom&&(I.y=_.bottom-I.height),I.x>y.x?I.x=y.x:I.x+I.width<_.right&&(I.x=_.right-I.width);var G={x:I.x-x.x,y:I.y-x.y},D={x:G.x*Math.cos(u.rotation)-G.y*Math.sin(u.rotation),y:G.x*Math.sin(u.rotation)+G.y*Math.cos(u.rotation)};u.translation.x+=D.x,u.translation.y+=D.y}else if("rotating"===r){var k=!1;if(I.y>y.y){var U=I.y-y.y;I.y=y.y,I.height+=2*U,k=!0}if(I.y+I.height<_.bottom){var V=_.bottom-(I.y+I.height);I.y=_.bottom-I.height,I.height+=2*V,k=!0}if(I.x>y.x){var B=I.x-y.x;I.x=y.x,I.width+=2*B,k=!0}if(I.x+I.width<_.right){var N=_.right-(I.x+I.width);I.x=_.right-I.width,I.width+=2*N,k=!0}k&&(u.scale=Vt(t,e,u.rotation,{x:(A-w.x)/w.width,y:(C-w.y)/w.height}))}return a({},u,{rotation:n.rotation})},He={n:function(t){return{x:t.x+.5*t.width,y:t.y}},e:function(t){return{x:t.x+t.width,y:t.y+.5*t.height}},s:function(t){return{x:t.x+.5*t.width,y:t.y+t.height}},w:function(t){return{x:t.x,y:t.y+.5*t.height}}},Ye=function(t,e){return He[t](e)},Ze=function(t,e,n){var r=n.origin,i=n.translation,o=2*Math.PI+n.rotation%(2*Math.PI),l=Bt(t,n),c={x:r.x+i.x,y:r.y+i.y},u=zt(e,o),s=_t(u.tl,-o,c),f=_t(u.tr,-o,c),d=_t(u.br,-o,c),h={x:Math.min(s.x,f.x,d.x),y:Math.min(s.y,f.y,d.y),width:Math.max(s.x,f.x,d.x)-Math.min(s.x,f.x,d.x),height:Math.max(s.y,f.y,d.y)-Math.min(s.y,f.y,d.y)},p=St(h.x,h.y,h.width,h.height),g=Ct(p),m=Ct(l),v=l;if(g.top<m.top||g.right>m.right||g.bottom>m.bottom||g.left<m.left){var y=a({},m);if(g.top<=y.top){var E=y.bottom-y.top,_=y.right-y.left,w=Math.max(1,p.height/E),T=E*w,x=_*w-_;y.bottom=g.top+T,y.top=g.top,y.left-=.5*x,y.right+=.5*x}if(g.bottom>=y.bottom){var R=y.bottom-y.top,A=y.right-y.left,C=Math.max(1,p.height/R),I=R*C,O=A*C-A;y.bottom=g.bottom,y.top=g.bottom-I,y.left-=.5*O,y.right+=.5*O}if(g.left<=y.left){var M=y.bottom-y.top,S=y.right-y.left,b=Math.max(1,p.width/S),L=S*b,P=M*b-M;y.right=g.left+L,y.left=g.left,y.top-=.5*P,y.bottom+=.5*P}if(g.right>=y.right){var G=y.bottom-y.top,D=y.right-y.left,k=Math.max(1,p.width/D),U=D*k,V=G*k-G;y.right=g.right,y.left=g.right-U,y.top-=.5*V,y.bottom+=.5*V}v=St(y.left,y.top,y.right-y.left,y.bottom-y.top)}var B=Mt(v),N=Ot(v),z=_t(B.tl,o,c),F=_t(B.br,o,c),W=z.x+.5*(F.x-z.x),q=z.y+.5*(F.y-z.y),H=xt(v,{x:W-N.x,y:q-N.y}),Y=xt(p,{x:W-N.x,y:q-N.y}),Z=Ot(Y),X={x:H.x,y:H.y},K=H.width,j=H.height,Q=(Z.x-X.x)/K,J=(Z.y-X.y)/j,$=K/t.width,tt={x:Q*t.width,y:J*t.height},et=1-$,nt=tt.x*et,rt=tt.y*et,it={x:X.x+K*Q,y:X.y+j*J},ot=_t(X,o,{x:X.x+.5*K,y:X.y+.5*j}),at=_t(X,o,it),lt=ot.x-at.x,ct=ot.y-at.y;return{origin:tt,translation:{x:X.x-nt+lt,y:X.y-rt+ct},scale:$,rotation:n.rotation}},Xe={nw:function(t){return{x:t.x,y:t.y}},ne:function(t){return{x:t.x+t.width,y:t.y}},se:function(t){return{x:t.x+t.width,y:t.y+t.height}},sw:function(t){return{x:t.x,y:t.y+t.height}}},Ke=function(t,e){return Xe[t](e)},je=Math.PI/2,Qe=Math.PI/4,Je=function(t){var e=pt(Qe),n=pt(je),r=t/n,i=Math.floor(r)*n,o=t-i;return o>e&&(o-=n,i+=n),{main:i,sub:o}},$e=function(t,e){var n={width:t.width,height:t.height};if(t.width>e.width||t.height>e.height){var r=t.height/t.width,i=e.width/t.width,o=e.height/t.height;i<o?(n.width=t.width*i,n.height=n.width*r):(n.height=t.height*o,n.width=n.height/r)}return n},tn=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return(e+t).slice(-e.length)},en=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date;return"".concat(t.getFullYear(),"-").concat(tn(t.getMonth()+1,"00"),"-").concat(tn(t.getDate(),"00"),"_").concat(tn(t.getHours(),"00"),"-").concat(tn(t.getMinutes(),"00"),"-").concat(tn(t.getSeconds(),"00"))},nn=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=t("GET_CROP_ASPECT_RATIO"),o={center:{x:.5,y:.5},flip:{horizontal:!1,vertical:!1},zoom:1,rotation:0,aspectRatio:null};n?Object.assign(o,n):e.options.crop?Object.assign(o,e.options.crop):o.aspectRatio=i;var a=r.width,l=r.height;if(a&&l)o.aspectRatio=l/a;else if(e.instructions.size){var c=e.instructions.size,u=c.width,s=c.height;u&&s&&(o.aspectRatio=s/u)}return o},rn=function(t){return t.charAt(0).toUpperCase()+t.slice(1)},on={jpeg:"jpg","svg+xml":"svg"},an=function(t){return Array.isArray(t)&&20===t.length},ln=["x","y","left","top","right","bottom","width","height"],cn=function(t){var e=l(t,2),n=e[0],r=e[1],i=!1!==r.allowSelect,o=!1!==r.allowMove,c=!1!==r.allowResize,u=!1!==r.allowInput,s=!1!==r.allowDestroy,f=void 0===r.allowEdit||r.allowEdit;(!0===r.allowResize||!0===r.allowMove||!0===r.allowInput||r.allowEdit)&&(i=!0),!1===r.allowMove&&(c=!1),!0===r.allowResize&&(o=!0);var d=r.points?{}:ln.reduce(function(t,e){return t[e]=function(t){return"string"==typeof t&&/%/.test(t)?parseFloat(t)/100:t}(r[e]),t},{});return r.points&&(c=!1),[n,a({zIndex:0,id:Math.random().toString(36).substr(2,9)},r,d,{isDestroyed:!1,isSelected:!1,isDirty:!0,allowDestroy:s,allowSelect:i,allowMove:o,allowResize:c,allowInput:u,allowEdit:f})]},un={contrast:function(t){return[t,0,0,0,.5*(1-t),0,t,0,0,.5*(1-t),0,0,t,0,.5*(1-t),0,0,0,1,0]},exposure:function(t){return[t,0,0,0,0,0,t,0,0,0,0,0,t,0,0,0,0,0,1,0]},brightness:function(t){return[1,0,0,0,t,0,1,0,0,t,0,0,1,0,t,0,0,0,1,0]},saturation:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return[.213+.787*t,.715-.715*t,.072-.072*t,0,0,.213-.213*t,.715+.285*t,.072-.072*t,0,0,.213-.213*t,.715-.715*t,.072+.928*t,0,0,0,0,0,1,0]}},sn=Math.PI/2,fn=function(t){var e={upscale:t("GET_OUTPUT_UPSCALE"),mode:t("GET_OUTPUT_FIT"),width:t("GET_OUTPUT_WIDTH"),height:t("GET_OUTPUT_HEIGHT")},n=t("GET_SIZE_INPUT");if(t("ALLOW_MANUAL_RESIZE")&&(n.width||n.height)){var r=n.width,i=n.height,o=t("GET_CROP_RECTANGLE_ASPECT_RATIO");r&&!i?i=r/o:i&&!r&&(r=i*o),e.width=r,e.height=i,e.upscale=!0,e.mode="force"}return e},dn=function(t,e,n){return new Promise(function(r,i){var o={data:null,file:null},l=Wt(e.image,e.crop),c=fn(n),u={crop:l,image:a({},function(t,e){var n=e("GET_CROP",e("GET_UID"),Date.now()),r={width:n.cropStatus.currentWidth,height:n.cropStatus.currentHeight},i=t.mode,o=t.width,a=t.height,l=t.upscale;if(!o&&!a)return r;if(null===o?o=a:null===a&&(a=o),"force"!==i){var c=o/r.width,u=a/r.height,s=1;if("cover"===i?s=Math.max(c,u):"contain"===i&&(s=Math.min(c,u)),s>1&&!1===l)return r;o=r.width*s,a=r.height*s}return{width:Math.round(o),height:Math.round(a)}}(c,n),{orientation:e.file.orientation}),size:c,output:{type:n("GET_OUTPUT_TYPE"),quality:n("GET_OUTPUT_QUALITY"),background:e.options.outputCanvasBackgroundColor},filter:e.colorMatrices.filter?{id:e.filterName,value:e.filterValue,matrix:e.colorMatrices.filter}:null,color:Object.keys(e.colorValues).length?Object.keys(e.colorValues).reduce(function(t,n){return t[n]={value:e.colorValues[n],matrix:e.colorMatrices[n].map(function(t){return pt(t,5)})},t},{}):null,markup:function(t){return t.markup.filter(function(t){return!t[1].isDestroyed})}(e).map(function(t){return[t[0],a({},t[1])]}),colorMatrix:n("GET_COLOR_MATRIX")};if(t.data&&(o.data=u),t.file){var s={beforeCreateBlob:n("GET_BEFORE_CREATE_BLOB"),afterCreateBlob:n("GET_AFTER_CREATE_BLOB"),stripImageHead:n("GET_OUTPUT_STRIP_IMAGE_HEAD"),canvasMemoryLimit:n("GET_OUTPUT_CANVAS_MEMORY_LIMIT")},f=e.file.data,d=a({},u,{filter:u.colorMatrix,markup:u.markup});Ue(f,d,s).then(function(t){o.file=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,i="string"==typeof n?t.slice(0,t.size,n):t.slice(0,t.size,t.type);return i.lastModifiedDate=new Date,F(e)||(e=en()),e&&null===r&&function(t){return t.split(".").pop()}(e)?i.name=e:(r=r||function(t){if("string"!=typeof t)return"";var e=t.split("/").pop();return/svg/.test(e)?"svg":/zip|compressed/.test(e)?"zip":/plain/.test(e)?"txt":/msword/.test(e)?"doc":/[a-z]+/.test(e)?"jpeg"===e?"jpg":e:""}(i.type),i.name=e+(r?"."+r:"")),i}(t,function(t,e){var n=function(t){return t.substr(0,t.lastIndexOf("."))||t}(t),r=e.split("/")[1],i=on[r]||r;return"".concat(n,".").concat(i)}(f.name,function(t){return/jpeg|png|svg\+xml/.test(t)?t:"image/jpeg"}(t.type))),r(o)}).catch(i)}else r(o)})},hn=function(t){t.crop.draft.rotateMinScale=null},pn=function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=!(arguments.length>4&&void 0!==arguments[4])||arguments[4];!function(t){t.crop.draft.rotateMinScale||(t.crop.draft.rotateMinScale=t.crop.transforms.scale)}(t);var o=a({},t.crop.transforms,{scale:t.crop.draft.rotateMinScale});t.crop.draft.transforms=xn(t.image,t.crop.rectangle,o,e.main+e.sub,n,t.crop.draft.transforms?t.crop.draft.transforms.rotation:t.crop.rotation.main+t.crop.rotation.sub,r,i),t.crop.rotation=Je(t.crop.draft.transforms.rotation)},gn=function(t,e,n){if(null!==t.stage){_n(t),t.size.width=!!t.instructions.size&&t.instructions.size.width,t.size.height=!!t.instructions.size&&t.instructions.size.height,t.size.aspectRatioLocked=!0,t.size.aspectRatioPrevious=!1,t.crop.rectangle=Lt(t.stage,t.instructions.crop.aspectRatio||t.image.aspectRatio),t.crop.draft.rectangle=null;var r=void 0===t.instructions.crop.scaleToFit?void 0===t.crop.limitToImageBounds?t.options.cropLimitToImageBounds:t.crop.limitToImageBounds:t.instructions.crop.scaleToFit;t.crop.transforms=Fe(t.instructions.crop,t.stage,t.image,r),t.crop.draft.transforms=null,t.crop.rotation=Je(t.instructions.crop.rotation),t.crop.flip=a({},t.instructions.crop.flip);var i=e("GET_CROP_ASPECT_RATIO_OPTIONS")||[],o=i.map(function(t){return t.value.aspectRatio}).find(function(e){return e===t.instructions.crop.aspectRatio}),l=i.find(function(t){return null===t.value.aspectRatio});o?t.crop.aspectRatio=o:l&&i.length?t.crop.aspectRatio=null:t.crop.aspectRatio=e("GET_CROP_ASPECT_RATIO"),t.crop.isDirty=!1,t.instructions.markup&&n("MARKUP_SET_VALUE",{value:t.instructions.markup.map(cn).sort(Ie)}),n("CROP_SET_LIMIT",{value:r,silent:!0}),Object.keys(t.instructions.color).forEach(function(e){return n("COLOR_SET_VALUE",{key:e,value:t.instructions.color[e]})}),n("FILTER_SET_VALUE",{value:t.instructions.filter}),hn(t)}},mn=function(t,e){if(t.stage){_n(t);var n=t.crop.rectangle,r=n.height/n.width,i=t.crop.aspectRatio;if(null!==i&&pt(r,3)!==pt(i,3)){var o=e("GET_MIN_CROP_SIZE");o.width=pt(o.width),o.height=pt(o.height);var l=Math.min(n.width,n.height);Math.min(l*i,l/i)<Math.max(o.width,o.height)&&(t.crop.rectangle=function(t,e,n){var r=At(t);return r.width=Math.min(r.height,r.width),r.height=r.width,r.height=r.width*e,r.height<n.height&&(r.height=n.height,r.width=r.height/e),r.width<n.width&&(r.width=n.width,r.height=r.width*e),r}(a({},t.crop.rectangle),i,o),t.crop.draft.transforms=Ze(t.image,t.crop.rectangle,t.crop.transforms))}var c=t.crop.draft.transforms||t.crop.transforms,u=Ft(t.image,t.crop.rectangle,c,t.crop.limitToImageBounds);t.crop.aspectRatio&&(u.aspectRatio=t.crop.aspectRatio),t.crop.transforms=Fe(u,t.stage,t.image,u.scaleToFit),t.crop.draft.transforms=null;var s=t.crop.aspectRatio||t.crop.rectangle.height/t.crop.rectangle.width;t.crop.rectangle=Lt(t.stage,s),t.crop.draft.rectangle=null,hn(t)}},vn=function(t){t.crop.draft.rectangle=null,t.crop.transforms=t.crop.draft.transforms||t.crop.transforms,t.crop.transforms.interaction=null,t.crop.draft.transforms=null,t.crop.transforms=a({},t.crop.transforms,function(t,e,n){var r=n.origin,i=n.translation,o=n.scale,a=2*Math.PI+n.rotation%(2*Math.PI),l={x:r.x+i.x,y:r.y+i.y},c=zt(e,a),u=_t(c.tl,-a,l),s=_t(c.tr,-a,l),f=_t(c.br,-a,l),d=_t(c.bl,-a,l),h=St(Math.min(u.x,s.x,f.x,d.x),Math.min(u.y,s.y,f.y,d.y),Math.max(u.x,s.x,f.x,d.x)-Math.min(u.x,s.x,f.x,d.x),Math.max(u.y,s.y,f.y,d.y)-Math.min(u.y,s.y,f.y,d.y)),p=St(h.x,h.y,h.width,h.height),g=Bt(t,n),m=Mt(g),v=Ot(g),y=_t(m.tl,a,l),E=_t(m.br,a,l),_=y.x+.5*(E.x-y.x),w=y.y+.5*(E.y-y.y),T=xt(g,{x:_-v.x,y:w-v.y}),x=xt(p,{x:_-v.x,y:w-v.y}),R=Ot(x),A={x:T.x,y:T.y},C=T.width,I=T.height,O=(R.x-A.x)/C,M=(R.y-A.y)/I,S={x:O*t.width,y:M*t.height},b=1-o,L=S.x*b,P=S.y*b,G={x:A.x+C*O,y:A.y+I*M},D=_t(A,a,{x:A.x+.5*C,y:A.y+.5*I}),k=_t(A,a,G),U=D.x-k.x,V=D.y-k.y;return{origin:gt(S),translation:gt({x:A.x-L+U,y:A.y-P+V})}}(t.image,t.crop.rectangle,t.crop.transforms)),t.crop.isRotating=!1,t.crop.isDirty=!0},yn=function(t,e,n){var r=e("GET_CROP_ZOOM_TIMEOUT");r&&(clearTimeout(t.zoomTimeoutId),t.zoomTimeoutId=setTimeout(function(){n("CROP_ZOOM")},r))},En=function(t,e,n){_n(t),yn(t,e,n)},_n=function(t){clearTimeout(t.zoomTimeoutId)},wn=function(t){t.crop.draft.transforms=We(t.crop.transforms),t.crop.draft.rectangle={limited:At(t.crop.rectangle),free:At(t.crop.rectangle)},_n(t)},Tn=function(t,e){return Math.min(t.width/e.width,t.height/e.height)},xn=function(t,e,n,r,i,o,l,c){var u=a({},We(n),{rotation:r}),s=c?qe(t,e,u,"rotating"):u,f=Tn(e,i);return pt(s.scale,5)>pt(f,5)?(l&&(o+=2*l),a({},We(n),{rotation:o,interaction:{rotation:s.rotation}})):(s.scale=Math.min(f,s.scale),s.interaction={rotation:s.rotation},s)},Rn=function(t,e,n,r,i,o){var l=Math.max(1e-10,r),c=a({},We(n),{scale:l}),u=o?qe(t,e,c,"resizing"):c,s=Tn(e,i);return u.scale=Math.min(s,u.scale),u.interaction={scale:l},u},An=function(t,e){return t.getAllResponseHeaders().indexOf(e)>=0?t.getResponseHeader("Content-Disposition"):null},Cn=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.progress,r=void 0===n?function(t){}:n,i=e.load,o=void 0===i?function(t,e){}:i,a=e.error,l=void 0===a?function(){}:a,c=new XMLHttpRequest;c.onprogress=function(t){return r(t.lengthComputable?t.loaded/t.total:null)},c.onerror=function(){return l(c)},c.onload=function(){var t=c.response;if(!t)return l(c);var e=c.getResponseHeader("Content-Type"),n=An(c,"Content-Disposition"),r=n?function(t){if(!t)return null;var e=t.split(/filename=|filename\*=.+''/).splice(1).map(function(t){return t.trim().replace(/^["']|[;"']{0,2}$/g,"")}).filter(function(t){return t.length});return e.length?decodeURI(e[e.length-1]):null}(n):null,i=An(c,"Content-Doka"),a=null;if(i)try{a=JSON.parse(i)}catch(t){}!t.type.length&&e&&(function(t){throw new Error('"'+t+'" is read-only')}("blob"),t=t.slice(0,t.size,e)),r&&(t.name=r),o(t,a)},c.open("GET",t),c.responseType="blob",c.send()},In=function(t){return!1===t.file},On=function(t,e,n){return a({SET_UID:function(t){var e=t.id;n.uid=e},AWAIT_IMAGE:function(){n.file||(n.noImageTimeout=setTimeout(function(){t("AWAITING_IMAGE")},250))},REQUEST_REMOVE_IMAGE:function(){t("UNLOAD_IMAGE"),n.file=!1,n.noImageTimeout=setTimeout(function(){t("AWAITING_IMAGE")},500)},DID_UNLOAD_IMAGE:function(){t("ABORT_IMAGE")},REQUEST_ABORT_IMAGE:function(e){t("UNLOAD_IMAGE"),n.file=!1,n.queuedFile=e},DID_SET_SRC:function(e){e.value!==e.prevValue&&(clearTimeout(n.noImageTimeout),t("REQUEST_LOAD_IMAGE",{source:e.value}))},ABORT_IMAGE:function(){if(n.file=null,n.queuedFile){var e=n.queuedFile;n.queuedFile=null,t("REQUEST_LOAD_IMAGE",e)}},REQUEST_LOAD_IMAGE:function(e){var r=e.source,i=e.success,o=void 0===i?function(){}:i,a=e.failure,l=void 0===a?function(t){}:a,c=e.options,u=e.resolveOnConfirm,s=void 0!==u&&u;if(clearTimeout(n.noImageTimeout),!r)return l("no-image-source");null===n.file?(Q(n),n.file={uid:$()},t("DID_REQUEST_LOAD_IMAGE",{source:r}),function(t,e){var n=e.progress;return new Promise(function(e,r){if(F(t))Cn(t,{progress:/^data:/.test(t)?function(){}:n,error:r,load:function(t,n){return e({file:t,fileInstructions:n})}});else if(t instanceof Blob)e({file:t});else{if("IMG"===t.nodeName){var i=function(t){var n=document.createElement("canvas");n.width=t.naturalWidth,n.height=t.naturalHeight,n.getContext("2d").drawImage(t,0,0),n.toBlob(function(t){return e({file:t})})};return t.complete?void i(t):void(t.onload=function(){return i(t)})}"CANVAS"!==t.nodeName?r(t):t.toBlob(function(t){return e({file:t})})}})}(r,{progress:function(e){return null!==e&&t("DID_MAKE_PROGRESS",{progress:e})}}).then(function(e){var r=e.file,i=e.fileInstructions;if(!c&&i){var a=i.crop,u=i.filter,f=i.colorMatrix,d=i.color,h=i.markup,p=i.size;c={crop:a,filter:u?u.id||u.matrix:f,color:d,markup:h,size:p}}if(In(n))return t("ABORT_IMAGE");r.name||(r.name=en()),n.file.orientation=-1,n.file.data=r,t("LOAD_IMAGE",{success:o,failure:l,options:c,resolveOnConfirm:s},!0),t("KICK")}).catch(function(e){if(In(n))return t("ABORT_IMAGE");t("DID_LOAD_IMAGE_ERROR",{error:{status:"IMAGE_LOAD_ERROR",data:e}}),l(e)})):t("REQUEST_ABORT_IMAGE",{source:r,success:o,failure:l,options:c,resolveOnConfirm:s})},LOAD_IMAGE:function(r){var i=r.success,o=r.failure,c=r.options,u=void 0===c?{}:c,s=r.resolveOnConfirm;if(In(n))return t("ABORT_IMAGE");var f=n.file.data;Promise.all([function(t){return new Promise(function(e,n){var r=new Image;r.src=URL.createObjectURL(t),r.onerror=function(t){clearInterval(i),n(t)};var i=setInterval(function(){r.naturalWidth&&r.naturalHeight&&(clearInterval(i),URL.revokeObjectURL(r.src),e({width:r.naturalWidth,height:r.naturalHeight}))},1)})}(f),Be(f)]).then(function(r){var c=l(r,2),f=c[0],d=c[1];if(In(n))return t("ABORT_IMAGE");if(n.file.orientation=e("GET_OUTPUT_CORRECT_IMAGE_EXIF_ORIENTATION")?d:-1,n.file.orientation>-1){var h=f.width,p=f.height;d>=5&&d<=8&&(f.width=p,f.height=h)}var g=e("GET_MIN_IMAGE_SIZE");if(f.width<g.width||f.height<g.height)return t("DID_LOAD_IMAGE_ERROR",{error:{status:"IMAGE_MIN_SIZE_VALIDATION_ERROR",data:{size:f,minImageSize:g}}}),Q(n),void o();var m=$e(f,{width:e("GET_MAX_IMAGE_PREVIEW_WIDTH"),height:e("GET_MAX_IMAGE_PREVIEW_HEIGHT")});if(n.image={x:0,y:0,width:m.width,height:m.height,naturalWidth:f.width,naturalHeight:f.height,aspectRatio:f.height/f.width},e("ALLOW_MANUAL_RESIZE")&&u.size&&(n.size.width=u.size.width,n.size.height=u.size.height,n.size.aspectRatioLocked=!0,n.size.aspectRatioPrevious=!1,n.instructions.size={width:u.size.width,height:u.size.height}),n.instructions.crop=nn(e,n,u.crop?a({},u.crop):null,n.size),n.crop.limitToImageBounds=n.options.cropLimitToImageBounds,!1===n.instructions.crop.scaleToFit&&(n.crop.limitToImageBounds=n.instructions.crop.scaleToFit),void 0===u.filter)n.instructions.filter=n.options.filter;else{var v=u.filter;n.instructions.filter=null===v?v:v.id||v.matrix||v}var y=n.options.markup||[];n.instructions.markup=y.concat(u.markup||[]),n.instructions.color=Object.keys(un).reduce(function(t,e){return t[e]=u.color&&void 0!==u.color[e]?"number"==typeof u.color[e]?u.color[e]:u.color[e].value:n.options["color".concat(rn(e))],t},{}),t("DID_LOAD_IMAGE",{image:a({size:n.file.data.size,name:n.file.data.name,type:n.file.data.type,orientation:d},f)}),n.filePromise={resolveOnConfirm:s,success:i,failure:o}}).catch(function(e){if(In(n))return t("ABORT_IMAGE");t("DID_LOAD_IMAGE_ERROR",{error:{status:"IMAGE_UNKNOWN_ERROR",data:e}}),Q(n),o()})},CHANGE_VIEW:function(e){var r=e.id;n.activeView=r,t("SHOW_VIEW",{id:r})},UPDATE_ROOT_RECT:function(t){var e=t.rect;n.rootRect=e},DID_RESIZE_STAGE:function(r){var i=r.size,o=r.offset,a=r.animate,l=null===n.stage;if(n.stage=St(0,0,i.width,i.height),n.stageOffset=wt(o.x,o.y),!e("GET_ALLOW_PREVIEW_FIT_TO_VIEW")){var c=Math.min(i.width,n.image.naturalWidth),u=Math.min(i.height,n.image.naturalHeight);n.stage=St(0,0,c,u),n.stageOffset=wt(o.x+.5*i.width-.5*c,o.y+.5*i.height-.5*u)}if(l){if(gn(n,e,t),!n.filePromise.resolveOnConfirm){var s=Wt(n.image,n.crop),f=fn(e);n.filePromise.success({crop:s,image:{orientation:n.file.orientation},size:f,output:{type:e("GET_OUTPUT_TYPE"),quality:e("GET_OUTPUT_QUALITY")}})}}else n.instantUpdate=!a,mn(n,e),setTimeout(function(){n.instantUpdate=!1},16)},RESIZE_SET_OUTPUT_SIZE_ASPECT_RATIO_LOCK:function(t){var e=t.value;n.size.aspectRatioLocked=e},RESIZE_SET_OUTPUT_SIZE:function(r){var i=r.width,o=r.height,a=Ht({width:i=i||null,height:o=o||null},e("GET_SIZE_MIN"),e("GET_SIZE_MAX"),e("GET_CROP_RECTANGLE_ASPECT_RATIO"));if(n.size.width=a.width?Math.round(a.width):null,n.size.height=a.height?Math.round(a.height):null,i&&o){var l=o/i;if(l===n.crop.aspectRatio)return;!1===n.size.aspectRatioPrevious&&(n.size.aspectRatioPrevious=n.crop.aspectRatio),t("CROP_SET_ASPECT_RATIO",{value:l})}else!1!==n.size.aspectRatioPrevious&&(t("CROP_SET_ASPECT_RATIO",{value:n.size.aspectRatioPrevious}),n.size.aspectRatioPrevious=!1)},CROP_SET_ASPECT_RATIO:function(t){var r=t.value;if(_n(n),n.crop.aspectRatio=F(r)?bt(r):r,n.crop.aspectRatio&&mn(n,e),n.crop.isDirty=!0,n.size.width&&n.size.height)if(n.crop.aspectRatio){var i=n.size.width*n.crop.aspectRatio,o=ht(i,e("GET_SIZE_MIN").height,e("GET_SIZE_MAX").height);n.size.height=o,n.size.width=o/n.crop.aspectRatio}else n.size.height=null},DID_SET_CROP_ASPECT_RATIO:function(e){var n=e.value,r=e.prevValue;bt(n)!==bt(r)&&t("CROP_SET_ASPECT_RATIO",{value:n})},CROP_ZOOM:function(){n.stage&&(_n(n),mn(n,e))},DID_SET_CROP_LIMIT_TO_IMAGE_BOUNDS:function(e){var r=e.value,i=e.prevValue;n.crop.limitToImageBounds=r,!1===i&&r&&t("CROP_ENABLED_LIMIT_TO_IMAGE_BOUNDS")},CROP_ENABLED_LIMIT_TO_IMAGE_BOUNDS:function(){var t=n.stage,r=n.image,i=n.crop.rectangle.height/n.crop.rectangle.width,o=Lt(t,i);n.crop.rectangle=o,n.crop.transforms=qe(r,n.crop.rectangle,n.crop.transforms,"moving"),n.crop.transforms=qe(r,n.crop.rectangle,n.crop.transforms,"resizing"),n.crop.transforms=qe(r,n.crop.rectangle,n.crop.transforms,"rotating"),n.crop.draft.rectangle=null,n.crop.draft.transforms=null,mn(n,e)},CROP_SET_LIMIT:function(e){var r=e.value,i=e.silent,o=void 0!==i&&i,a=n.crop.limitToImageBounds!==r;n.crop.limitToImageBounds=r,a&&!o&&(n.crop.isDirty=!0),a&&r&&t("CROP_ENABLED_LIMIT_TO_IMAGE_BOUNDS")},CROP_IMAGE_RESIZE_GRAB:function(){wn(n),_n(n)},CROP_IMAGE_ROTATE_GRAB:function(){wn(n),_n(n),n.crop.isRotating=!0},CROP_RECT_DRAG_GRAB:function(){wn(n),_n(n)},CROP_RECT_DRAG_RELEASE:function(){(function(t){t.crop.rectangle=t.crop.draft.rectangle.limited,t.crop.draft.rectangle=null,vn(t),hn(t)})(n),yn(n,e,t)},CROP_RECT_EDGE_DRAG:function(t){var r=t.offset,i=t.origin,o=t.anchor,a=n.image,l=n.stage,c=/n|s/.test(i)?ze:Ne,u=Ye(i,n.crop.rectangle),s=Ye(o,n.crop.rectangle),f=Et({x:u.x+(c===Ne?r.x:0),y:u.y+(c===ze?r.y:0)},l),d=e("GET_MIN_CROP_SIZE"),h=e("GET_MAX_CROP_SIZE");d.width=pt(d.width),d.height=pt(d.height);var p=Tn(n.crop.rectangle,e("GET_MIN_PREVIEW_IMAGE_SIZE"))/(n.crop.draft.transforms.scale||n.crop.transforms.scale);h.width=pt(h.width*p),h.height=pt(h.height*p);var g={x:Math.sign(u.x-s.x),y:Math.sign(u.y-s.y)};n.crop.draft.rectangle=function(t,e,n,r,i,o,a,l,c){var u=o.left,s=o.right,f=o.top,d=o.bottom,h=s-u,p=d-f,g=i.left,m=i.right,v=i.top,y=i.bottom;if(n===ze){if(v=t.y>0?r.y:Math.min(r.y,Math.max(e.y,f)),y=t.y>0?Math.max(r.y,Math.min(e.y,d)):r.y,a){var E=(y-v)/a;g=r.x-.5*E,m=r.x+.5*E}}else if(g=t.x>0?r.x:Math.min(r.x,Math.max(e.x,u)),m=t.x>0?Math.max(r.x,Math.min(e.x,s)):r.x,a){var _=(m-g)*a;v=r.y-.5*_,y=r.y+.5*_}var w,T,x,R,A=l.width,C=l.height;if(n===ze?(w=r.x-.5*A,T=r.x+.5*A,t.y<0?(x=r.y-C,R=r.y):t.y>0&&(x=r.y,R=r.y+C)):(x=r.y-.5*C,R=r.y+.5*C,t.x<0?(w=r.x-A,T=r.x):t.x>0&&(w=r.x,T=r.x+A)),a)if(n===ze){var I=Math.min((y-v)/a,h),O=I*a;g<u&&(m=(g=u)+I),m>s&&(g=(m=s)-I),r.x=g+.5*I,t.y<0?v=r.y-O:t.y>0&&(y=r.y+O)}else{var M=Math.min((m-g)*a,p),S=M/a;v<f&&(y=(v=f)+M),y>d&&(v=(y=d)-M),r.y=v+.5*M,t.x<0?g=r.x-S:t.x>0&&(m=r.x+S)}var b=It({top:v,right:m,bottom:y,left:g}),L=function(){var e=A*a;n===Ne?(v=r.y-.5*e,y=r.y+.5*e):t.y<0?(y=r.y,v=y-e):t.y>0&&(v=r.y,y=v+e)},P=function(){var e=C/a;n===ze?(g=r.x-.5*e,m=r.x+.5*e):t.x<0?(m=r.x,g=m-e):t.x>0&&(g=r.x,m=g+e)};m<T&&(m=T,g=T-A,a&&L()),g>w&&(g=w,m=w+A,a&&L()),v>x&&(v=x,y=x+C,a&&P()),y<R&&(y=R,v=R-C,a&&P());var G=c.width,D=c.height;if(a&&(a<1?G=D/a:D=G*a),m-g>G&&(t.x<0?g=r.x-G:m=r.x+G),y-v>D&&(t.y<0?v=r.y-D:y=r.y+D),m-g==0&&(t.x>0?m=r.x+2:g=r.x-2),y-v==0&&(t.y>0?y=r.y+2:v=r.y-2),Math.round(g)<u||Math.round(m)>s||Math.round(v)<f||Math.round(y)>d){var k=d-f,U=s-u;if(g<u){g=u;var V=Math.min(m-g,U);m=g+V}if(m>s){m=s;var B=Math.min(m-g,U);g=m-B}if(v<f){v=f;var N=Math.min(y-v,k);y=v+N}if(y>d){y=d;var z=Math.min(y-v,k);v=y-z}b=It({top:v,right:m,bottom:y,left:g})}return{free:b,limited:It({top:v,right:m,bottom:y,left:g})}}(g,f,c,s,Ct(n.crop.rectangle),Ct(l),n.crop.aspectRatio,d,h),n.crop.limitToImageBounds&&(n.crop.draft.transforms=Ze(a,n.crop.draft.rectangle.limited,n.crop.transforms))},CROP_RECT_CORNER_DRAG:function(t){var r=t.offset,i=t.origin,o=t.anchor,a=n.image,l=n.stage,c=Ke(i,n.crop.rectangle),u=Ke(o,n.crop.rectangle),s={x:c.x+r.x,y:c.y+r.y},f=e("GET_MIN_CROP_SIZE"),d=e("GET_MAX_CROP_SIZE");f.width=pt(f.width),f.height=pt(f.height);var h=Tn(n.crop.rectangle,e("GET_MIN_PREVIEW_IMAGE_SIZE"))/(n.crop.draft.transforms.scale||n.crop.transforms.scale);d.width=pt(d.width*h),d.height=pt(d.height*h);var p={x:Math.sign(c.x-u.x),y:Math.sign(c.y-u.y)};n.crop.draft.rectangle=function(t,e,n,r,i,o,a){var l=Ct(r),c=l.left,u=l.right,s=l.top,f=l.bottom,d=Et({x:e.x,y:e.y},r),h=t.x>0?n.x:Math.min(d.x,n.x),p=t.x>0?Math.max(n.x,d.x):n.x,g=t.y>0?n.y:Math.min(d.y,n.y),m=t.y>0?Math.max(n.y,d.y):n.y;if(i){var v=d.x-n.x;t.x>0?p=Math.max(n.x,n.x+t.x*v):h=Math.min(n.x,n.x-t.x*v),t.y>0?m=Math.max(n.y,n.y+t.x*v*i):g=Math.min(n.y,n.y-t.x*v*i)}var y=It({top:g,right:p,bottom:m,left:h});if(It({top:g,right:p,bottom:m,left:h}),o.width&&o.height){var E=o.width,_=o.height;i&&(1===i?_=E=Math.max(E,_):E<_?E=_/i:E>_?_=E*i:E=_/i),p-h<E&&(t.x>0?p=n.x+E:h=n.x-E),m-g<_&&(t.y>0?m=n.y+_:g=n.y-_);var w=a.width,T=a.height;i&&(i<1?w=T/i:T=w*i),p-h>w&&(t.x<0?h=n.x-w:p=n.x+w),m-g>T&&(t.y<0?g=n.y-T:m=n.y+T)}if(p-h==0&&(t.x>0?p=n.x+2:h=n.x-2),m-g==0&&(t.y>0?m=n.y+2:g=n.y-2),Math.round(h)<c||Math.round(p)>u||Math.round(g)<s||Math.round(m)>f){var x=f-s,R=u-c;if(h<c){h=c;var A=Math.min(p-h,R);p=h+A,i&&(t.y>0&&(m=n.y+A*i),t.y<0&&(g=n.y-A*i))}if(p>u){p=u;var C=Math.min(p-h,R);h=p-C,i&&(t.y>0&&(m=n.y+C*i),t.y<0&&(g=n.y-C*i))}if(g<s){g=s;var I=Math.min(m-g,x);m=g+I,i&&(t.x>0&&(p=n.x+I/i),t.x<0&&(h=n.x-I/i))}if(m>f){m=f;var O=Math.min(m-g,x);g=m-O,i&&(t.x>0&&(p=n.x+O/i),t.x<0&&(h=n.x-O/i))}y=It({top:g,right:p,bottom:m,left:h})}return{free:y,limited:It({top:g,right:p,bottom:m,left:h})}}(p,s,u,l,n.crop.aspectRatio,f,d),n.crop.limitToImageBounds&&(n.crop.draft.transforms=Ze(a,n.crop.draft.rectangle.limited,n.crop.transforms))},CROP_IMAGE_DRAG_GRAB:function(){return wn(n)||_n(n)},CROP_IMAGE_DRAG_RELEASE:function(){vn(n),hn(n),yn(n,e,t)},CROP_IMAGE_ROTATE_RELEASE:function(){vn(n),yn(n,e,t)},CROP_IMAGE_DRAG:function(t){var e=t.value;_n(n),n.crop.draft.transforms=function(t,e,n,r,i){var o={x:n.translation.x+r.x,y:n.translation.y+r.y},l=a({},We(n),{translation:o}),c=i?qe(t,e,l,"moving"):l;return c.interaction={translation:o},c}(n.image,n.crop.rectangle,n.crop.transforms,e,n.crop.limitToImageBounds)},CROP_IMAGE_RESIZE_RELEASE:function(){e("GET_CROP_RESIZE_MATCH_IMAGE_ASPECT_RATIO")&&function(t){if(t.crop.draft.targetSize>=t.crop.draft.transforms.scale)return!1;if(null!==t.crop.aspectRatio)return!1;if(!1===t.crop.limitToImageBounds)return!1;if(0!==pt(t.crop.rotation.sub,5))return!1;var e=pt(t.crop.rotation.main/sn,5)%2!=0?t.image.width/t.image.height:t.image.height/t.image.width,n=t.crop.rectangle.height/t.crop.rectangle.width;if(e===n)return!1;var r=Lt(t.stage,n);!!Tt(r,t.crop.rectangle)&&(t.crop.rectangle=Lt(t.stage,e))}(n),vn(n),hn(n),yn(n,e,t)},CROP_IMAGE_RESIZE:function(t){var r=t.value;_n(n);var i=n.crop.transforms;n.crop.draft.targetSize=i.scale+i.scale*r,n.crop.draft.transforms=Rn(n.image,n.crop.rectangle,i,n.crop.draft.targetSize,e("GET_MIN_PREVIEW_IMAGE_SIZE"),n.crop.limitToImageBounds)},CROP_IMAGE_RESIZE_MULTIPLY:function(t){var r=t.value;_n(n);var i=n.crop.transforms;n.crop.draft.targetSize=i.scale*r,n.crop.draft.transforms=Rn(n.image,n.crop.rectangle,i,n.crop.draft.targetSize,e("GET_MIN_PREVIEW_IMAGE_SIZE"),n.crop.limitToImageBounds)},CROP_IMAGE_RESIZE_AMOUNT:function(t){var r=t.value;_n(n);var i=n.crop.transforms;n.crop.draft.targetSize=(n.crop.draft.transforms?n.crop.draft.transforms.scale:i.scale)+r,n.crop.draft.transforms=Rn(n.image,n.crop.rectangle,i,n.crop.draft.targetSize,e("GET_MIN_PREVIEW_IMAGE_SIZE"),n.crop.limitToImageBounds)},CROP_IMAGE_ROTATE:function(t){var r=t.value;_n(n),n.crop.isRotating=!0,pn(n,{main:n.crop.rotation.main,sub:r},e("GET_MIN_PREVIEW_IMAGE_SIZE"),!1,n.crop.limitToImageBounds)},CROP_IMAGE_ROTATE_ADJUST:function(t){var r=t.value;_n(n),pn(n,{main:n.crop.rotation.main,sub:Math.min(Math.PI/4,Math.max(-Math.PI/4,n.crop.rotation.sub+r))},e("GET_MIN_PREVIEW_IMAGE_SIZE"),!1,n.crop.limitToImageBounds),vn(n)},CROP_IMAGE_ROTATE_CENTER:function(){_n(n),pn(n,{main:n.crop.rotation.main,sub:0},e("GET_MIN_PREVIEW_IMAGE_SIZE"),!1,n.crop.limitToImageBounds),vn(n)},CROP_IMAGE_ROTATE_LEFT:function(){En(n,e,t),pn(n,{main:n.crop.rotation.main-sn,sub:n.crop.rotation.sub},e("GET_MIN_PREVIEW_IMAGE_SIZE"),-sn,n.crop.limitToImageBounds),vn(n),e("GET_CROP_FORCE_LETTERBOX")&&t("CROP_UPDATE_LETTERBOX")},CROP_IMAGE_ROTATE_RIGHT:function(){En(n,e,t),pn(n,{main:n.crop.rotation.main+sn,sub:n.crop.rotation.sub},e("GET_MIN_PREVIEW_IMAGE_SIZE"),sn,n.crop.limitToImageBounds),vn(n),e("GET_CROP_FORCE_LETTERBOX")&&t("CROP_UPDATE_LETTERBOX")},CROP_IMAGE_FLIP_HORIZONTAL:function(){En(n,e,t),0===pt(n.crop.rotation.main%Math.PI/2,5)?n.crop.flip.horizontal=!n.crop.flip.horizontal:n.crop.flip.vertical=!n.crop.flip.vertical,n.crop.isDirty=!0},CROP_IMAGE_FLIP_VERTICAL:function(){En(n,e,t),0===pt(n.crop.rotation.main%Math.PI/2,5)?n.crop.flip.vertical=!n.crop.flip.vertical:n.crop.flip.horizontal=!n.crop.flip.horizontal,n.crop.isDirty=!0},DID_RECEIVE_IMAGE_DATA:function(t){var e=t.previewData,r=t.thumbData;n.file.preview=e,n.file.thumb=r},MARKUP_SET_VALUE:function(t){var e=t.value;n.markup=e},MARKUP_ADD_DEFAULT:function(n){var r=n.value,i=function(){return-.5+Math.random()},o=.25*e("GET_CROP_RECTANGLE_ASPECT_RATIO"),l=function(){return{width:.25,height:o,x:.5+.5*i()-.125,y:.5+.5*i()-.5*o}},c=function(t){return e("GET_MARKUP_TOOL_VALUES")[t]},u=function(){var t=c("shapeStyle"),e=c("color");return{backgroundColor:t[0]||t[1]?null:e,borderWidth:t[0],borderStyle:t[1]?t[1]:null,borderColor:e}},s={rect:function(){return a({},l(),u())},ellipse:function(){return a({},l(),u())},text:function(){return{x:.5+.5*i()-.1,y:.5+.5*i(),width:0,height:0,fontColor:c("color"),fontSize:c("fontSize"),fontFamily:c("fontFamily"),text:"Text"}},line:function(){var t=c("lineStyle");return a({},l(),{lineColor:c("color"),lineWidth:t[0],lineStyle:t[1]?t[1]:null,lineDecoration:c("lineDecoration")})}}[r]();t("MARKUP_ADD",[r,s])},MARKUP_ADD:function(r){n.markup.forEach(function(t){return t[1].isSelected=!1}),n.markup=n.markup.filter(function(t){return!t[1].isDestroyed});var i=cn(r);n.markup.push(i),n.markup.sort(Ie),"draw"!==e("GET_MARKUP_UTIL")&&t("MARKUP_SELECT",{id:i[1].id}),n.crop.isDirty=!0},MARKUP_SELECT:function(t){var e=t.id;n.markup.forEach(function(t){t[1].isSelected=t[1].id===e,t[1].isDirty=!0})},MARKUP_ELEMENT_DRAG:function(t){var e=t.id,r=t.origin,i=t.offset,o=t.size,a=n.markup.find(function(t){return t[1].id===e});if(a){var l=a[1],c=r.x/o.width,u=r.y/o.height,s=r.width/o.width,f=r.height/o.height,d=i.x/o.width,h=i.y/o.height;l.x=c+d,l.y=u+h,l.width=s,l.height=f,l.left=void 0,l.top=void 0,l.right=void 0,l.bottom=void 0,l.isDirty=!0,n.crop.isDirty=!0}},MARKUP_ELEMENT_RESIZE:function(t){var e=t.id,r=t.corner,i=t.origin,o=t.offset,a=t.size,c=n.markup.find(function(t){return t[1].id===e});if(c){var u=l(c,2),s=u[0],f=u[1],d=(i.x+o.x)/a.width,h=(i.y+o.y)/a.height;if(/n/.test(r))if("line"===s)f.height=f.height-(h-f.y),f.y=h;else{var p=f.y+f.height;h>p&&(h=p),f.height=f.height-(h-f.y),f.y=h}if(/w/.test(r))if("line"===s)f.width=f.width-(d-f.x),f.x=d;else{var g=f.x+f.width;d>g&&(d=g),f.width=f.width-(d-f.x),f.x=d}/s/.test(r)&&(f.height="line"===s?h-f.y:Math.max(0,h-f.y)),/e/.test(r)&&(f.width="line"===s?d-f.x:Math.max(0,d-f.x)),f.left=void 0,f.top=void 0,f.right=void 0,f.bottom=void 0,f.isDirty=!0,n.crop.isDirty=!0}},MARKUP_DELETE:function(e){var r=e.id,i=n.markup.find(function(t){return t[1].id===r});if(i){var o=i[1];o.allowDestroy&&(o.isDestroyed=!0,o.isSelected=!1,o.isDirty=!0);for(var a=null,l=n.markup.length;l>0;){l--;var c=n.markup[l][1];if(!c.isDestroyed&&c.allowDestroy){a=c.id;break}}t("MARKUP_SELECT",{id:a})}},MARKUP_UPDATE:function(t){var e=t.style,r=t.value;n.markupToolValues[e]=r,n.markup.map(function(t){return t[1]}).filter(function(t){return t.isSelected}).forEach(function(t){if("color"===e)t[function(t){return t.borderWidth?"borderColor":t.lineWidth?"lineColor":t.fontColor?"fontColor":t.backgroundColor?"backgroundColor":void 0}(t)]=r;else if("shapeStyle"===e){var n=function(t){var e=t.fontColor,n=t.backgroundColor,r=t.lineColor,i=t.borderColor;return e||n||r||i}(t);t.borderWidth=r[0],t.borderStyle=r[1],t.backgroundColor=r[0]||r[1]?null:n}else"lineStyle"===e?(t.lineWidth=r[0],t.lineStyle=r[1]):t[e]=r;t.isDirty=!0}),n.crop.isDirty=!0}},["color","shapeStyle","lineStyle","textDecoration","fontSize","fontFamily"].reduce(function(e,r){var i=r.split(/(?=[A-Z])/).join("_").toUpperCase(),o=rn(r);return e["SET_MARKUP_"+i]=function(e){var i=e.value;i!==e.prevValue&&(n.options["markup".concat(o)]=i,t("MARKUP_UPDATE",{style:r,value:i}))},e},{}),{COLOR_SET_COLOR_VALUE:function(e){var r=e.key,i=e.value;n.crop.isDirty=!0,t("COLOR_SET_VALUE",{key:r,value:i})},COLOR_SET_VALUE:function(e){var r=e.key,i=e.value;n.colorValues[r]=i,t("SET_COLOR_MATRIX",{key:r,matrix:un[r](i)})}},Object.keys(un).reduce(function(r,i){var o=i.toUpperCase(),a=rn(i);return r["SET_COLOR_".concat(o)]=function(r){var c=r.value;if(c!==r.prevValue){var u=l(e("GET_COLOR_".concat(o,"_RANGE")),2),s=u[0],f=u[1],d=ht(c,s,f);n.options["color".concat(a)]=d,n.instructions.color||(n.instructions.color={}),n.instructions.color[i]=d,t("COLOR_SET_VALUE",{key:i,value:d})}},r},{}),{SET_COLOR_MATRIX:function(e){var r=e.key,i=e.matrix;i?n.colorMatrices[r]=c(i):delete n.colorMatrices[r],t("DID_SET_COLOR_MATRIX",{key:r,matrix:i})},FILTER_SET_FILTER:function(e){var r=e.value;n.crop.isDirty=!0,t("FILTER_SET_VALUE",{value:r})},FILTER_SET_VALUE:function(r){var i=r.value,o=an(i)?i:null;if(F(i)){var a=e("GET_FILTERS");d(a,function(t,e){t===i&&(o=e.matrix())})}n.filter=i,n.filterName=F(i)?i:null,t("SET_COLOR_MATRIX",{key:"filter",matrix:o})},DID_SET_UTIL:function(e){var r=e.value;e.prevValue,-1!==n.options.utils.indexOf(r)&&t("CHANGE_VIEW",{id:r})},DID_SET_FILTER:function(e){var n=e.value;n!==e.prevValue&&(t("FILTER_SET_VALUE",{value:n}),t("SET_DATA",{filter:n}))},DID_SET_SIZE:function(e){var n=e.value;n!==e.prevValue&&t("SET_DATA",{size:n})},DID_SET_CROP:function(e){var n=e.value;n!==e.prevValue&&t("SET_DATA",{crop:n})},DID_SET_MARKUP_UTIL:function(e){var n=e.value;n!==e.prevValue&&n&&(/^(draw|line|text|rect|ellipse)$/.test(n)||(n="select"),t("SWITCH_MARKUP_UTIL",{util:n}))},DID_SET_MARKUP:function(e){var n=e.value,r=e.prevValue;n!==r&&JSON.stringify(n)===JSON.stringify(r)||t("SET_DATA",{markup:n})},SET_DATA:function(r){if(r.size&&e("ALLOW_MANUAL_RESIZE")){var i=a({width:null,height:null},r.size),o=Ht(i,e("GET_SIZE_MIN"),e("GET_SIZE_MAX"),null);n.instructions.size=a({},o),t("RESIZE_SET_OUTPUT_SIZE",o)}r.filter&&(console.log(r.filter),n.instructions.filter=r.filter?r.filter.id||r.filter.matrix:r.colorMatrix),n.instructions.markup=r.markup||[],n.instructions.color=Object.keys(un).reduce(function(t,e){var i=void 0===r.color||void 0===r.color[e],o=n.options["color".concat(rn(e))];return t[e]=i?o:T(r.color[e])?r.color[e]:r.color[e].value,t},{}),r.crop&&(n.instructions.crop=nn(e,n,r.crop,n.size),n.crop.limitToImageBounds=n.options.cropLimitToImageBounds,!1===n.instructions.crop.scaleToFit&&(n.crop.limitToImageBounds=n.instructions.crop.scaleToFit),t("EDIT_RESET"))},DID_SET_INITIAL_STATE:function(t){var r=t.value||{},i=r.crop,o=r.filter,l=r.color,c=r.size,u=void 0===c?{}:c,s=r.markup,f=void 0===s?[]:s,d=a({width:null,height:null},u),h=Ht(d,e("GET_SIZE_MIN"),e("GET_SIZE_MAX"),null);n.instructions.size=a({},h),n.instructions.crop=nn(e,n,i),n.crop.limitToImageBounds=n.options.cropLimitToImageBounds,!1===n.instructions.crop.scaleToFit&&(n.crop.limitToImageBounds=n.instructions.crop.scaleToFit),n.instructions.filter=o||null,n.instructions.color=Object.keys(un).reduce(function(t,e){return t[e]=void 0===l||void 0===l[e]?n.options["color".concat(rn(e))]:l[e],t},{}),n.instructions.markup=f,n.crop.isDirty=!0},GET_DATA:function(r){var i=r.success,o=r.failure,a=r.file,l=r.data;if(!n.file)return o("no-image-source");if(!n.stage)return o("image-not-fully-loaded");var c={file:z(a)?a:e("GET_OUTPUT_FILE"),data:z(l)?l:e("GET_OUTPUT_DATA"),success:i,failure:o};t(c.file?"REQUEST_PREPARE_OUTPUT":"PREPARE_OUTPUT",c)},REQUEST_PREPARE_OUTPUT:function(e){var n=e.file,r=e.data,i=e.success,o=e.failure;t("PREPARE_OUTPUT",{file:n,data:r,success:i,failure:o},!0),t("DID_REQUEST_PREPARE_OUTPUT")},PREPARE_OUTPUT:function(r){var i=r.file,o=r.data,a=r.success,l=void 0===a?function(){}:a,c=r.failure,u=void 0===c?function(){}:c;if(In(n))return t("ABORT_IMAGE");var s=function(e){if(t("DID_PREPARE_OUTPUT"),In(n))return t("ABORT_IMAGE");l(e)},f=function(e){if(In(n))return t("ABORT_IMAGE");u(e)};dn({file:i,data:o},n,e).then(function(e){var r=n.options.afterCreateOutput,i=r?r(e,function(e){return t("DID_REQUEST_POSTPROCESS_OUTPUT",{label:e}),function(e){t("DID_MAKE_PROGRESS",{progress:e})}}):e;Promise.resolve(i).then(s).catch(f)}).catch(f)},EDIT_RESET:function(){_n(n),gn(n,e,t)},EDIT_CONFIRM:function(){if(n.file&&n.stage){_n(n),t("CROP_ZOOM");var r={file:e("GET_OUTPUT_FILE"),data:e("GET_OUTPUT_DATA"),success:function(e){n.filePromise.resolveOnConfirm&&n.filePromise.success(e),t("DID_CONFIRM",{output:e})},failure:console.error};t(r.file?"REQUEST_PREPARE_OUTPUT":"PREPARE_OUTPUT",r)}},EDIT_CANCEL:function(){n.filePromise&&n.filePromise.success(null),t("DID_CANCEL")},EDIT_CLOSE:function(){_n(n)},EDIT_DESTROY:function(){Q(n)},SET_OPTIONS:function(e){var n=e.options;d(n,function(e,n){t("SET_".concat(J(e,"_").toUpperCase()),{value:n})})}})},Mn=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:24;return'<svg width="'.concat(e,'" height="').concat(e,'" viewBox="0 0 ').concat(e," ").concat(e,'" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false">').concat(t,"</svg>")},Sn=D({ignoreRect:!0,ignoreRectUpdate:!0,name:"button",mixins:{styles:["opacity"],animations:{opacity:{type:"tween",duration:250}},apis:["id"],listeners:!0},tag:"button",create:function(t){var e=t.root,n=t.props;e.element.innerHTML="".concat(n.icon||"","<span>").concat(n.label,"</span>"),e.element.setAttribute("type",n.type||"button"),n.name&&n.name.split(" ").forEach(function(t){e.element.className+=" doka--button-".concat(t)}),e.ref.handleClick=function(){"string"==typeof n.action?e.dispatch(n.action):n.action()},e.element.addEventListener("click",e.ref.handleClick),e.ref.handlePointer=function(t){return t.stopPropagation()},e.element.addEventListener("pointerdown",e.ref.handlePointer),n.create&&n.create({root:e,props:n})},destroy:function(t){var e=t.root;e.element.removeEventListener("pointerdown",e.ref.handlePointer),e.element.removeEventListener("click",e.ref.handleClick)}}),bn=D({name:"status-progress",tag:"svg",ignoreRect:!0,ignoreRectUpdate:!0,mixins:{apis:["progress"],animations:{progress:{type:"spring",stiffness:.25,damping:.25,mass:2.5}}},create:function(t){var e=t.root;e.opacity=0,e.progress=0,e.element.setAttribute("data-value",0),e.element.setAttribute("width",24),e.element.setAttribute("height",24),e.element.setAttribute("viewBox","0 0 20 20");var n=e.ref.circle=document.createElementNS("http://www.w3.org/2000/svg","circle"),r={r:5,cx:10,cy:10,fill:"none",stroke:"currentColor","stroke-width":10,transform:"rotate(-90) translate(-20)"};Object.keys(r).forEach(function(t){n.setAttribute(t,r[t])}),e.element.appendChild(n)},write:k({DID_MAKE_PROGRESS:function(t){var e=t.root,n=t.action;e.progress=n.progress,e.element.setAttribute("data-value",n.progress)}},function(t){var e=t.root;e.ref.circle.setAttribute("stroke-dasharray","".concat(31.42*Math.min(1,e.progress)," 31.42"))})}),Ln=D({name:"status-bubble-inner",create:function(t){var e=t.root,n=t.props;n.onClose?e.appendChildView(e.createChildView(Sn,{label:"Close",name:"icon-only status-bubble-close",icon:Mn('<g fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"><path d="M18 6L6 18M6 6l12 12"/></g>'),action:n.onClose})):e.ref.progressIndicator=e.appendChildView(e.createChildView(bn)),e.appendChildView(e.createChildView(function(t){return D({ignoreRect:!0,tag:t,create:function(t){var e=t.root,n=t.props;e.element.textContent=n.text}})}("p"),{text:n.label}))}}),Pn=D({name:"status-bubble",styles:["opacity","translateY"],apis:["markedForRemoval"],animations:{opacity:{type:"tween",duration:500},translateY:{type:"spring",mass:20}},create:function(t){var e=t.root,n=t.props;return e.appendChildView(e.createChildView(Ln,n))}}),Gn=function(t){t.element.dataset.viewStatus="idle",Dn(t)},Dn=function(t){t.ref.busyIndicators.forEach(function(t){t.translateY=-10,t.opacity=0,t.markedForRemoval=!0})},kn=function(t,e,n){t.element.dataset.viewStatus="busy";var r=Un(t,e,n);Dn(t),t.ref.busyIndicators.push(r),r.markedForRemoval=!1,r.translateY=0,r.opacity=1},Un=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return t.appendChildView(t.createChildView(Pn,{translateY:20,opacity:0,label:e,onClose:n}))},Vn=D({name:"edit-status",ignoreRect:!0,create:function(t){var e=t.root;e.ref.busyIndicators=[],e.element.setAttribute("tabindex",-1)},write:k({MISSING_WEBGL:function(t){var e=t.root,n=/fullscreen/.test(e.query("GET_STYLE_LAYOUT_MODE"));kn(e,e.query("GET_LABEL_STATUS_MISSING_WEB_G_L"),n?function(){e.dispatch("EDIT_CANCEL")}:null)},AWAITING_IMAGE:function(t){var e=t.root;kn(e,e.query("GET_LABEL_STATUS_AWAITING_IMAGE"))},DID_PRESENT_IMAGE:function(t){var e=t.root;Gn(e)},DID_LOAD_IMAGE_ERROR:function(t){var e=t.root,n=t.action,r=/fullscreen/.test(e.query("GET_STYLE_LAYOUT_MODE")),i=e.query("GET_LABEL_STATUS_LOAD_IMAGE_ERROR"),o="function"==typeof i?i(n.error):i;kn(e,o,r?function(){e.dispatch("EDIT_CANCEL")}:null)},DID_REQUEST_LOAD_IMAGE:function(t){var e=t.root;kn(e,e.query("GET_LABEL_STATUS_LOADING_IMAGE"))},DID_REQUEST_PREPARE_OUTPUT:function(t){var e=t.root;kn(e,e.query("GET_LABEL_STATUS_PROCESSING_IMAGE"))},DID_REQUEST_POSTPROCESS_OUTPUT:function(t){var e=t.root,n=t.action;kn(e,n.label)},DID_PREPARE_OUTPUT:function(t){var e=t.root;Gn(e)}}),didWriteView:function(t){var e=t.root;e.ref.busyIndicators=e.ref.busyIndicators.filter(function(t){return!t.markedForRemoval||0!==t.opacity||(e.removeChildView(t),!1)})}}),Bn={down:"pointerdown",move:"pointermove",up:"pointerup"},Nn=function(){var t=[],e=function(e){return t.findIndex(function(t){return t.pointerId===e.pointerId})};return{update:function(n){var r=e(n);r<0||(t[r]=n)},multiple:function(){return t.length>1},count:function(){return t.length},active:function(){return t.concat()},push:function(n){e(n)>=0||t.push(n)},pop:function(n){var r=e(n);r<0||t.splice(r,1)}}},zn=function(t,e,n,r){return t.addEventListener(Bn[e],n,r)},Fn=function(t,e,n){return t.removeEventListener(Bn[e],n)},Wn=function(t,e){"contains"in t&&t.contains(e);var n=e;do{if(n===t)return!0}while(n=n.parentNode);return!1},qn=function(t,e,n,r){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{stopPropagation:!0,cancelOnMultiple:!1},o={x:0,y:0},a={enabled:!0,origin:null,cancel:!1,cancelled:!1,pointers:Nn()},l=null,c=function(t,e){e&&(l||u(t,e),cancelAnimationFrame(l),l=requestAnimationFrame(function(){u(t,e),l=null}))},u=function(t,e){return e.apply(null,[t,function(t){return{x:t.pageX-o.x,y:t.pageY-o.y}}(t)])},s=function(n){var r=0===a.pointers.count();r&&(a.active=!1,a.cancel=!1,a.cancelled=!1),a.pointers.push(n),zn(document.documentElement,"up",d),r?(t===n.target||Wn(t,n.target))&&n.isPrimary&&(n.preventDefault(),i.stopPropagation&&(n.stopPropagation(),n.stopImmediatePropagation()),a.active=!0,o.x=n.pageX,o.y=n.pageY,zn(document.documentElement,"move",f),e(n)):i.cancelOnMultiple&&(a.cancel=!0)},f=function(t){t.isPrimary&&(a.cancelled||(t.preventDefault(),i.stopPropagation&&t.stopPropagation(),c(t,n),a.cancel&&(a.cancelled=!0,c(t,r))))},d=function t(e){a.pointers.pop(e),0===a.pointers.count()&&(Fn(document.documentElement,"move",f),Fn(document.documentElement,"up",t)),a.active&&(a.cancelled||(e.preventDefault(),i.stopPropagation&&e.stopPropagation(),c(e,n),c(e,r)))};return zn(document.documentElement,"down",s),{enable:function(){a.enabled||zn(document.documentElement,"down",s),a.enabled=!0},disable:function(){a.enabled&&Fn(document.documentElement,"down",s),a.enabled=!1},destroy:function(){Fn(document.documentElement,"up",d),Fn(document.documentElement,"move",f),Fn(document.documentElement,"down",s)}}},Hn={type:"spring",stiffness:.4,damping:.65,mass:7},Yn=function(t,e){return Object.keys(e).forEach(function(n){t.setAttribute(n,e[n])})},Zn=function(t,e){var n=document.createElementNS("http://www.w3.org/2000/svg",t);return e&&Yn(n,e),n},Xn=["nw","se"],Kn=["nw","n","ne","w","e","sw","s","se"],jn={nw:"nwse",n:"ns",ne:"nesw",w:"ew",e:"ew",sw:"nesw",s:"ns",se:"nwse"},Qn={nw:function(t){return{x:t.x,y:t.y}},n:function(t){return{x:t.x+.5*t.width,y:t.y}},ne:function(t){return{x:t.x+t.width,y:t.y}},w:function(t){return{x:t.x,y:t.y+.5*t.height}},e:function(t){return{x:t.x+t.width,y:t.y+.5*t.height}},sw:function(t){return{x:t.x,y:t.y+t.height}},s:function(t){return{x:t.x+.5*t.width,y:t.y+t.height}},se:function(t){return{x:t.x+t.width,y:t.y+t.height}}},Jn=D({tag:"div",name:"image-markup",ignoreRect:!0,mixins:{styles:["opacity"],animations:{opacity:"spring",markupX:Hn,markupY:Hn,markupWidth:Hn,markupHeight:Hn},listeners:!0,apis:["toolsReference","onSelect","onDrag","markupX","markupY","markupWidth","markupHeight","allowInteraction"]},create:function(t){var e=t.root,n=t.props,r=n.onSelect,i=void 0===r?function(){}:r,o=n.onUpdate,a=void 0===o?function(){}:o,l=Zn("svg",{xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink"});e.ref.canvas=l;var c=e.query("GET_ROOT_SIZE");l.setAttribute("width",c.width),l.setAttribute("height",c.height);var u=document.createElement("input");Yn(u,{type:"text",autocomplete:"off",autocapitalize:"off"}),u.addEventListener("keydown",function(t){t.stopPropagation(),13===t.keyCode||9===t.keyCode?(t.target.blur(),s()):8!==t.keyCode||e.ref.input.value.length||e.dispatch("MARKUP_DELETE",{id:e.ref.selected.id})}),e.appendChild(u),e.ref.input=u,e.ref.elements=[],e.ref.viewSize={width:0,height:0,scale:0},e.ref.resetSelected=function(){return e.ref.selected={id:null,type:null,settings:{}},e.ref.selected},e.ref.resetSelected();var s=function(){e.ref.resetSelected(),i(null)};e.ref.handleDeselect=function(t){var r;e.query("IS_ACTIVE_VIEW","markup")&&e.ref.selected.id&&t.target!==e.ref.removeButton.element&&(r=t.target,e.ref.selected.id!==function(t){return t.id?t:t.parentNode}(r).id&&(function(t){return Wn(e.ref.manipulatorGroup,t)||t===e.ref.input}(t.target)||n.isMarkupUtil(t.target)||s()))},zn(document.body,"down",e.ref.handleDeselect),e.ref.handleTextInput=function(){return a("text",e.ref.input.value)},e.ref.input.addEventListener("input",e.ref.handleTextInput),e.ref.handleAttemptDelete=function(t){e.query("IS_ACTIVE_VIEW","markup")&&(!e.ref.selected.id||8!==t.keyCode&&46!==t.keyCode||(t.stopPropagation(),t.preventDefault(),e.dispatch("MARKUP_DELETE",{id:e.ref.selected.id})))},document.body.addEventListener("keydown",e.ref.handleAttemptDelete);var f=Zn("g"),d=Zn("g",{class:"doka--shape-group"});f.appendChild(d),e.ref.shapeGroup=d;var h=Zn("g",{fill:"none",class:"doka--manipulator-group"}),p=Zn("rect",{x:0,y:0,width:0,height:0,fill:"none"}),g=Zn("path");h.appendChild(g),h.appendChild(p),e.ref.manipulatorPath=g,e.ref.manipulatorRect=p,e.ref.manipulators=[];for(var m=0;m<10;m++){var v=Zn("circle",{r:6,"stroke-width":2,style:"display:none"});h.appendChild(v),e.ref.manipulators.push(v)}f.appendChild(h),e.ref.manipulatorGroup=h,l.appendChild(f),e.ref.shapeOffsetGroup=f,e.ref.removeButton=e.appendChildView(e.createChildView(Sn,{label:e.query("GET_LABEL_MARKUP_REMOVE_SHAPE"),name:"destroy-shape",action:function(){e.dispatch("MARKUP_DELETE",{id:e.ref.selected.id})}})),e.query("IS_ACTIVE_VIEW","markup")&&(e.element.dataset.active=!0),e.ref.drawInput=null,e.ref.drawState={lineColor:null,lineWidth:null,lineStyle:null,points:[]};var y=Zn("path",{fill:"none",class:"doka--draw-path"});e.ref.drawPath=y,l.appendChild(y),e.element.appendChild(l)},destroy:function(t){var e=t.root;e.ref.elements.concat(e.ref.manipulators).forEach(function(t){t.dragger&&t.dragger.destroy()}),e.ref.input.removeEventListener("input",e.ref.handleTextInput),document.body.removeEventListener("keydown",e.ref.handleAttemptDelete),Fn(document.body,"down",e.ref.handleDeselect)},read:function(t){var e=t.root;if(!e.rect.element.hidden)for(var n in e.ref.elements){var r=e.ref.elements[n];if(r&&"text"===r.nodeName&&r.parentNode){var i=r.getBBox();r.bbox={x:i.x,y:i.y,width:i.width,height:i.height}}}},write:k({SHOW_VIEW:function(t){var e=t.root,n=t.props;"markup"===t.action.id?e.element.dataset.active=!0:(e.element.dataset.active=!1,n.onSelect(null))},MARKUP_SET_VALUE:function(t){t.root.ref.shapeGroup.innerHTML=""},UPDATE_ROOT_RECT:function(t){var e=t.root,n=t.action,r=e.ref.canvas;r.setAttribute("width",n.rect.width),r.setAttribute("height",n.rect.height),e.ref.previousScale=null},SWITCH_MARKUP_UTIL:function(t){var e=t.root,n=t.action.util;if(/^(line|text|ellipse|rect)$/.test(n))e.dispatch("MARKUP_ADD_DEFAULT",{value:n}),e.dispatch("SET_MARKUP_UTIL",{value:"select"});else if("draw"===n&&!e.ref.drawInput){var r=e.ref,i=r.drawState,o=r.viewSize,l=0,c=0,u={},s=e.query("GET_MARKUP_DRAW_DISTANCE");e.ref.drawInput=qn(e.element,function(t){var n=e.query("GET_MARKUP_TOOL_VALUES"),r=n.lineStyle[0],a=n.lineStyle[1];i.lineColor=n.color,i.lineWidth=r,i.lineStyle=a,l=t.offsetX-e.markupX,c=t.offsetY-e.markupY,u.x=0,u.y=0,i.points.push({x:l/o.width,y:c/o.height})},function(t,n){if(e.dispatch("KICK"),s){var r=yt(n,u);if(r>s){var a=function(t,e){var n=mt(t,e);return Math.atan2(n.y,n.x)}(u,n)+Math.PI/2,f=s-r;u.x+=Math.sin(a)*f,u.y-=Math.cos(a)*f,i.points.push({x:(l+u.x)/o.width,y:(c+u.y)/o.height})}}else i.points.push({x:(l+n.x)/o.width,y:(c+n.y)/o.height})},function(t,n){i.points.length>1&&e.dispatch("MARKUP_ADD",["path",a({},i)]),i.points=[]})}"draw"!==n&&e.ref.drawInput&&(e.ref.drawInput.destroy(),e.ref.drawInput=null)}},function(t){var e=t.root,n=t.props,r=t.timestamp;if(!(e.opacity<=0)){var i=e.query("GET_CROP",n.id,r);if(i){var o=e.query("GET_MARKUP_UTIL");e.element.dataset.util=o||"";var c=i.markup,u=i.cropStatus,s=n.onSelect,f=n.onDrag,d=e.ref,h=d.manipulatorGroup,p=d.drawPath,g=d.viewSize,m=d.shapeOffsetGroup,v=d.manipulators,y=d.manipulatorPath,E=d.manipulatorRect,_=d.removeButton,w=d.drawState,T=e.query("GET_OUTPUT_WIDTH"),x=e.query("GET_OUTPUT_HEIGHT"),R=u.image,A=u.crop,C=A.width,I=A.height,O=A.widthFloat/A.heightFloat;if(T||x){var M=e.query("GET_OUTPUT_FIT");T&&!x&&(x=T),x&&!T&&(T=x);var S,b=T/C,L=x/I;"force"===M?(C=T,I=x):("cover"===M?S=Math.max(b,L):"contain"===M&&(S=Math.min(b,L)),C*=S,I*=S)}else R.width&&R.height?(C=R.width,I=R.height):R.width&&!R.height?(C=R.width,I=R.width/O):R.height&&!R.width&&(I=R.height,C=R.height*O);var P=w.points.length,G=pt(e.markupX,3),D=pt(e.markupY,3),k=pt(e.markupWidth,3),U=pt(e.markupHeight,3),V=pt(Math.min(e.markupWidth/C,e.markupHeight/I),4);if(g.width=k,g.height=U,g.scale=V,tr(e,{drawLength:P,markupX:G,markupY:D,scale:V,markup:c,currentWidth:C,currentHeight:I})&&(m.setAttribute("transform","translate(".concat(G," ").concat(D,")")),e.ref.previousDrawLength=P,e.ref.previousX=G,e.ref.previousY=D,e.ref.previousScale=V,e.ref.previousCurrentHeight=I,e.ref.previousCurrentWidth=C,e.ref.previousMarkupLength=c.length,!(g.width<1||g.height<1))){var B,N=c.find(function(t){return t[1].isSelected}),z=N&&e.ref.selected.id!==N[1].id||e.ref.selected.id&&!N;if(B=N?e.ref.selected={id:N[1].id,type:N[0],settings:N[1]}:e.ref.resetSelected(),w.points.length){var F=pe(w,g,V);return F.d=ve(w.points.map(function(t){return{x:G+t.x*g.width,y:D+t.y*g.height}})),void Yn(p,F)}p.removeAttribute("d"),e.ref.input.hidden="text"!==e.ref.selected.type,_.element.dataset.active=null!==e.ref.selected.id,y.setAttribute("style","opacity:0"),E.setAttribute("style","opacity:0"),v.forEach(function(t){return t.setAttribute("style","opacity:0;pointer-events:none;")});var W=e.query("GET_MARKUP_FILTER");c.filter(W).sort(Ie).forEach(function(t,r){var i=l(t,2),o=i[0],c=i[1],u=c.id,d=c.isDestroyed,p=c.isDirty,m=c.isSelected,w=c.allowSelect,T=c.allowMove,x=c.allowResize,R=c.allowInput;if(d){var A=e.ref.elements[u];A&&(A.dragger&&A.dragger.destroy(),e.ref.elements[u]=null,A.parentNode.removeChild(A))}else{var C,I,O,M=e.ref.elements[u];if(M||(M=Ae(o,c),e.ref.elements[u]=M,w?(M.dragger=qn(M,function(){I=Date.now(),C=a({},M.rect),(O=u===e.ref.selected.id)||s(u)},function(t,e){T&&f(u,C,e,g,V)},function(t,n){if(R&&"text"===o&&O){var r=vt({x:0,y:0},n),i=Date.now()-I;if(!(r>10||i>750)){e.ref.input.focus();var a=e.markupX+M.bbox.x,l=M.bbox.width,c=(t.offsetX-a)/l,u=Math.round(e.ref.input.value.length*c);e.ref.input.setSelectionRange(u,u)}}}),M.dragger.disable()):M.setAttribute("style","pointer-events:none;")),M.dragger&&(n.allowInteraction?M.dragger.enable():M.dragger.disable()),r!==M.index){M.index=r;var S=e.ref.shapeGroup;S.insertBefore(M,S.childNodes[r+1])}if(p&&Ce(M,o,c,g,V),m){var b=_.rect.element.width,L=_.rect.element.height,P=e.markupX-.5*b,G=e.markupY-L-15,D="text"===o?M.bbox:M.rect,k=!1,U=function(t){var e=t.fontColor,n=t.backgroundColor,r=t.lineColor,i=t.borderColor;return e||n||r||i}(c);if(U){var B=Yt(U);k=(.2126*B[0]+.7152*B[1]+.0722*B[2])/255>.65,h.setAttribute("is-bright-color",k)}"line"===o?(P+=D.x,G+=D.y,Yn(y,{d:"M ".concat(D.x," ").concat(D.y," L ").concat(D.x+D.width," ").concat(D.y+D.height),style:"opacity:1"})):"path"===o?(P+=(D={x:c.points[0].x*g.width,y:c.points[0].y*g.height,width:0,height:0}).x,G+=D.y,Yn(y,{d:ve(c.points.map(function(t){return{x:t.x*g.width,y:t.y*g.height}})),style:"opacity:1"})):D&&(P+=D.x+.5*D.width,G+=D.y,Yn(E,{x:D.x-("text"===o?5:0),y:D.y,width:D.width+("text"===o?10:0),height:D.height,style:"opacity:1"}));var N=e.markupY+10,z=e.markupY+e.markupHeight-10,F=e.markupX+10,W=e.markupX+e.markupWidth-10;if(G<N?G=N:G+L>z&&(G=z-L),P<F?P=F:P+b>W&&(P=W-b),D||(_.element.dataset.active="false"),_.element.setAttribute("style","transform: translate3d(".concat(P,"px, ").concat(G,"px, 0)")),"text"===o&&D){var q=D.width+65,H=e.markupWidth-D.x,Y="\n                        width: ".concat(Math.min(q,H),"px;\n                        height: ").concat(D.height,"px;\n                        color: ").concat(M.getAttribute("fill"),";\n                        font-family: ").concat(M.getAttribute("font-family"),";\n                        font-size: ").concat(M.getAttribute("font-size").replace(/px/,""),"px;\n                        font-weight: ").concat(M.getAttribute("font-weight")||"normal",";\n                    ");ut()?Y+="\n                            left: ".concat(Math.round(e.markupX+D.x),"px;\n                            top: ").concat(Math.round(e.markupY+D.y),"px;\n                        "):Y+="\n                            transform: translate3d(".concat(Math.round(e.markupX+D.x),"px,").concat(Math.round(e.markupY+D.y),"px,0);\n                        "),e.ref.input.setAttribute("style",Y),M.setAttribute("fill","none")}if("text"===o)return;if(!x)return;var Z="line"===o?Xn:Kn;v.forEach(function(t,e){var n=Z[e];if(n){var r="line"===o?"move":"".concat(jn[n],"-resize"),i=Qn[n](M.rect);Yn(t,{cx:i.x,cy:i.y,style:"opacity:1;cursor:".concat(r)})}})}c.isDirty=!1}}),z&&(nr(e),"text"===B.type?e.ref.input.value=B.settings.text:e.ref.selected.id&&er(e,n.onResize))}}}})}),$n=function(t){return t.forEach(function(t){return t[1].isDirty=!0})},tr=function(t,e){var n=e.drawLength,r=e.markup,i=e.markupX,o=e.markupY,a=e.currentWidth,l=e.currentHeight,c=e.scale;return!(n===t.ref.previousDrawLength&&(i!==t.ref.previousX?($n(r),0):o!==t.ref.previousY?($n(r),0):c!==t.ref.previousScale?($n(r),0):l!==t.ref.previousCurrentHeight?($n(r),0):a!==t.ref.previousCurrentWidth?($n(r),0):r.length===t.ref.previousMarkupLength&&!r.find(function(t){return t[1].isDirty})))},er=function(t,e){var n=t.ref.selected.id,r="g"===t.ref.elements[n].nodeName?Xn:Kn;t.ref.manipulators.forEach(function(i,o){var a=r[o];if(a){var l=null;i.dragger=qn(i,function(){l={x:parseFloat(p(i,"cx")),y:parseFloat(p(i,"cy"))}},function(r,i){e(n,a,l,i,t.ref.viewSize)},null,{stopPropagation:!0})}})},nr=function(t){t.ref.manipulators.forEach(function(t){t.dragger&&(t.dragger.destroy(),t.dragger=null)})},rr={38:"up",40:"down",37:"left",39:"right",189:"minus",187:"plus",72:"h",76:"l",81:"q",82:"r",84:"t",86:"v",90:"z",219:"left_bracket",221:"right_bracket"},ir=function(t,e,n,r,i){var o=null,a=!0,l={enabled:!0},c=function(t){var i=rr[t.keyCode]||t.keyCode;n[i]&&(t.stopPropagation(),a&&(o=e(i),a=!1),n[i](o),r(o))},u=function(t){var e=rr[t.keyCode]||t.keyCode;n[e]&&(t.stopPropagation(),i(o),a=!0)};return t.addEventListener("keydown",c),t.addEventListener("keyup",u),{enable:function(){l.enabled||(t.addEventListener("keydown",c),t.addEventListener("keyup",u)),l.enabled=!0},disable:function(){l.enabled&&(t.removeEventListener("keydown",c),t.removeEventListener("keyup",u)),l.enabled=!1},destroy:function(){t.removeEventListener("keydown",c),t.removeEventListener("keyup",u)}}},or={1:function(){return[1,0,0,1,0,0]},2:function(t){return[-1,0,0,1,t,0]},3:function(t,e){return[-1,0,0,-1,t,e]},4:function(t,e){return[1,0,0,-1,0,e]},5:function(){return[0,1,1,0,0,0]},6:function(t,e){return[0,1,-1,0,e,0]},7:function(t,e){return[0,-1,-1,0,e,t]},8:function(t){return[0,-1,1,0,0,t]}},ar=function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,i=arguments.length>4?arguments[4]:void 0;e=Math.round(e),n=Math.round(n);var o=i||document.createElement("canvas"),a=o.getContext("2d");return r>=5&&r<=8?(o.width=n,o.height=e):(o.width=e,o.height=n),a.save(),function(t,e,n,r){-1!==r&&t.transform.apply(t,or[r](e,n))}(a,e,n,r),a.drawImage(t,0,0,e,n),a.restore(),o},lr=function(){self.onmessage=function(t){createImageBitmap(t.data.message.file).then(function(e){self.postMessage({id:t.data.id,message:e},[e])})}},cr=function(t,e,n){var r=t.createShader(n);return t.shaderSource(r,e),t.compileShader(r),r},ur=function(t,e,n){var r=t.createProgram();return t.attachShader(r,cr(t,e,t.VERTEX_SHADER)),t.attachShader(r,cr(t,n,t.FRAGMENT_SHADER)),t.linkProgram(r),r},sr=function(){return{x:arguments.length<3?0:arguments.length<=0?void 0:arguments[0],y:arguments.length<3?0:arguments.length<=1?void 0:arguments[1],width:arguments.length<3?arguments.length<=0?void 0:arguments[0]:arguments.length<=2?void 0:arguments[2],height:1===arguments.length?arguments.length<=0?void 0:arguments[0]:arguments.length<3?arguments.length<=1?void 0:arguments[1]:arguments.length<=3?void 0:arguments[3]}},fr=function(t,e){return{x:t.x*e,y:t.y*e,width:t.width*e,height:t.height*e}},dr=function(){var t=new Float32Array(16);return t[0]=1,t[5]=1,t[10]=1,t[15]=1,t},hr=function(t,e,n,r,i){var o=1/Math.tan(e/2),a=1/(r-i);t[0]=o/n,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=o,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[11]=-1,t[12]=0,t[13]=0,t[15]=0,t[10]=(i+r)*a,t[14]=2*i*r*a},pr=function(t,e){var n=e[0],r=e[1],i=e[2];t[12]=t[0]*n+t[4]*r+t[8]*i+t[12],t[13]=t[1]*n+t[5]*r+t[9]*i+t[13],t[14]=t[2]*n+t[6]*r+t[10]*i+t[14],t[15]=t[3]*n+t[7]*r+t[11]*i+t[15]},gr=function(t,e){var n=e[0],r=e[1],i=e[2];t[0]=t[0]*n,t[1]=t[1]*n,t[2]=t[2]*n,t[3]=t[3]*n,t[4]=t[4]*r,t[5]=t[5]*r,t[6]=t[6]*r,t[7]=t[7]*r,t[8]=t[8]*i,t[9]=t[9]*i,t[10]=t[10]*i,t[11]=t[11]*i},mr=function(t,e){var n=Math.sin(e),r=Math.cos(e),i=t[4],o=t[5],a=t[6],l=t[7],c=t[8],u=t[9],s=t[10],f=t[11];t[4]=i*r+c*n,t[5]=o*r+u*n,t[6]=a*r+s*n,t[7]=l*r+f*n,t[8]=c*r-i*n,t[9]=u*r-o*n,t[10]=s*r-a*n,t[11]=f*r-l*n},vr=function(t,e){var n=Math.sin(e),r=Math.cos(e),i=t[0],o=t[1],a=t[2],l=t[3],c=t[8],u=t[9],s=t[10],f=t[11];t[0]=i*r-c*n,t[1]=o*r-u*n,t[2]=a*r-s*n,t[3]=l*r-f*n,t[8]=i*n+c*r,t[9]=o*n+u*r,t[10]=a*n+s*r,t[11]=l*n+f*r},yr=function(t,e){var n=Math.sin(e),r=Math.cos(e),i=t[0],o=t[1],a=t[2],l=t[3],c=t[4],u=t[5],s=t[6],f=t[7];t[0]=i*r+c*n,t[1]=o*r+u*n,t[2]=a*r+s*n,t[3]=l*r+f*n,t[4]=c*r-i*n,t[5]=u*r-o*n,t[6]=s*r-a*n,t[7]=f*r-l*n},Er="\nattribute vec4 aPosition;\nvoid main() {\n\tgl_Position = aPosition;\n}\n",_r=function(t,e,n){var r=sr(),i=null,o=function(t){return t*Math.PI/180}(30),a=Math.tan(o/2),l={antialias:!1,alpha:!1},u=t.getContext("webgl",l)||t.getContext("experimental-webgl",l);if(!u)return null;u.enable(u.BLEND),u.blendFunc(u.SRC_ALPHA,u.ONE_MINUS_SRC_ALPHA);var s=ur(u,Er,"\nprecision mediump float;\n\nuniform vec2 uViewportSize;\nuniform vec3 uColorStart;\nuniform vec3 uColorEnd;\nuniform vec2 uOverlayLeftTop;\nuniform vec2 uOverlayRightBottom;\nuniform vec4 uColorCanvasBackground;\n\nvoid main() {\n\n\tfloat x = gl_FragCoord.x;\n\tfloat y = gl_FragCoord.y;\n\n\tvec2 center = vec2(.5, .5);\n\tvec2 st = vec2(x / uViewportSize.x, y / uViewportSize.y);\n\tfloat mixValue = distance(st, center) * 1.5; // expand outside view (same as doka--root::after)\n\tvec3 color = mix(uColorStart, uColorEnd, mixValue);\n\n\tif (uColorCanvasBackground[3] == 1.0) {\n\n\t\tfloat innerLeft = uOverlayLeftTop.x;\n\t\tfloat innerRight = uOverlayRightBottom.x;\n\t\tfloat innerTop = uOverlayRightBottom.y;\n\t\tfloat innerBottom = uOverlayLeftTop.y;\n\n\t\tif (x < innerLeft || x > innerRight || y < innerTop || y > innerBottom) {\n\t\t\tgl_FragColor = vec4(color, 1.0);\n\t\t\treturn;\n\t\t}\n\n\t\tgl_FragColor = uColorCanvasBackground;\n\t\treturn;\n\t}\n\t\n\tgl_FragColor = vec4(color, 1.0);\n}\n"),f=u.getUniformLocation(s,"uColorStart"),d=u.getUniformLocation(s,"uColorEnd"),h=u.getUniformLocation(s,"uViewportSize"),p=u.getAttribLocation(s,"aPosition"),g=u.getUniformLocation(s,"uOverlayLeftTop"),m=u.getUniformLocation(s,"uOverlayRightBottom"),v=u.getUniformLocation(s,"uColorCanvasBackground"),y=u.createBuffer(),E=new Float32Array([1,-1,1,1,-1,-1,-1,1]);u.bindBuffer(u.ARRAY_BUFFER,y),u.bufferData(u.ARRAY_BUFFER,E,u.STATIC_DRAW),u.bindBuffer(u.ARRAY_BUFFER,null);var _=ur(u,Er,"\nprecision mediump float;\n\nuniform vec2 uOverlayLeftTop;\nuniform vec2 uOverlayRightBottom;\nuniform vec4 uOutlineColor;\nuniform float uOutlineWidth;\n\nvoid main() {\n\n\tfloat x = gl_FragCoord.x;\n\tfloat y = gl_FragCoord.y;\n\n\tfloat innerLeft = uOverlayLeftTop.x;\n\tfloat innerRight = uOverlayRightBottom.x;\n\tfloat innerTop = uOverlayRightBottom.y;\n\tfloat innerBottom = uOverlayLeftTop.y;\n\n\tfloat outerLeft = innerLeft - uOutlineWidth;\n\tfloat outerRight = innerRight + uOutlineWidth;\n\tfloat outerTop = innerTop - uOutlineWidth;\n\tfloat outerBottom = innerBottom + uOutlineWidth;\n\t\n\tif (x < outerLeft || x > outerRight || y < outerTop || y > outerBottom) {\n\t\tdiscard;\n\t}\n\n\tif (x < innerLeft || x > innerRight || y < innerTop || y > innerBottom) {\n\t\tgl_FragColor = uOutlineColor;\n\t}\n}\n"),w=u.getAttribLocation(_,"aPosition"),T=u.getUniformLocation(_,"uOutlineWidth"),x=u.getUniformLocation(_,"uOutlineColor"),R=u.getUniformLocation(_,"uOverlayLeftTop"),A=u.getUniformLocation(_,"uOverlayRightBottom"),C=u.createBuffer(),I=new Float32Array([1,-1,1,1,-1,-1,-1,1]);u.bindBuffer(u.ARRAY_BUFFER,C),u.bufferData(u.ARRAY_BUFFER,I,u.STATIC_DRAW),u.bindBuffer(u.ARRAY_BUFFER,null);var O=ur(u,"\nattribute vec4 aPosition;\nattribute vec2 aTexCoord;\nuniform mat4 uMatrix;\n\n// send to fragment shader\nvarying vec2 vTexCoord;\nvarying vec4 vPosition;\n\nvoid main () {\n    vPosition = uMatrix * aPosition;\n    gl_Position = vPosition;\n    vTexCoord = aTexCoord;\n}\n","\nprecision mediump float;\n\nuniform sampler2D uTexture;\nuniform vec2 uTextureSize;\n\nuniform float uColorOpacity;\nuniform mat4 uColorMatrix;\nuniform vec4 uColorOffset;\n\nuniform vec4 uOverlayColor;\nuniform vec2 uOverlayLeftTop;\nuniform vec2 uOverlayRightBottom;\n\n// received from vertex shader\nvarying vec2 vTexCoord;\nvarying vec4 vPosition;\n\nvoid main () {\n\n\t// get texture color\n\tvec4 color = texture2D(uTexture, vTexCoord);\n\t\n\t// apply color matrix\n\tcolor = color * uColorMatrix + uColorOffset;\n\n\t// test if falls within \n    if ((gl_FragCoord.x < uOverlayLeftTop.x || gl_FragCoord.x > uOverlayRightBottom.x) || \n        (gl_FragCoord.y > uOverlayLeftTop.y || gl_FragCoord.y < uOverlayRightBottom.y)) {\n\t\tcolor *= uOverlayColor;\n\t}\n\t\n    gl_FragColor = color * uColorOpacity;\n}\n");u.useProgram(O);var M=u.getUniformLocation(O,"uMatrix"),S=u.getUniformLocation(O,"uTexture"),b=u.getUniformLocation(O,"uTextureSize"),L=u.getUniformLocation(O,"uOverlayColor"),P=u.getUniformLocation(O,"uOverlayLeftTop"),G=u.getUniformLocation(O,"uOverlayRightBottom"),D=u.getUniformLocation(O,"uColorOpacity"),k=u.getUniformLocation(O,"uColorOffset"),U=u.getUniformLocation(O,"uColorMatrix"),V=u.getAttribLocation(O,"aPosition"),B=u.getAttribLocation(O,"aTexCoord"),N=function(t,e,n,r,i){var o=t.createTexture();t.activeTexture(t.TEXTURE0+r),t.bindTexture(t.TEXTURE_2D,o),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MIN_FILTER,t.LINEAR),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MAG_FILTER,t.LINEAR),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_S,t.CLAMP_TO_EDGE),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_T,t.CLAMP_TO_EDGE),t.uniform1i(e,r),t.uniform2f(n,i.width,i.height);try{t.texImage2D(t.TEXTURE_2D,0,t.RGBA,t.RGBA,t.UNSIGNED_BYTE,i)}catch(e){t.texImage2D(t.TEXTURE_2D,0,t.RGBA,i.width,i.height,0,t.RGBA,t.UNSIGNED_BYTE,null)}return o}(u,S,b,0,e),z=e.width*n,F=e.height*n,W=-.5*z,q=.5*F,H=.5*z,Y=-.5*F,Z=new Float32Array([W,q,W,Y,H,q,H,Y]),X=new Float32Array([0,0,0,1,1,0,1,1]),K=Z.length/2,j=u.createBuffer();u.bindBuffer(u.ARRAY_BUFFER,j),u.bufferData(u.ARRAY_BUFFER,Z,u.STATIC_DRAW),u.bindBuffer(u.ARRAY_BUFFER,null);var Q=u.createBuffer();return u.bindBuffer(u.ARRAY_BUFFER,Q),u.bufferData(u.ARRAY_BUFFER,X,u.STATIC_DRAW),u.bindBuffer(u.ARRAY_BUFFER,null),{release:function(){t.width=1,t.height=1},resize:function(e,o){t.width=e*n,t.height=o*n,t.style.width="".concat(e,"px"),t.style.height="".concat(o,"px"),r.width=e*n,r.height=o*n,i=function(t,e){return t/e}(r.width,r.height),u.viewport(0,0,u.canvas.width,u.canvas.height)},update:function(t,l,E,I,S,b,z,F,W,q,H,Y,Z,X,J,$,tt,et,nt){H=H?fr(H,n):sr(r.width,r.height);var rt=.5*r.width,it=.5*r.height,ot=e.width*n,at=e.height*n;t*=n,l*=n,E*=n,I*=n;var lt=at/2/a*(r.height/H.height)*-1;lt/=-a*lt*2/r.height;var ct=.5*ot,ut=.5*at;t-=ct,l-=ut;var st=F,ft=-(rt-ct)+E,dt=it-ut-I,ht=dr();hr(ht,o,i,1,2*-lt),pr(ht,[ft,dt,lt]),pr(ht,[t,-l,0]),gr(ht,[st,st,st]),yr(ht,-z),pr(ht,[-t,l,0]),vr(ht,b),mr(ht,S),u.clearColor(X[0],X[1],X[2],1),u.clear(u.COLOR_BUFFER_BIT),u.useProgram(s),u.uniform3fv(f,J),u.uniform3fv(d,$),u.uniform4fv(v,nt.map(function(t,e){return e<3?t/255:t})),u.uniform2f(h,r.width,r.height),u.uniform2f(g,Y.x*n,r.height-Y.y*n),u.uniform2f(m,(Y.x+Y.width)*n,r.height-(Y.y+Y.height)*n),u.bindBuffer(u.ARRAY_BUFFER,y),u.vertexAttribPointer(p,2,u.FLOAT,!1,0,0),u.enableVertexAttribArray(p),u.drawArrays(u.TRIANGLE_STRIP,0,4),u.useProgram(O),u.bindFramebuffer(u.FRAMEBUFFER,null),u.bindTexture(u.TEXTURE_2D,N),u.bindBuffer(u.ARRAY_BUFFER,j),u.vertexAttribPointer(V,2,u.FLOAT,!1,0,0),u.enableVertexAttribArray(V),u.bindBuffer(u.ARRAY_BUFFER,Q),u.vertexAttribPointer(B,2,u.FLOAT,!1,0,0),u.enableVertexAttribArray(B),u.uniformMatrix4fv(M,!1,ht),Y=fr(Y,n),u.uniform2f(P,Y.x,r.height-Y.y),u.uniform2f(G,Y.x+Y.width,r.height-(Y.y+Y.height)),u.uniform4fv(L,Z),u.uniform1f(D,q),u.uniform4f(k,W[4],W[9],W[14],W[19]),u.uniformMatrix4fv(U,!1,[].concat(c(W.slice(0,4)),c(W.slice(5,9)),c(W.slice(10,14)),c(W.slice(15,19)))),u.drawArrays(u.TRIANGLE_STRIP,0,K),u.useProgram(_),u.uniform1f(T,tt),u.uniform4fv(x,et),u.uniform2f(R,Y.x,r.height-Y.y),u.uniform2f(A,Y.x+Y.width,r.height-(Y.y+Y.height)),u.bindBuffer(u.ARRAY_BUFFER,C),u.vertexAttribPointer(w,2,u.FLOAT,!1,0,0),u.enableVertexAttribArray(w),u.drawArrays(u.TRIANGLE_STRIP,0,4)}}},wr=function(t){var e=0,n={},r=x(t),i=x(t),o=x(t),l=x(t);return r.onupdate=function(t){return n.x=t},r.oncomplete=function(){return e++},i.onupdate=function(t){return n.y=t},i.oncomplete=function(){return e++},o.onupdate=function(t){return n.width=t},o.oncomplete=function(){return e++},l.onupdate=function(t){return n.height=t},l.oncomplete=function(){return e++},{interpolate:function(t){r.interpolate(t),i.interpolate(t),o.interpolate(t),l.interpolate(t)},setTarget:function(t){e=0,r.target=t?t.x:null,i.target=t?t.y:null,o.target=t?t.width:null,l.target=t?t.height:null},getRect:function(){return a({},n)},isStable:function(){return 4===e}}},Tr=function(t){var e=0,n={},r=x(t),i=x(t),o=x(t);return r.onupdate=function(t){return n.r=t},r.oncomplete=function(){return e++},i.onupdate=function(t){return n.g=t},i.oncomplete=function(){return e++},o.onupdate=function(t){return n.b=t},o.oncomplete=function(){return e++},{interpolate:function(t){r.interpolate(t),i.interpolate(t),o.interpolate(t)},setTarget:function(t){e=0,r.target=t?t[0]:null,i.target=t?t[1]:null,o.target=t?t[2]:null},getColor:function(){return[n.r,n.g,n.b]},isStable:function(){return 3===e}}},xr={stiffness:.25,damping:.25,mass:2.5},Rr=[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0],Ar=D({name:"image-gl",ignoreRect:!0,ignoreRectUpdate:!0,mixins:{apis:["top","left","width","height","xOrigin","yOrigin","xTranslation","yTranslation","xRotation","yRotation","zRotation","scale","overlay","stage","colorMatrix","colorOpacity","overlayOpacity","outlineWidth","isDraft"],animations:{xTranslation:Hn,yTranslation:Hn,xOrigin:Hn,yOrigin:Hn,scale:Hn,xRotation:{type:"spring",stiffness:.25,damping:.25,mass:2.5},yRotation:{type:"spring",stiffness:.25,damping:.25,mass:2.5},zRotation:{type:"spring",stiffness:.25,damping:.25,mass:2.5},colorOpacity:{type:"tween",delay:150,duration:750},overlayOpacity:"spring",introScale:{type:"spring",stiffness:.25,damping:.75,mass:15},outlineWidth:Hn}},create:function(t){var e=t.root;e.ref.canvas=document.createElement("canvas"),e.ref.canvas.width=0,e.ref.canvas.height=0,e.appendChild(e.ref.canvas),e.ref.gl=null,e.introScale=1,e.ref.isPreview="preview"===e.query("GET_STYLE_LAYOUT_MODE"),e.ref.shouldZoom=!e.ref.isPreview,e.ref.didZoom=!1,e.ref.backgroundColor=null,e.ref.backgroundColorSpring=Tr(xr),e.ref.backgroundColorCenter=null,e.ref.backgroundColorCenterSpring=Tr(xr),e.ref.overlaySpring=wr(Hn),e.ref.stageSpring=wr(Hn),e.ref.outlineSpring=x(Hn),e.ref.colorMatrixSpring=[],e.ref.colorMatrixStable=!0,e.ref.colorMatrixStableCount=0,e.ref.colorMatrixPositions=[];for(var n=0;n<20;n++)!function(){var t=n,r=x(xr);r.target=Rr[t],r.onupdate=function(n){e.ref.colorMatrixPositions[t]=n},r.oncomplete=function(){e.ref.colorMatrixStableCount++},e.ref.colorMatrixSpring[t]=r}();e.ref.dragger=qn(e.element,function(){e.dispatch("CROP_IMAGE_DRAG_GRAB")},function(t,n){e.dispatch("CROP_IMAGE_DRAG",{value:n})},function(){e.dispatch("CROP_IMAGE_DRAG_RELEASE")},{cancelOnMultiple:!0});var r=0,i=0;e.ref.keyboard=ir(e.element,function(){return r=0,i=0,{x:0,y:0}},{up:function(t){t.y-=20},down:function(t){t.y+=20},left:function(t){t.x-=20},right:function(t){t.x+=20},plus:function(){r+=.1,e.dispatch("CROP_IMAGE_RESIZE_AMOUNT",{value:r}),e.dispatch("CROP_IMAGE_RESIZE_RELEASE")},minus:function(){r-=.1,e.dispatch("CROP_IMAGE_RESIZE_AMOUNT",{value:r}),e.dispatch("CROP_IMAGE_RESIZE_RELEASE")},left_bracket:function(){i-=Math.PI/128,e.dispatch("CROP_IMAGE_ROTATE_ADJUST",{value:i})},right_bracket:function(){i+=Math.PI/128,e.dispatch("CROP_IMAGE_ROTATE_ADJUST",{value:i})},h:function(){e.dispatch("CROP_IMAGE_FLIP_HORIZONTAL")},l:function(){e.dispatch("CROP_IMAGE_ROTATE_LEFT")},q:function(){e.dispatch("CROP_RESET")},r:function(){e.dispatch("CROP_IMAGE_ROTATE_RIGHT")},v:function(){e.dispatch("CROP_IMAGE_FLIP_VERTICAL")},z:function(){e.dispatch("CROP_ZOOM")}},function(t){t&&e.dispatch("CROP_IMAGE_DRAG",{value:t})},function(t){t&&e.dispatch("CROP_IMAGE_DRAG_RELEASE")});var o=e.query("GET_FILE"),a=URL.createObjectURL(o.data),l=function(t){var n=$e(t,{width:e.query("GET_MAX_IMAGE_PREVIEW_WIDTH"),height:e.query("GET_MAX_IMAGE_PREVIEW_HEIGHT")}),r=ar(t,n.width,n.height,o.orientation),i=Math.max(1,.75*window.devicePixelRatio),a=r.height/r.width,l=96*i,c=ar(r,a>1?l:l/a,a>1?l*a:l),u=r.getContext("2d").getImageData(0,0,r.width,r.height),s=c.getContext("2d").getImageData(0,0,c.width,c.height);oe(r),oe(c),e.ref.gl=_r(e.ref.canvas,u,i),e.ref.gl?(e.dispatch("DID_RECEIVE_IMAGE_DATA",{previewData:u,thumbData:s}),e.dispatch("DID_PRESENT_IMAGE")):e.dispatch("MISSING_WEBGL")},c=function(){(function(t){return new Promise(function(e,n){var r=new Image;r.onload=function(){e(r)},r.onerror=function(t){n(t)},r.src=t})})(a).then(l)};if(function(t){return"createImageBitmap"in window&&function(t){return/^image/.test(t.type)&&!/svg/.test(t.type)}(t)}(o.data)){var u=Le(lr);u.post({file:o.data},function(t){u.terminate(),t?l(t):c()})}else c();e.ref.canvasStyle=getComputedStyle(e.ref.canvas),e.ref.previousBackgroundColor,e.ref.previousLeft,e.ref.previousTop,e.ref.previousWidth,e.ref.previousHeight},destroy:function(t){var e=t.root;e.ref.gl&&(e.ref.gl.release(),e.ref.gl=null),e.ref.dragger.destroy()},read:function(t){var e=t.root,n=e.ref.canvasStyle.backgroundColor,r=e.ref.canvasStyle.color;if("transparent"!==r&&""!==r||(r=null),"transparent"!==n&&""!==n||(n=null),n&&n!==e.ref.previousBackgroundColor){var i=Yt(n).map(function(t){return t/255}),o=(i[0]+i[1]+i[2])/3;e.ref.backgroundColor=i,e.ref.backgroundColorCenter=i.map(function(t){return o>.5?t-.15:t+.15}),e.ref.previousBackgroundColor=n}r&&r!==e.ref.previousOutlineColor&&(e.ref.outlineColor=Yt(r).map(function(t){return t/255}).concat(1),e.ref.previousOutlineColor=r)},write:k({SHOW_VIEW:function(t){var e=t.root;"crop"===t.action.id?(e.ref.dragger.enable(),e.element.setAttribute("tabindex","0")):(e.ref.dragger.disable(),e.element.removeAttribute("tabindex"))}},function(t){var e=t.root,n=t.props,r=(t.actions,t.timestamp);if(e.ref.gl&&n.width&&n.height){var i=e.ref,o=i.gl,a=i.previousWidth,l=i.previousHeight,c=i.shouldZoom,u=i.stageSpring,s=i.overlaySpring,f=i.backgroundColorSpring,d=i.backgroundColorCenterSpring;n.width===a&&n.height===l||(e.ref.gl.resize(n.width,n.height),e.ref.previousWidth=n.width,e.ref.previousHeight=n.height),n.left===e.ref.previousLeft&&n.top===e.ref.previousTop||(e.ref.canvas.style.transform="translate(".concat(-n.left,"px, ").concat(-n.top,"px)"),e.ref.previousLeft=n.left,e.ref.previousTop=n.top),c&&!e.ref.didZoom&&(e.introScale=null,e.introScale=1.15,e.introScale=1,e.ref.didZoom=!0),f.setTarget(e.ref.backgroundColor),f.interpolate(r);var h=f.isStable();d.setTarget(e.ref.backgroundColorCenter),d.interpolate(r);var p=d.isStable();e.ref.colorMatrixStableCount=0;var g=n.colorMatrix||Rr,m=e.ref.colorMatrixSpring.map(function(t,n){return t.target=g[n],t.interpolate(r),e.ref.colorMatrixPositions[n]}),v=20===e.ref.colorMatrixStableCount;n.isDraft&&s.setTarget(null),s.setTarget(n.overlay),s.interpolate(r);var y=s.isStable();n.isDraft&&u.setTarget(null),u.setTarget(n.stage),u.interpolate(r);var E=u.isStable();return o.update(e.xOrigin,e.yOrigin,e.xTranslation+n.left,e.yTranslation+n.top,e.xRotation,e.yRotation,e.zRotation,e.scale*e.introScale,m,e.ref.isPreview?1:e.colorOpacity,u.getRect(),s.getRect(),[1,1,1,1-e.overlayOpacity],f.getColor(),d.getColor(),f.getColor(),e.outlineWidth,e.ref.outlineColor,e.query("GET_BACKGROUND_COLOR")),y&&E&&v&&h&&p}})}),Cr=D({name:"image",ignoreRect:!0,mixins:{apis:["offsetTop"]},create:function(t){var e=t.root,n=t.props;e.ref.imageGL=e.appendChildView(e.createChildView(Ar)),/markup/.test(e.query("GET_UTILS"))&&(e.ref.markup=e.appendChildView(e.createChildView(Jn,{id:n.id,opacity:0,onSelect:function(t){e.dispatch("MARKUP_SELECT",{id:t})},onDrag:function(t,n,r,i,o){e.dispatch("MARKUP_ELEMENT_DRAG",{id:t,origin:n,offset:r,size:i,scale:o})},onResize:function(t,n,r,i,o){e.dispatch("MARKUP_ELEMENT_RESIZE",{id:t,corner:n,origin:r,offset:i,size:o})},onUpdate:function(t,n){e.dispatch("MARKUP_UPDATE",{style:t,value:n})},isMarkupUtil:function(t){var e=t;do{if("doka--markup-tools"===e.className)return!0}while(e=e.parentNode);return!1}}))),e.ref.isModal=/modal/.test(e.query("GET_STYLE_LAYOUT_MODE"))},write:k({DID_PRESENT_IMAGE:function(t){t.root.ref.imageGL.colorOpacity=1}},function(t){var e=t.root,n=t.props,r=t.timestamp,i=e.ref.imageGL,o=e.ref.markup,l=e.query("GET_CROP",n.id,r);if(l){var c=l.isDraft,u=l.cropRect,s=l.cropStatus,f=l.origin,d=l.translation,h=l.translationBand,p=l.scale,g=l.scaleBand,m=l.rotation,v=l.rotationBand,y=l.flip,E=l.colorMatrix,_=e.query("GET_ROOT"),w=e.query("GET_STAGE"),T=w.x,x=w.y;c&&(i.scale=null,i.zRotation=null,i.xTranslation=null,i.yTranslation=null,i.xOrigin=null,i.yOrigin=null),i.colorMatrix=E;var R=e.query("IS_ACTIVE_VIEW","crop"),A=e.query("IS_ACTIVE_VIEW","markup"),C=R?.75:.95,I=a({},u),O=1,M=R?1:5;if(e.query("IS_ACTIVE_VIEW","resize")){var S=s.image.width,b=s.image.height;O=null===S&&null===b?s.crop.width/u.width:null===S?b/u.height:S/u.width,O/=window.devicePixelRatio;var L=u.width*O,P=u.height*O;I.x=I.x+(.5*u.width-.5*L),I.y=I.y+(.5*u.height-.5*P),I.width=L,I.height=P}var G=e.ref.isModal?0:_.left,D=e.ref.isModal?0:_.top,k=e.ref.isModal?0:_.width-e.rect.element.width,U=e.ref.isModal?0:_.height-e.rect.element.height-n.offsetTop,V=(p+g)*O;i.isDraft=c,i.overlayOpacity=C,i.xOrigin=f.x,i.yOrigin=f.y,i.xTranslation=d.x+h.x+T,i.yTranslation=d.y+h.y+x,i.left=G,i.top=D+n.offsetTop,i.width=e.rect.element.width+k,i.height=e.rect.element.height+U+n.offsetTop,i.scale=V,i.xRotation=y.vertical?Math.PI:0,i.yRotation=y.horizontal?Math.PI:0,i.zRotation=m.main+m.sub+v,i.stage={x:w.x+G,y:w.y+D+n.offsetTop,width:w.width,height:w.height},i.overlay={x:I.x+T+G,y:I.y+x+D+n.offsetTop,width:I.width,height:I.height},i.outlineWidth=M,o&&(c&&(o.translateX=null,o.translateY=null,o.markupX=null,o.markupY=null,o.markupWidth=null,o.markupHeight=null),o.opacity=R?.3:1,o.markupX=I.x+T,o.markupY=I.y+x,o.markupWidth=I.width,o.markupHeight=I.height,o.allowInteraction=A)}})}),Ir=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"group",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:["opacity"],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return D({ignoreRect:!0,name:t,mixins:{styles:["opacity"].concat(c(e)),animations:a({opacity:{type:"spring",stiffness:.25,damping:.5,mass:5}},n)},create:function(t){var e=t.root,n=t.props;(n.controls||[]).map(function(t){var n=e.createChildView(t.view,t);t.didCreateView&&t.didCreateView(n),e.appendChildView(n)}),n.element&&e.element.appendChild(n.element)}})},Or=D({ignoreRect:!0,tag:"div",name:"dropdown-list",mixins:{styles:["translateY","opacity"],apis:["selectedValue","options","onSelect"],animations:{translateY:"spring",opacity:{type:"tween",duration:250}}},create:function(t){var e=t.root,n=t.props;e.element.setAttribute("role","list"),e.ref.handleClick=function(){return n.action&&n.action()},e.element.addEventListener("click",e.ref.handleClick),e.ref.activeOptions=null,e.ref.activeSelectedValue},write:function(t){var e=t.root,n=t.props;if(n.options!==e.ref.activeOptions&&(e.ref.activeOptions=n.options,e.childViews.forEach(function(t){return e.removeChildView(t)}),n.options.map(function(t){var r=e.createChildView(Sn,a({},t,{action:function(){return n.onSelect(t.value)}}));return e.appendChildView(r)})),n.selectedValue!==e.ref.activeSelectedValue){e.ref.activeSelectedValue=n.selectedValue;var r=n.options.findIndex(function(t){return"object"===i(t.value)&&n.selectedValue?JSON.stringify(t.value)===JSON.stringify(n.selectedValue):t.value===n.selectedValue});e.childViews.forEach(function(t,e){t.element.setAttribute("aria-selected",e===r)})}},destroy:function(t){var e=t.root;e.element.removeEventListener("click",e.ref.handleClick)}}),Mr=D({ignoreRect:!0,tag:"div",name:"dropdown",mixins:{styles:["opacity"],animations:{opacity:"spring"},apis:["direction","selectedValue","options","onSelect"]},create:function(t){var e=t.root,n=t.props;e.ref.open=!1;var r=function(t){e.ref.open=t,e.dispatch("KICK")};e.ref.button=e.appendChildView(e.createChildView(Sn,a({},n,{action:function(){r(!e.ref.open)}}))),e.ref.list=e.appendChildView(e.createChildView(Or,a({},n,{opacity:0,action:function(){r(!1)}}))),e.ref.handleBodyClick=function(t){e.element.contains(t.target)||r(!1)},e.element.addEventListener("focusin",function(t){t.target!==e.ref.button.element&&r(!0)}),e.element.addEventListener("focusout",function(t){e.element.contains(t.relatedTarget)||r(!1)}),document.body.addEventListener("click",e.ref.handleBodyClick)},destroy:function(t){var e=t.root;document.body.removeEventListener("click",e.ref.handleBodyClick)},write:function(t){var e=t.root,n=t.props;if(e.ref.list.opacity=e.ref.open?1:0,e.ref.list.selectedValue=n.selectedValue,e.ref.list.options=n.options,"up"===n.direction){var r=e.ref.list.rect.element.height;e.ref.list.translateY=(e.ref.open?-(r+5):-r)-e.rect.element.height}else e.ref.list.translateY=e.ref.open?0:-5}}),Sr=D({name:"crop-rotator-line",ignoreRect:!0,ignoreRectUpdate:!0,mixins:{styles:["translateX"],animations:{translateX:"spring"}},create:function(t){for(var e=t.root,n='<svg viewBox="-90 -5 180 10" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false">',r=0;r<=180;r+=2){var i=r*(176/180)-90+2,o=r%10==0?.5:.2;n+='<circle fill="currentColor" cx="'.concat(i,'" cy="').concat(0,'" r="').concat(o,'"/>'),r%10==0&&(n+='<text fill="currentColor" x="'.concat(i+(i<0?-2.25:0===i?-.75:-1.5),'" y="').concat(3.5,'">').concat(-90+r,"&deg;</text>"))}n+="</svg>",e.element.innerHTML=n}}),br=D({name:"crop-rotator",ignoreRect:!0,mixins:{styles:["opacity","translateY"],animations:{opacity:{type:"spring",damping:.5,mass:5},translateY:"spring"},apis:["rotation","animate","setAllowInteraction"]},create:function(t){var e=t.root,n=t.props;e.element.setAttribute("tabindex",0);var r=document.createElement("button");r.innerHTML="<span>".concat(e.query("GET_LABEL_BUTTON_CROP_ROTATE_CENTER"),"</span>"),r.className="doka--crop-rotator-center",r.addEventListener("click",function(){e.dispatch("CROP_IMAGE_ROTATE_CENTER")}),e.appendChild(r);var i=null;e.appendChildView(e.createChildView(function(t,e){return D({name:t,ignoreRect:!0,create:e})}("crop-rotator-line-mask",function(t){var e=t.root,n=t.props;i=e.appendChildView(e.createChildView(Sr,{translateX:Math.round(312*n.rotation)}))}),n)),e.ref.line=i;var o=document.createElement("div");o.className="doka--crop-rotator-bar",e.appendChild(o);var a=Math.PI/4,l=0;e.ref.dragger=qn(o,function(){l=i.translateX/312,e.dispatch("CROP_IMAGE_ROTATE_GRAB")},function(t,n){var r=n.x/e.rect.element.width*(Math.PI/2),i=ht(l+r,-a,a);e.dispatch("CROP_IMAGE_ROTATE",{value:-i})},function(){e.dispatch("CROP_IMAGE_ROTATE_RELEASE")},{stopPropagation:!0}),n.setAllowInteraction=function(t){t?e.ref.dragger.enable():e.ref.dragger.disable()},e.ref.keyboard=ir(e.element,function(){l=0},{left:function(){l+=Math.PI/128,e.dispatch("CROP_IMAGE_ROTATE_ADJUST",{value:l})},right:function(){l-=Math.PI/128,e.dispatch("CROP_IMAGE_ROTATE_ADJUST",{value:l})}},function(){},function(){}),e.ref.prevRotation},destroy:function(t){var e=t.root;e.ref.dragger.destroy(),e.ref.keyboard.destroy()},write:function(t){var e=t.root,n=t.props,r=t.timestamp,i=n.animate,o=n.rotation;if(e.ref.prevRotation!==o){e.ref.prevRotation=o,i||0===o||(e.ref.line.translateX=null);var a=0,l=e.query("GET_CROP",n.id,r);if(l&&l.interaction&&l.interaction.rotation){var c=Je(l.interaction.rotation).sub-o;a=.025*Math.sign(c)*Math.log10(1+Math.abs(c)/.025)}e.ref.line.translateX=Math.round(312*(-o-a))}}}),Lr=["nw","ne","se","sw"],Pr=["n","e","s","w"],Gr=P()&&1===window.devicePixelRatio?function(t){return Math.round(t)}:function(t){return t},Dr=D({ignoreRect:!0,ignoreRectUpdate:!0,name:"crop-rect-focal-line",mixins:{styles:["translateX","translateY","scaleX","scaleY","opacity"],animations:{translateX:"spring",translateY:"spring",scaleX:"spring",scaleY:"spring",opacity:"spring"}}}),kr=function(t){return D({ignoreRect:!0,ignoreRectUpdate:!0,tag:"div",name:"crop-rect-edge-".concat(t),mixins:{styles:["translateX","translateY","scaleX","scaleY"],apis:["setAllowInteraction"]},create:function(e){var n=e.root,r=e.props;n.element.classList.add("doka--crop-rect-edge"),n.element.setAttribute("tabindex",0),n.element.setAttribute("role","button");var i=t,o=function(t){return Pr[(Pr.indexOf(t)+2)%Pr.length]}(t);n.ref.dragger=qn(n.element,function(){n.dispatch("CROP_RECT_DRAG_GRAB")},function(t,e){return n.dispatch("CROP_RECT_EDGE_DRAG",{offset:e,origin:i,anchor:o})},function(){return n.dispatch("CROP_RECT_DRAG_RELEASE")},{stopPropagation:!0,cancelOnMultiple:!0}),r.setAllowInteraction=function(t){t?n.ref.dragger.enable():n.ref.dragger.disable()},n.ref.keyboard=ir(n.element,function(){return{x:0,y:0}},{up:function(t){t.y-=20},down:function(t){t.y+=20},left:function(t){t.x-=20},right:function(t){t.x+=20}},function(t){n.dispatch("CROP_RECT_DRAG_GRAB"),n.dispatch("CROP_RECT_EDGE_DRAG",{offset:t,origin:i,anchor:o})},function(){n.dispatch("CROP_RECT_DRAG_RELEASE")})},destroy:function(t){var e=t.root;e.ref.keyboard.destroy(),e.ref.dragger.destroy()}})},Ur=function(t,e,n){return D({ignoreRect:!0,ignoreRectUpdate:!0,tag:"div",name:"crop-rect-corner-".concat(t),mixins:{styles:["translateX","translateY","scaleX","scaleY"],animations:{translateX:Hn,translateY:Hn,scaleX:{type:"spring",delay:n},scaleY:{type:"spring",delay:n},opacity:{type:"spring",delay:e}},apis:["setAllowInteraction"]},create:function(e){var n=e.root,r=e.props;n.element.classList.add("doka--crop-rect-corner"),n.element.setAttribute("role","button"),n.element.setAttribute("tabindex",-1);var i=t,o=function(t){return Lr[(Lr.indexOf(t)+2)%Lr.length]}(t);n.ref.dragger=qn(n.element,function(){n.dispatch("CROP_RECT_DRAG_GRAB")},function(t,e){n.dispatch("CROP_RECT_CORNER_DRAG",{offset:e,origin:i,anchor:o})},function(){n.dispatch("CROP_RECT_DRAG_RELEASE")},{stopPropagation:!0,cancelOnMultiple:!0}),r.setAllowInteraction=function(t){t?n.ref.dragger.enable():n.ref.dragger.disable()}},destroy:function(t){t.root.ref.dragger.destroy()}})},Vr=D({ignoreRect:!0,ignoreRectUpdate:!0,name:"crop-rect",mixins:{apis:["rectangle","draft","rotating","enabled"]},create:function(t){var e=t.root;e.ref.wasRotating=!1,Lr.forEach(function(t,n){var r=10*n,i=250+r+50,o=250+r;e.ref[t]=e.appendChildView(e.createChildView(Ur(t,i,o),{opacity:0,scaleX:.5,scaleY:.5}))}),Pr.forEach(function(t){e.ref[t]=e.appendChildView(e.createChildView(kr(t)))}),e.ref.lines=[];for(var n=0;n<10;n++)e.ref.lines.push(e.appendChildView(e.createChildView(Dr,{opacity:0})));e.ref.animationDir=null,e.ref.previousRotating,e.ref.previousRect={},e.ref.previousEnabled,e.ref.previousDraft},write:function(t){var e=t.root,n=t.props,r=n.rectangle,i=n.draft,o=n.rotating,a=n.enabled;if(r&&(!Tt(r,e.ref.previousRect)||o!==e.ref.previousRotating||a!==e.ref.previousEnabled||i!==e.ref.previousDraft)){e.ref.previousRect=r,e.ref.previousRotating=o,e.ref.previousEnabled=a,e.ref.previousDraft=i;var l=e.ref,c=l.n,u=l.e,s=l.s,f=l.w,d=l.nw,h=l.ne,p=l.se,g=l.sw,m=l.lines,v=l.animationDir,y=r.x,E=r.y,_=r.x+r.width,w=r.y+r.height,T=w-E,x=_-y,R=Math.min(x,T);e.element.dataset.indicatorSize=R<80?"none":"default",Pr.forEach(function(t){return e.ref[t].setAllowInteraction(a)}),Lr.forEach(function(t){return e.ref[t].setAllowInteraction(a)});var A=e.query("IS_ACTIVE_VIEW","crop");if(A&&"in"!==v?(e.ref.animationDir="in",Lr.map(function(t){return e.ref[t]}).forEach(function(t){t.opacity=1,t.scaleX=1,t.scaleY=1})):A||"out"===v||(e.ref.animationDir="out",Lr.map(function(t){return e.ref[t]}).forEach(function(t){t.opacity=0,t.scaleX=.5,t.scaleY=.5})),Nr(i,d,y,E),Nr(i,h,_,E),Nr(i,p,_,w),Nr(i,g,y,w),Br(i,c,y,E,x/100,1),Br(i,u,_,E,1,T/100),Br(i,s,y,w,x/100,1),Br(i,f,y,E,1,T/100),o){e.ref.wasRotating=!0;var C=m.slice(0,5),I=1/C.length;C.forEach(function(t,e){Br(i,t,y,E+T*(I+e*I),x/100,.01),t.opacity=.5});var O=m.slice(5);I=1/O.length,O.forEach(function(t,e){Br(i,t,y+x*(I+e*I),E,.01,T/100),t.opacity=.5})}else if(i){e.ref.wasRotating=!1;var M=m[0],S=m[1],b=m[2],L=m[3];Br(i,M,y,E+.333*T,x/100,.01),Br(i,S,y,E+.666*T,x/100,.01),Br(i,b,y+.333*x,E,.01,T/100),Br(i,L,y+.666*x,E,.01,T/100),M.opacity=.5,S.opacity=.5,b.opacity=.5,L.opacity=.5}else{var P=m[0],G=m[1],D=m[2],k=m[3];!e.ref.wasRotating&&P.opacity>0&&(Br(i,P,y,E+.333*T,x/100,.01),Br(i,G,y,E+.666*T,x/100,.01),Br(i,D,y+.333*x,E,.01,T/100),Br(i,k,y+.666*x,E,.01,T/100)),m.forEach(function(t){return t.opacity=0})}}}}),Br=function(t,e,n,r,i,o){t&&(e.translateX=null,e.translateY=null,e.scaleX=null,e.scaleY=null),e.translateX=Gr(n),e.translateY=Gr(r),e.scaleX=i,e.scaleY=o},Nr=function(t,e,n,r){t&&(e.translateX=null,e.translateY=null),e.translateX=Gr(n),e.translateY=Gr(r)},zr=function(t,e){if(!/svg/.test(t.namespaceURI)||"innerHTML"in t)t.innerHTML=e;else{var n=document.createElement("div");n.innerHTML="<svg>"+e+"</svg>";for(var r=n.firstChild;r.firstChild;)t.appendChild(r.firstChild)}},Fr=D({ignoreRect:!0,ignoreRectUpdate:!0,name:"crop-mask",tag:"svg",mixins:{styles:["opacity","translateX","translateY"],animations:{scale:Hn,maskWidth:Hn,maskHeight:Hn,translateX:Hn,translateY:Hn,opacity:{type:"tween",delay:0,duration:1e3}},apis:["rectangle","animate","maskWidth","maskHeight","scale"]},create:function(t){var e=t.root;e.customWriter=e.query("GET_CROP_MASK")(e.element,zr)||function(){}},didWriteView:function(t){var e=t.root,n=t.props,r=n.maskWidth,i=n.maskHeight,o=n.scale;if(r&&i){e.element.setAttribute("width",Gr(r)),e.element.setAttribute("height",Gr(i));var a=e.query("GET_CROP_MASK_INSET");e.customWriter({x:o*a,y:o*a,width:r-o*a*2,height:i-o*a*2},{width:r,height:i})}}}),Wr=function(t,e){var n=t.childNodes[0];n?e!==n.nodeValue&&(n.nodeValue=e):(n=document.createTextNode(e),t.appendChild(n))},qr={type:"spring",stiffness:.25,damping:.1,mass:1},Hr=D({ignoreRect:!0,name:"crop-size",mixins:{styles:["translateX","translateY","opacity"],animations:{translateX:"spring",translateY:"spring",opacity:"spring",sizeWidth:qr,sizeHeight:qr},apis:["sizeWidth","sizeHeight"],listeners:!0},create:function(t){var e=t.root,n=v("span");n.className="doka--crop-size-info doka--crop-resize-percentage",e.ref.resizePercentage=n,e.appendChild(n);var r=v("span");r.className="doka--crop-size-info";var i=v("span");i.className="doka--crop-size-multiply",i.textContent="×";var o=v("span"),a=v("span");e.ref.outputWidth=o,e.ref.outputHeight=a,r.appendChild(o),r.appendChild(i),r.appendChild(a),e.appendChild(r),e.ref.previousValues={width:0,height:0,percentage:0}},write:function(t){var e=t.root,n=t.props,r=t.timestamp;if(!(e.opacity<=0)){var i=e.query("GET_CROP",n.id,r);if(i){var o=i.cropStatus,a=i.isDraft,l=e.ref,c=l.outputWidth,u=l.outputHeight,s=l.resizePercentage,f=l.previousValues,d=o.image,h=o.crop,p=o.currentWidth,g=o.currentHeight,m=d.width?Math.round(d.width/h.width*100):0;a&&(e.sizeWidth=null,e.sizeHeight=null),e.sizeWidth=p,e.sizeHeight=g;var v=Math.round(e.sizeWidth),y=Math.round(e.sizeHeight);v!==f.width&&(Wr(c,v),f.width=v),y!==f.height&&(Wr(u,y),f.height=y),m!==f.percentage&&(d.width?Wr(s,"".concat(m,"%")):Wr(s,""),f.percentage=m)}}}}),Yr=function(t){return JSON.parse(localStorage.getItem(t)||"{}")},Zr=function(){return window.matchMedia("(pointer: fine) and (hover: hover)").matches},Xr=D({ignoreRect:!0,ignoreRectUpdate:!0,name:"instructions-bubble",mixins:{styles:["opacity","translateX","translateY"],animations:{opacity:{type:"tween",duration:400}},apis:["seen"]},create:function(t){var e=t.root,n=t.props;return e.element.innerHTML=(n.iconBefore||"")+n.text},write:function(t){var e=t.root;t.props.seen&&(e.opacity=0)}}),Kr={type:"spring",stiffness:.4,damping:.65,mass:7},jr=D({name:"crop-subject",ignoreRect:!0,mixins:{styles:["opacity","translateX","translateY"],animations:{opacity:{type:"tween",duration:250},translateX:Kr,translateY:Kr}},create:function(t){var e=t.root,n=t.props;e.opacity=1,e.ref.timestampOffset=null,e.query("GET_CROP_ALLOW_INSTRUCTION_ZOOM")&&Zr()&&(function(t,e,n){var r=Yr(t);return void 0===r[e]?n:r[e]}(e.query("GET_STORAGE_NAME"),"instruction_zoom_shown",!1)||(e.ref.instructions=e.appendChildView(e.createChildView(Xr,{opacity:0,seen:!1,text:e.query("GET_LABEL_CROP_INSTRUCTION_ZOOM"),iconBefore:Mn('<rect stroke-width="1.5" fill="none" stroke="currentColor" x="5" y="1" width="14" height="22" rx="7" ry="7"></rect><circle fill="currentColor" stroke="none" cx="12" cy="8" r="2"></circle>')})))),e.query("GET_CROP_MASK")&&(e.ref.maskView=e.appendChildView(e.createChildView(Fr))),e.ref.cropView=e.appendChildView(e.createChildView(Vr)),e.query("GET_CROP_SHOW_SIZE")&&(e.ref.cropSize=e.appendChildView(e.createChildView(Hr,{id:n.id,opacity:1,scaleX:1,scaleY:1,translateX:null}))),e.query("GET_CROP_ZOOM_TIMEOUT")||(e.ref.btnZoom=e.appendChildView(e.createChildView(function(t,e){return D({ignoreRect:!0,name:t,mixins:e,create:function(t){var e=t.root,n=t.props;n.className&&e.element.classList.add(n.className),n.controls.map(function(t){var n=e.createChildView(t.view,t);t.didCreateView&&t.didCreateView(n),e.appendChildView(n)})}})}("zoom-wrapper",{styles:["opacity","translateX","translateY"],animations:{opacity:{type:"tween",duration:250}}}),{opacity:0,controls:[{view:Sn,label:e.query("GET_LABEL_BUTTON_CROP_ZOOM"),name:"zoom",icon:Mn('<g fill="currentColor" fill-rule="nonzero"><path d="M12.5 19a6.5 6.5 0 1 1 0-13 6.5 6.5 0 0 1 0 13zm0-2a4.5 4.5 0 1 0 0-9 4.5 4.5 0 0 0 0 9z"/><path d="M15.765 17.18a1 1 0 1 1 1.415-1.415l3.527 3.528a1 1 0 0 1-1.414 1.414l-3.528-3.527z"/></g>',26),action:function(){return e.dispatch("CROP_ZOOM")}}]})))},write:k({CROP_IMAGE_RESIZE_MULTIPLY:function(t){var e=t.root,n=e.ref.instructions;n&&!n.seen&&(n.seen=!0,function(t,e,n){var r=Yr(t);r[e]=n,localStorage.setItem(t,JSON.stringify(r))}(e.query("GET_STORAGE_NAME"),"instruction_zoom_shown",!0))},CROP_RECT_DRAG_RELEASE:function(t){var e=t.root,n=t.props,r=t.timestamp,i=e.ref.btnZoom;if(i){var o=e.query("GET_CROP",n.id,r).cropRect,a=o.x+.5*o.width,l=o.y+.5*o.height;i.translateX=a,i.translateY=l}}},function(t){var e=t.root,n=t.props,r=t.timestamp,i=e.ref,o=i.cropView,a=i.maskView,l=i.btnZoom,c=i.cropSize,u=i.instructions;if(!e.query("IS_ACTIVE_VIEW","crop"))return o.enabled=!1,e.ref.timestampOffset=null,void(c&&(c.opacity=0));e.ref.timestampOffset||(e.ref.timestampOffset=r);var s=e.query("GET_CROP",n.id,r);if(s){var f=s.cropRect,d=s.isRotating,h=s.isDraft,p=s.scale,g=e.query("GET_STAGE");if(e.translateX=g.x-e.rect.element.left,e.translateY=g.y-e.rect.element.top,o.draft=h,o.rotating=d,o.rectangle=f,o.enabled=!0,c){c.opacity=1,h&&(c.translateX=null,c.translateY=null);var m=Qr(e.rect.element,c.rect.element,f);c.translateX=h?m.x:Gr(m.x),c.translateY=h?m.y:Gr(m.y)}if(a&&(h&&(a.translateX=null,a.translateY=null,a.maskWidth=null,a.maskHeight=null),a.translateX=Gr(f.x),a.translateY=Gr(f.y),a.maskWidth=f.width,a.maskHeight=f.height,a.scale=p),s.canRecenter)u&&(u.opacity=0),l&&(l.opacity=s.isDraft?0:1);else if(l&&(l.opacity=0),u&&!u.seen&&!s.isDraft){var v=f.x+.5*f.width,y=f.y+.5*f.height;u.translateX=Math.round(v-.5*u.rect.element.width),u.translateY=Math.round(y-.5*u.rect.element.height),r-e.ref.timestampOffset>2e3&&(u.opacity=1)}}})}),Qr=function(t,e,n){var r=n.x,i=n.x+n.width,o=n.y+n.height,a=i-e.width-16,l=o-e.height-16;return e.width>n.width-32&&(a=r+(.5*n.width-.5*e.width),(l=o+16)>t.height-e.height&&(l=o-e.height-16)),{x:a=Math.max(0,Math.min(a,t.width-e.width)),y:l}},Jr=function(){return performance.now()},$r=function(t,e){for(;1===t.nodeType&&!e(t);)t=t.parentNode;return 1===t.nodeType?t:null},ti=function(t,e){var n=$r(e,function(t){return t.classList.contains("doka--root")});return!!n&&n.contains(t)},ei=function(t){var e=t.root,n=t.props,r=t.action.position,i=n.pivotPoint,o=e.ref,a=o.indicatorA,l=o.indicatorB,c=i.x-r.x,u=i.y-r.y,s={x:i.x+c,y:i.y+u},f={x:i.x-c,y:i.y-u};a.style.cssText="transform: translate3d(".concat(s.x,"px, ").concat(s.y,"px, 0)"),l.style.cssText="transform: translate3d(".concat(f.x,"px, ").concat(f.y,"px, 0)")},ni=function(t){return{x:t.pageX,y:t.pageY}},ri=D({ignoreRect:!0,ignoreRectUpdate:!0,name:"crop-resizer",mixins:{apis:["pivotPoint","scrollRect"]},create:function(t){var e=t.root,n=t.props;e.ref.isActive=!1,e.ref.isCropping=!1,e.ref.indicatorA=document.createElement("div"),e.appendChild(e.ref.indicatorA),e.ref.indicatorB=document.createElement("div"),e.appendChild(e.ref.indicatorB);var r=e.query("GET_CROP_RESIZE_KEY_CODES");e.ref.hasEnabledResizeModifier=r.length>0;var i={origin:{x:null,y:null},position:{x:null,y:null},selecting:!1,enabled:!1,scrollY:0,offsetX:0,offsetY:0},o=Jr();e.ref.state=i;var l=Nn(),c=0,u=!1;e.ref.resizeStart=function(t){if(e.ref.isActive&&(0===l.count()&&(u=!1),l.push(t),zn(document.documentElement,"up",e.ref.resizeEnd),ti(e.element,t.target)&&l.multiple())){t.stopPropagation(),t.preventDefault();var n=l.active(),r=ni(n[0]),i=ni(n[1]);c=yt(r,i),zn(document.documentElement,"move",e.ref.resizeMove),u=!0}},e.ref.resizeMove=function(t){if(e.ref.isActive&&u&&(t.preventDefault(),2===l.count())){l.update(t);var n=l.active(),r=ni(n[0]),i=ni(n[1]),o=(yt(r,i)-c)/c;e.dispatch("CROP_IMAGE_RESIZE",{value:o})}},e.ref.resizeEnd=function(t){if(e.ref.isActive){l.pop(t);var n=0===l.count();n&&(Fn(document.documentElement,"move",e.ref.resizeMove),Fn(document.documentElement,"up",e.ref.resizeEnd)),u&&(t.preventDefault(),n&&e.dispatch("CROP_IMAGE_RESIZE_RELEASE"))}},zn(document.documentElement,"down",e.ref.resizeStart);var s=performance.now(),f=0,d=1,h=function(t,e){var n=null,r=null;return function(){var i=arguments;if(!r)return t.apply(null,Array.from(arguments)),void(r=Jr());clearTimeout(n),n=setTimeout(function(){Jr()-r>=e&&(t.apply(null,Array.from(i)),r=Jr())},e-(Jr()-r))}}(function(t){if(!e.ref.isCropping){var n=Math.sign(t.wheelDelta||t.deltaY),r=Jr(),i=r-s;s=r,(i>750||f!==n)&&(d=1,f=n),d+=.05*n,e.dispatch("CROP_IMAGE_RESIZE_MULTIPLY",{value:Math.max(.1,d)}),e.dispatch("CROP_IMAGE_RESIZE_RELEASE")}},100);e.ref.wheel=function(t){if(e.ref.isActive&&ti(e.element,t.target)){if(n.scrollRect){var r=n.scrollRect,i=e.query("GET_ROOT"),o=ni(t),a={x:o.x-i.leftScroll,y:o.y-i.topScroll};if(a.x<r.x||a.x>r.x+r.width||a.y<r.y||a.y>r.y+r.height)return}t.preventDefault(),h(t)}},document.addEventListener("wheel",e.ref.wheel,{passive:!1}),e.ref.hasEnabledResizeModifier&&(e.ref.move=function(t){if(e.ref.isActive&&!e.ref.isCropping&&(i.position.x=t.pageX-e.ref.state.offsetX,i.position.y=t.pageY-e.ref.state.scrollY-e.ref.state.offsetY,i.enabled))if(ti(e.element,t.target)){"idle"===e.element.dataset.state&&e.dispatch("RESIZER_SHOW",{position:a({},i.position)}),t.preventDefault(),e.dispatch("RESIZER_MOVE",{position:a({},i.position)});var r=n.pivotPoint,l=r.x-i.position.x,u=r.y-i.position.y,s={x:r.x+l,y:r.y+u},f=a({},i.position);if(i.selecting){var d=(yt(s,f)-c)/c,h=performance.now();h-o>25&&(o=h,e.dispatch("CROP_IMAGE_RESIZE",{value:d}))}}else e.dispatch("RESIZER_CANCEL")},e.ref.select=function(t){if(e.ref.isActive&&ti(e.element,t.target)){var r=n.pivotPoint,o=r.x-i.position.x,a=r.y-i.position.y,l={x:r.x+o,y:r.y+a},u=i.position;c=yt(l,u),i.selecting=!0,i.origin.x=t.pageX,i.origin.y=t.pageY,e.dispatch("CROP_IMAGE_RESIZE_GRAB")}},e.ref.confirm=function(t){e.ref.isActive&&ti(e.element,t.target)&&(i.selecting=!1,e.dispatch("CROP_IMAGE_RESIZE_RELEASE"))},e.ref.blur=function(){e.ref.isActive&&(i.selecting=!1,i.enabled=!1,document.removeEventListener("mousedown",e.ref.select),document.removeEventListener("mouseup",e.ref.confirm),e.dispatch("RESIZER_CANCEL"))},window.addEventListener("blur",e.ref.blur),document.addEventListener("mousemove",e.ref.move),e.ref.keyDown=function(t){e.ref.isActive&&r.includes(t.keyCode)&&i.position&&(i.enabled=!0,document.addEventListener("mousedown",e.ref.select),document.addEventListener("mouseup",e.ref.confirm),e.dispatch("RESIZER_SHOW",{position:a({},i.position)}))},e.ref.keyUp=function(t){e.ref.isActive&&r.includes(t.keyCode)&&(i.enabled=!1,document.removeEventListener("mousedown",e.ref.select),document.removeEventListener("mouseup",e.ref.confirm),e.dispatch("RESIZER_CANCEL"))},document.body.addEventListener("keydown",e.ref.keyDown),document.body.addEventListener("keyup",e.ref.keyUp))},destroy:function(t){var e=t.root;document.removeEventListener("touchmove",e.ref.resizeMove),document.removeEventListener("touchend",e.ref.resizeEnd),document.removeEventListener("touchstart",e.ref.resizeStart),document.removeEventListener("wheel",e.ref.wheel),document.removeEventListener("mousedown",e.ref.select),document.removeEventListener("mouseup",e.ref.confirm),e.ref.hasEnabledResizeModifier&&(document.removeEventListener("mousemove",e.ref.move),document.body.removeEventListener("keydown",e.ref.keyDown),document.body.removeEventListener("keyup",e.ref.keyUp),window.removeEventListener("blur",e.ref.blur))},read:function(t){var e=t.root;e.ref.state.scrollY=window.scrollY;var n=e.element.getBoundingClientRect();e.ref.state.offsetX=n.x,e.ref.state.offsetY=n.y},write:k({CROP_RECT_DRAG_GRAB:function(t){t.root.ref.isCropping=!0},CROP_RECT_DRAG_RELEASE:function(t){t.root.ref.isCropping=!1},SHOW_VIEW:function(t){var e=t.root,n=t.action;e.ref.isActive="crop"===n.id},RESIZER_SHOW:function(t){var e=t.root,n=t.props,r=t.action;e.element.dataset.state="multi-touch",ei({root:e,props:n,action:r})},RESIZER_CANCEL:function(t){t.root.element.dataset.state="idle"},RESIZER_MOVE:ei})}),ii=function(t,e){return t.style.opacity=e},oi=function(t,e){var n=Array.from(t.element.querySelectorAll(".doka--icon-crop-limit rect"));n.length&&(ii(n[0],e?.3:0),ii(n[1],e?1:0),ii(n[2],e?0:.3),ii(n[3],e?0:1))},ai=function(t,e){var n=t.element.querySelectorAll(".doka--icon-aspect-ratio rect");if(n.length){if(!e)return ii(n[0],.2),ii(n[1],.3),void ii(n[2],.4);ii(n[0],e>1?1:.3),ii(n[1],1===e?.85:.5),ii(n[2],e<1?1:.3)}},li=D({name:"crop",ignoreRect:!0,mixins:{apis:["viewId","stagePosition","hidden","offsetTop"]},create:function(t){var e=t.root,n=t.props;n.viewId="crop",n.hidden=!1,e.ref.isHiding=!1;var r=[];e.query("GET_CROP_ALLOW_IMAGE_TURN_LEFT")&&r.push({view:Sn,name:"tool",label:e.query("GET_LABEL_BUTTON_CROP_ROTATE_LEFT"),icon:Mn('<g transform="translate(3 2)" fill="currentColor" fill-rule="evenodd" class="doka--icon-turn"><rect y="9" width="12" height="12" rx="1"/><path d="M9.823 5H11a5 5 0 0 1 5 5 1 1 0 0 0 2 0 7 7 0 0 0-7-7H9.626l.747-.747A1 1 0 0 0 8.958.84L6.603 3.194a1 1 0 0 0 0 1.415l2.355 2.355a1 1 0 0 0 1.415-1.414L9.823 5z" fill-rule="nonzero" /></g>',26),action:function(){return e.dispatch("CROP_IMAGE_ROTATE_LEFT")}}),e.query("GET_CROP_ALLOW_IMAGE_TURN_RIGHT")&&r.push({view:Sn,name:"tool",label:e.query("GET_LABEL_BUTTON_CROP_ROTATE_RIGHT"),icon:Mn('<g transform="translate(5 2)" fill="currentColor" fill-rule="evenodd" class="doka--icon-turn"><path d="M8.177 5H7a5 5 0 0 0-5 5 1 1 0 0 1-2 0 7 7 0 0 1 7-7h1.374l-.747-.747A1 1 0 0 1 9.042.84l2.355 2.355a1 1 0 0 1 0 1.415L9.042 6.964A1 1 0 0 1 7.627 5.55l.55-.55z" fill-rule="nonzero"/><rect x="6" y="9" width="12" height="12" rx="1"/></g>',26),action:function(){return e.dispatch("CROP_IMAGE_ROTATE_RIGHT")}}),e.query("GET_CROP_ALLOW_IMAGE_FLIP_HORIZONTAL")&&r.push({view:Sn,name:"tool",label:e.query("GET_LABEL_BUTTON_CROP_FLIP_HORIZONTAL"),icon:Mn('<g fill="currentColor" fill-rule="evenodd"><path d="M11.93 7.007V20a1 1 0 0 1-1 1H5.78a1 1 0 0 1-.93-1.368l5.15-12.993a1 1 0 0 1 1.929.368z"/><path d="M14 7.007V20a1 1 0 0 0 1 1h5.149a1 1 0 0 0 .93-1.368l-5.15-12.993A1 1 0 0 0 14 7.007z" opacity=".6"/></g>',26),action:function(){return e.dispatch("CROP_IMAGE_FLIP_HORIZONTAL")}}),e.query("GET_CROP_ALLOW_IMAGE_FLIP_HORIZONTAL")&&r.push({view:Sn,name:"tool",label:e.query("GET_LABEL_BUTTON_CROP_FLIP_VERTICAL"),icon:Mn('<g fill="currentColor" fill-rule="evenodd"><path d="M19.993 12.143H7a1 1 0 0 1-1-1V5.994a1 1 0 0 1 1.368-.93l12.993 5.15a1 1 0 0 1-.368 1.93z"/><path d="M19.993 14a1 1 0 0 1 .368 1.93L7.368 21.078A1 1 0 0 1 6 20.148V15a1 1 0 0 1 1-1h12.993z" opacity=".6"/></g>',26),action:function(){return e.dispatch("CROP_IMAGE_FLIP_VERTICAL")}});var i=e.query("GET_CROP_ASPECT_RATIO_OPTIONS");i&&i.length&&r.push({view:Mr,name:"tool",label:e.query("GET_LABEL_BUTTON_CROP_ASPECT_RATIO"),icon:Mn('<g class="doka--icon-aspect-ratio" fill="currentColor" fill-rule="evenodd"><rect x="2" y="4" opacity=".3" width="10" height="18" rx="1"/><rect opacity=".5" x="4" y="8" width="14" height="14" rx="1"/><rect x="6" y="12" width="17" height="10" rx="1"/></g>',26),options:null,onSelect:function(t){t.width&&t.height?e.dispatch("RESIZE_SET_OUTPUT_SIZE",{width:t.width,height:t.height}):(e.query("GET_CROP_ASPECT_RATIO_OPTIONS").find(function(t){return t.value&&t.value.width||t.value.height})&&e.dispatch("RESIZE_SET_OUTPUT_SIZE",{width:null,height:null}),e.dispatch("CROP_SET_ASPECT_RATIO",{value:t.aspectRatio}))},didCreateView:function(t){e.ref.aspectRatioDropdown=t}}),e.query("GET_CROP_ALLOW_TOGGLE_LIMIT")&&r.push({view:Mr,name:"tool",label:e.query("GET_LABEL_BUTTON_CROP_TOGGLE_LIMIT"),icon:Mn('<g class="doka--icon-crop-limit" fill="currentColor" fill-rule="evenodd">\n                    <rect x="2" y="3" width="20" height="20" rx="1"/>\n                    <rect x="7" y="8" width="10" height="10" rx="1"/>\n                    <rect x="4" y="8" width="14" height="14" rx="1"/>\n                    <rect x="12" y="4" width="10" height="10" rx="1"/>\n                </g>',26),options:[{value:!0,label:e.query("GET_LABEL_BUTTON_CROP_TOGGLE_LIMIT_ENABLE"),icon:'<svg width="23" height="23" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false"><g fill="currentColor"><rect x="3" y="3" width="17" height="17" rx="2.5" opacity=".3"/><rect x="7" y="7" width="9" height="9" rx="2.5"/></g></svg>'},{value:!1,label:e.query("GET_LABEL_BUTTON_CROP_TOGGLE_LIMIT_DISABLE"),icon:'<svg width="23" height="23" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false"><g fill="currentColor"><rect x="3" y="6" width="13" height="13" rx="2.5" opacity=".3"/><rect x="10" y="3" width="9" height="9" rx="2.5"/></g></svg>'}],onSelect:function(t){e.dispatch("CROP_SET_LIMIT",{value:t})},didCreateView:function(t){e.ref.cropToggleLimitDropdown=t}}),e.ref.menu=e.appendChildView(e.createChildView(Ir("toolbar",["opacity"],{opacity:{type:"spring",mass:15,delay:50}}),{opacity:0,controls:r})),e.ref.menuItemsRequiredWidth=null,e.ref.subject=e.appendChildView(e.createChildView(jr,a({},n))),e.query("GET_CROP_ALLOW_ROTATE")&&(e.ref.rotator=e.appendChildView(e.createChildView(br,{rotation:0,opacity:0,translateY:20,id:n.id}))),e.ref.resizer=e.appendChildView(e.createChildView(ri,{pivotPoint:{x:0,y:0}})),e.ref.updateControls=function(){var t=e.query("GET_IMAGE");if(function(t,e){Array.from(t.element.querySelectorAll(".doka--icon-turn rect")).forEach(function(t){e>1&&(t.setAttribute("x",t.previousElementSibling?5:4),t.setAttribute("width",9)),e<1&&(t.setAttribute("y",11),t.setAttribute("height",10))})}(e,t.height/t.width),e.ref.cropToggleLimitDropdown&&(e.ref.isLimitedToImageBounds=e.query("GET_CROP_LIMIT_TO_IMAGE_BOUNDS"),oi(e,e.ref.isLimitedToImageBounds),e.ref.cropToggleLimitDropdown.selectedValue=e.ref.isLimitedToImageBounds),e.ref.aspectRatioDropdown){var n=e.query("GET_MIN_IMAGE_SIZE"),r=i.filter(function(e){if(!e.value.aspectRatio)return!0;if(e.value.aspectRatio<1){if(t.naturalWidth*e.value.aspectRatio<n.height)return!1}else if(t.naturalHeight/e.value.aspectRatio<n.width)return!1;return!0});e.ref.aspectRatioDropdown.options=r.map(function(t){return a({},t,{icon:function(t){var e,n;t>1?(n=14,e=Math.round(n/t)):(e=14,n=Math.round(e*t));var r=Math.round(.5*(23-e)),i=Math.round(.5*(23-n));return'<svg width="23" height="23" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false"><g fill="currentColor"><rect x="'.concat(r,'" y="').concat(i,'" width="').concat(e,'" height="').concat(n,'" rx="2.5"/></g></svg>')}(t.value.aspectRatio)})})}},e.ref.isModal=/modal|fullscreen/.test(e.query("GET_STYLE_LAYOUT_MODE"))},read:function(t){var e=t.root,n=t.props;if(n.hidden)e.ref.menuItemsRequiredWidth=null;else{var r=e.rect;if(0!==r.element.width&&0!==r.element.height){if(null===e.ref.menuItemsRequiredWidth){var i=e.ref.menu.childViews.reduce(function(t,e){return t+e.rect.outer.width},0);e.ref.menuItemsRequiredWidth=0===i?null:i}var o=e.ref.subject.rect.element,a=o.left,l=o.top,c=o.width,u=o.height;n.stagePosition={x:a,y:l,width:c,height:u}}}},shouldUpdateChildViews:function(t){var e=t.props,n=t.actions;return!e.hidden||e.hidden&&n&&n.length},write:k({SHOW_VIEW:function(t){var e=t.root,n=t.action,r=t.props,i=e.ref,o=i.menu,a=i.rotator,l=i.subject;r.viewId===n.id?(l.opacity=1,o.opacity=1,a&&(a.opacity=1,a.translateY=0),r.hidden=!1,e.ref.isHiding=!1,e.ref.updateControls()):(l.opacity=0,o.opacity=0,a&&(a.opacity=0,a.translateY=20),e.ref.isHiding=!0)},UNLOAD_IMAGE:function(t){var e=t.root.ref,n=e.menu,r=e.rotator;n.opacity=0,r&&(r.opacity=0,r.translateY=20)},DID_PRESENT_IMAGE:function(t){var e=t.root,n=e.ref,r=n.menu,i=n.rotator;r.opacity=1,i&&(i.opacity=1,i.translateY=0),e.ref.updateControls()}},function(t){var e=t.root,n=t.props,r=t.timestamp,i=e.ref,o=i.resizer,a=i.subject,l=i.menu,c=i.rotator,u=i.isHiding,s=i.cropToggleLimitDropdown,f=i.aspectRatioDropdown,d=n.hidden,h=0===a.opacity&&0===l.opacity&&(!c||c&&0===c.opacity);if(!d&&u&&h&&(e.ref.isHiding=!1,n.hidden=!0),!n.hidden){var p=e.query("GET_CROP",n.id,r);if(p){if(f){var g=e.query("GET_ACTIVE_CROP_ASPECT_RATIO"),m=e.query("GET_SIZE"),v=f.selectedValue;v?(v.aspectRatio!==g&&ai(e,g),v.aspectRatio===g&&v.width===m.width&&v.height===m.height||(f.selectedValue={aspectRatio:g,width:m.width,height:m.height})):(f.selectedValue={aspectRatio:g,width:m.width,height:m.height},ai(e,g))}if(s&&e.ref.isLimitedToImageBounds!==p.isLimitedToImageBounds&&(e.ref.isLimitedToImageBounds=p.isLimitedToImageBounds,oi(e,p.isLimitedToImageBounds),s.selectedValue=p.isLimitedToImageBounds),o.pivotPoint={x:.5*o.rect.element.width,y:.5*o.rect.element.height},c&&(c.animate=!p.isDraft,c.rotation=p.rotation.sub,c.setAllowInteraction(e.query("IS_ACTIVE_VIEW","crop"))),l.element.dataset.layout=e.ref.menuItemsRequiredWidth>e.ref.menu.rect.element.width?"compact":"spacious",e.query("GET_CROP_RESIZE_SCROLL_RECT_ONLY")){var y=e.query("GET_STAGE"),E=y.x,_=y.y,w=e.query("GET_ROOT"),T=e.ref.isModal?w.left:0,x=e.ref.isModal?w.top:0;o.scrollRect={x:T+E+p.cropRect.x,y:x+_+p.cropRect.y+n.offsetTop,width:p.cropRect.width,height:p.cropRect.height}}}}})}),ci=D({name:"size-input",mixins:{listeners:!0,apis:["id","value","placeholder","getValue","setValue","setPlaceholder","hasFocus","onChange"]},create:function(t){var e=t.root,n=t.props,r=n.id,i=n.min,o=n.max,a=n.value,l=n.placeholder,c=n.onChange,u=void 0===c?function(){}:c,s=n.onBlur,f=void 0===s?function(){}:s,d="doka--".concat(r,"-").concat($()),h=v("input",{type:"number",step:1,id:d,min:i,max:o,value:a,placeholder:l}),p=h.getAttribute("max").length,g=v("label",{for:d});g.textContent=n.label;var m=function(t,e,n){return F(t)?((t=t.replace(/[^0-9]/g,"")).length>p&&(t=t.slice(0,p)),t=parseInt(t,10)):t=Math.round(t),isNaN(t)?null:ht(t,e,n)},y=function(t){return t.length?parseInt(h.value,10):null};e.ref.handleInput=function(){h.value=m(h.value,1,o),u(y(h.value))},e.ref.handleBlur=function(){h.value=m(h.value,i,o),f(y(h.value))},h.addEventListener("input",e.ref.handleInput),h.addEventListener("blur",e.ref.handleBlur),e.appendChild(h),e.appendChild(g),e.ref.input=h,n.hasFocus=function(){return h===document.activeElement},n.getValue=function(){return y(h.value)},n.setValue=function(t){return h.value=t?m(t,1,999999):null},n.setPlaceholder=function(t){return h.placeholder=t}},destroy:function(t){var e=t.root;e.ref.input.removeEventListener("input",e.ref.handleInput),e.ref.input.removeEventListener("blur",e.ref.handleBlur)}}),ui=D({name:"checkable",tag:"span",mixins:{listeners:!0,apis:["id","checked","onChange","onSetValue","setValue","getValue"]},create:function(t){var e=t.root,n=t.props,r=n.id,i=n.checked,o=n.onChange,a=void 0===o?function(){}:o,l=n.onSetValue,c=void 0===l?function(){}:l,u="doka--".concat(r,"-").concat($()),s=v("input",{type:"checkbox",value:1,id:u});s.checked=i,e.ref.input=s;var f=v("label",{for:u});f.innerHTML=n.label,e.appendChild(s),e.appendChild(f),e.ref.handleChange=function(){c(s.checked),a(s.checked)},s.addEventListener("change",e.ref.handleChange),n.getValue=function(){return s.checked},n.setValue=function(t){s.checked=t,c(s.checked)},setTimeout(function(){c(s.checked)},0)},destroy:function(t){var e=t.root;e.ref.input.removeEventListener("change",e.ref.handleChange)}}),si=null,fi=D({ignoreRect:!0,name:"resize-form",tag:"form",mixins:{styles:["opacity"],animations:{opacity:{type:"spring",mass:15,delay:150}}},create:function(t){var e=t.root;e.element.setAttribute("novalidate","novalidate"),e.element.setAttribute("action","#"),e.ref.shouldBlurKeyboard=ut()||(null===si&&(si=/Android/i.test(navigator.userAgent)),si);var n=e.query("GET_SIZE_MAX"),r=e.query("GET_SIZE_MIN"),i=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=t.axisLock,o=void 0===i?"none":i,a=t.enforceLimits,l=void 0!==a&&a,c=e.ref,u=c.inputImageWidth,s=c.inputImageHeight,f=c.buttonConfirm,d=e.query("GET_SIZE_ASPECT_RATIO_LOCK"),h=e.query("GET_CROP_RECTANGLE_ASPECT_RATIO"),p={width:u.getValue(),height:s.getValue()},g=Ht(p,l?r:{width:1,height:1},l?n:{width:999999,height:999999},d?h:null,o);if(d)"width"===o?s.setValue(g.width/h):"height"===o?u.setValue(g.height*h):(u.setValue(g.width||g.height*h),s.setValue(g.height||g.width/h));else if(g.width&&!g.height){var m=Math.round(g.width/h),v=Ht({width:g.width,height:m},l?r:{width:1,height:1},l?n:{width:999999,height:999999},h,o);l&&u.setValue(Math.round(v.width)),s.setPlaceholder(Math.round(v.height))}else if(g.height&&!g.width){var y=Math.round(g.height*h);u.setPlaceholder(y)}var E=e.query("GET_SIZE_INPUT"),_=E.width,w=E.height,x=T(_)?Math.round(_):null,R=T(w)?Math.round(w):null,A=u.getValue(),C=s.getValue(),I=A!==x||C!==R;return f.opacity=I?1:0,e.dispatch("KICK"),{width:u.getValue(),height:s.getValue()}},o=e;e.appendChildView(e.createChildView(di("Image size",function(t){var e=t.root,a=e.query("GET_SIZE"),l=e.appendChildView(e.createChildView(ci,{id:"image-width",label:e.query("GET_LABEL_RESIZE_WIDTH"),value:T(a.width)?Math.round(a.width):null,min:r.width,max:n.width,placeholder:0,onChange:function(){return i({axisLock:"width"})},onBlur:function(){return i({enforceLimits:!1})}})),c=e.appendChildView(e.createChildView(ui,{id:"aspect-ratio-lock",label:Mn('<g fill="none" fill-rule="evenodd"><path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" class="doka--aspect-ratio-lock-ring" d="M9.401 10.205v-.804a2.599 2.599 0 0 1 5.198 0V14"/><rect fill="currentColor" x="7" y="10" width="10" height="7" rx="1.5"/></g>'),checked:e.query("GET_SIZE_ASPECT_RATIO_LOCK"),onSetValue:function(t){var e=t?0:-3;c.element.querySelector(".doka--aspect-ratio-lock-ring").setAttribute("transform","translate(0 ".concat(e,")"))},onChange:function(t){e.dispatch("RESIZE_SET_OUTPUT_SIZE_ASPECT_RATIO_LOCK",{value:t}),i()}})),u=e.appendChildView(e.createChildView(ci,{id:"image-height",label:e.query("GET_LABEL_RESIZE_HEIGHT"),value:T(a.height)?Math.round(a.height):null,min:r.height,max:n.height,placeholder:0,onChange:function(){return i({axisLock:"height"})},onBlur:function(){return i({enforceLimits:!1})}}));o.ref.aspectRatioLock=c,o.ref.inputImageWidth=l,o.ref.inputImageHeight=u}))),e.ref.buttonConfirm=e.appendChildView(e.createChildView(Sn,{name:"app action-confirm icon-only",label:e.query("GET_LABEL_RESIZE_APPLY_CHANGES"),action:function(){},opacity:0,icon:Mn('<polyline fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" points="20 6 9 17 4 12"></polyline>'),type:"submit"})),e.ref.confirmForm=function(t){var n=i({enforceLimits:!0});t.preventDefault();var r=e.ref,o=r.shouldBlurKeyboard,a=r.buttonConfirm;o&&(document.activeElement.blur(),a.element.focus()),a.opacity=0,e.dispatch("RESIZE_SET_OUTPUT_SIZE",n)},e.element.addEventListener("submit",e.ref.confirmForm)},destroy:function(t){var e=t.root;e.element.removeEventListener("submit",e.ref.confirmForm)},write:k({EDIT_RESET:function(t){var e=t.root,n=e.query("GET_SIZE"),r=e.ref,i=r.inputImageWidth,o=r.inputImageHeight,a=r.aspectRatioLock,l=r.buttonConfirm;i.setValue(n.width),o.setValue(n.height),a.setValue(e.query("GET_SIZE_ASPECT_RATIO_LOCK")),l.opacity=0},RESIZE_SET_OUTPUT_SIZE:function(t){var e=t.root,n=t.action,r=e.ref,i=r.inputImageWidth,o=r.inputImageHeight;i.setValue(n.width),o.setValue(n.height)},CROP_SET_ASPECT_RATIO:function(t){var e=t.root,n=t.props,r=t.action,i=t.timestamp,o=e.query("GET_CROP",n.id,i);if(o){var a=o.cropStatus,l=e.ref,c=l.inputImageWidth,u=l.inputImageHeight;null!==r.value?(c.setValue(a.image.width),c.setPlaceholder(a.crop.width),u.setValue(a.image.height),u.setPlaceholder(a.crop.height)):c.getValue()&&u.getValue()&&(u.setValue(null),u.setPlaceholder(a.crop.height))}}},function(t){var e=t.root,n=t.props,r=t.timestamp,i=e.query("GET_CROP",n.id,r);if(i){e.opacity;var o=i.cropStatus,a=e.ref,l=a.inputImageWidth,c=a.inputImageHeight;if(!l.hasFocus()&&!c.hasFocus()){var u=e.query("GET_CROP_RECTANGLE_ASPECT_RATIO");if(null===l.getValue()&&null===c.getValue())l.setPlaceholder(o.crop.width),c.setPlaceholder(o.crop.height);else if(null===l.getValue()&&null!==o.image.height){var s=Math.round(o.image.height*u);l.setPlaceholder(s)}else if(null===c.getValue()&&null!==o.image.width){var f=Math.round(o.image.width/u);c.setPlaceholder(f)}}}})}),di=function(t,e){return D({tag:"fieldset",create:function(n){var r=n.root,i=v("legend");i.textContent=t,r.element.appendChild(i),e({root:r})}})},hi=D({name:"resize",ignoreRect:!0,mixins:{apis:["viewId","stagePosition","hidden"]},create:function(t){var e=t.root,n=t.props;n.viewId="resize",n.hidden=!1,e.ref.isHiding=!1,e.ref.form=e.appendChildView(e.createChildView(fi,{opacity:0,id:n.id}))},read:function(t){var e=t.root,n=t.props;if(!n.hidden){var r=e.rect;if(0!==r.element.width&&0!==r.element.height){var i=e.ref.form.rect;n.stagePosition={x:r.element.left,y:r.element.top+i.element.height,width:r.element.width,height:r.element.height-i.element.height}}}},shouldUpdateChildViews:function(t){var e=t.props,n=t.actions;return!e.hidden||e.hidden&&n&&n.length},write:k({SHOW_VIEW:function(t){var e=t.root,n=t.action,r=t.props;n.id===r.viewId?(e.ref.isHiding=!1,e.ref.form.opacity=1):(e.ref.isHiding=!0,e.ref.form.opacity=0)}},function(t){var e=t.root,n=t.props,r=e.ref,i=r.form,o=r.isHiding,a=n.hidden;o&&0===i.opacity&&!a?n.hidden=!0:n.hidden=!1})}),pi=D({name:"range-input",tag:"span",mixins:{listeners:!0,apis:["onUpdate","setValue","getValue","setAllowInteraction"]},create:function(t){var e=t.root,n=t.props,r=n.id,i=n.min,o=n.max,a=n.step,l=n.value,c=n.onUpdate,u=void 0===c?function(){}:c,s="doka--".concat(r,"-").concat($()),f=v("input",{type:"range",id:s,min:i,max:o,step:a});f.value=l,e.ref.input=f;var d=v("span");d.className="doka--range-input-inner";var h=v("label",{for:s});h.innerHTML=n.label;var p=i+.5*(o-i);e.element.dataset.centered=l===p,e.ref.handleRecenter=function(){n.setValue(p),e.ref.handleChange()};var g=v("button",{type:"button"});g.textContent="center",g.addEventListener("click",e.ref.handleRecenter),e.ref.recenter=g,d.appendChild(f),d.appendChild(g),e.appendChild(h),e.appendChild(d),e.ref.handleChange=function(){var t=n.getValue();e.element.dataset.centered=t===p,u(t)},f.addEventListener("input",e.ref.handleChange);var m=null;e.ref.dragger=qn(d,function(){m=f.getBoundingClientRect()},function(t){var n=(t.pageX-m.left)/m.width;f.value=i+n*(o-i),e.ref.handleChange()},function(){},{stopPropagation:!0}),n.getValue=function(){return parseFloat(f.value)},n.setValue=function(t){return f.value=t},n.setAllowInteraction=function(t){t?e.ref.dragger.enable():e.ref.dragger.disable()}},destroy:function(t){var e=t.root;e.ref.dragger.destroy(),e.ref.recenter.removeEventListener("click",e.ref.handleRecenter),e.ref.input.removeEventListener("input",e.ref.handleChange)}}),gi={brightness:{icon:Mn('<g fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="7"/><line x1="12" y1="1" x2="12" y2="3"/><line x1="12" y1="21" x2="12" y2="23"/><line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/><line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/><line x1="1" y1="12" x2="3" y2="12"/><line x1="21" y1="12" x2="23" y2="12"/><line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/><line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/></g>')},contrast:{icon:Mn('<g fill="none" fill-rule="evenodd"><circle stroke="currentColor" stroke-width="3" cx="12" cy="12" r="10"/><path d="M12 2v20C6.477 22 2 17.523 2 12S6.477 2 12 2z" fill="currentColor"/></g>')},exposure:{icon:Mn('<g fill="none" fill-rule="evenodd"><rect stroke="currentColor" stroke-width="3" x="2" y="2" width="20" height="20" rx="4"/><path d="M20.828 3.172L3.172 20.828A3.987 3.987 0 0 1 2 18V6a4 4 0 0 1 4-4h12c1.105 0 2.105.448 2.828 1.172zM7 7H5v2h2v2h2V9h2V7H9V5H7v2zM12 15h5v2h-5z" fill="currentColor"/></g>')},saturation:{icon:Mn('<g fill="none" fill-rule="evenodd"><rect stroke="currentColor" stroke-width="3" x="2" y="2" width="20" height="20" rx="4"/><path fill="currentColor" opacity=".3" d="M7 2.5h5v18.75H7z"/><path fill="currentColor" opacity=".6" d="M12 2.5h5v18.75h-5z"/><path fill="currentColor" opacity=".9" d="M17 2.5h4v18.75h-4z"/></g>')}},mi=D({ignoreRect:!0,name:"color-form",tag:"form",mixins:{styles:["opacity"],animations:{opacity:{type:"spring",mass:15}}},create:function(t){var e=t.root;e.element.setAttribute("novalidate","novalidate");var n=e.query("GET_COLOR_VALUES");e.ref.tools=Object.keys(gi).reduce(function(t,r){var i=r,o=gi[r].icon,a=e.query("GET_LABEL_COLOR_".concat(r.toUpperCase())),l=e.query("GET_COLOR_".concat(r.toUpperCase(),"_RANGE")),c=n[r];return t[i]={view:e.appendChildView(e.createChildView(pi,{id:i,label:"".concat(o,"<span>").concat(a,"</span>"),min:l[0],max:l[1],step:.01,value:c,onUpdate:function(t){return e.dispatch("COLOR_SET_COLOR_VALUE",{key:i,value:t})}}))},t},{})},write:k({COLOR_SET_VALUE:function(t){var e=t.root,n=t.action;e.ref.tools[n.key].view.setValue(n.value)},SHOW_VIEW:function(t){var e=t.root,n=t.action;Object.keys(e.ref.tools).forEach(function(t){e.ref.tools[t].view.setAllowInteraction("color"===n.id)})}})}),vi=null,yi=null,Ei=function(t,e){var n=e.brightness,r=e.exposure,i=e.contrast,o=e.saturation;if(0!==n){var a=n<0,l=a?"multiply":"overlay",c=a?0:255,u=a?Math.abs(n):1-n;t.ref.imageOverlay.style.cssText="mix-blend-mode: ".concat(l,"; background: rgba(").concat(c,",").concat(c,",").concat(c,",").concat(u,")")}return t.ref.imageOverlay.style.cssText="background:transparent",t.ref.image.style.cssText="filter: brightness(".concat(r,") contrast(").concat(i,") saturate(").concat(o,")"),e},_i=Object.keys(gi),wi=function(t){return D({ignoreRect:!0,tag:"li",name:"filter-tile",mixins:{styles:["opacity","translateY"],animations:{translateY:{type:"spring",delay:10*t},opacity:{type:"spring",delay:30*t}}},create:function(t){var e=t.root,n=t.props,r="doka--filter-".concat(n.style,"-").concat($()),i=v("input",{id:r,type:"radio",name:"filter"});e.appendChild(i),i.checked=n.selected,i.value=n.style,i.addEventListener("change",function(){i.checked&&n.onSelect()});var o=v("label",{for:r});o.textContent=n.label,e.appendChild(o);var a=n.imageData,l=Math.min(a.width,a.height),c=l,u=v("canvas");u.width=l,u.height=c;var s=u.getContext("2d");e.ref.image=u;var f=v("div");e.ref.imageOverlay=f;var d={x:.5*l-.5*a.width,y:.5*c-.5*a.height},h=v("div");h.appendChild(u),h.appendChild(f),e.appendChild(h),e.ref.imageWrapper=h,n.matrix?(vi||(vi=Le(Oe)),clearTimeout(yi),vi.post({transforms:[{type:"filter",data:n.matrix}],imageData:a},function(t){s.putImageData(t,d.x,d.y),clearTimeout(yi),yi=setTimeout(function(){vi.terminate(),vi=null},1e3)},[a.data.buffer]),e.ref.activeColors=Ei(e,e.query("GET_COLOR_VALUES"))):s.putImageData(a,d.x,d.y)},write:function(t){var e=t.root;if(!(e.opacity<=0)){var n=e.query("GET_COLOR_VALUES"),r=e.ref.activeColors;(!r&&n||!function(t,e){return _i.findIndex(function(n){return t[n]!==e[n]})<0}(r,n))&&(e.ref.activeColors=n,Ei(e,n))}}})},Ti=function(t,e){return Array.isArray(t)&&Array.isArray(e)&&t.length===e.length&&t.every(function(t,n){return t===e[n]})},xi=D({ignoreRect:!0,tag:"ul",name:"filter-list",mixins:{apis:["filterOpacity","hidden"]},create:function(t){var e=t.root,n=t.props;e.element.setAttribute("role","list"),e.ref.tiles=[];var r=e.query("GET_THUMB_IMAGE_DATA"),i=e.query("GET_FILTERS"),o=[];d(i,function(t,e){o.push(a({id:t},e))}),e.ref.activeFilter=e.query("GET_FILTER"),e.ref.tiles=o.map(function(t,i){var o=t.matrix(),a=e.ref.activeFilter===t.id||Ti(e.ref.activeFilter,o)||0===i;return e.appendChildView(e.createChildView(wi(i),{opacity:0,translateY:-5,id:n.id,style:t.id,label:t.label,matrix:o,imageData:function(t){var e;try{e=new ImageData(t.width,t.height)}catch(n){e=document.createElement("canvas").getContext("2d").createImageData(t.width,t.height)}return e.data.set(new Uint8ClampedArray(t.data)),e}(r),selected:a,onSelect:function(){return e.dispatch("FILTER_SET_FILTER",{value:o?t.id:null})}}))})},write:function(t){var e=t.root,n=t.props;if(!n.hidden){var r=e.query("GET_FILTER");if(r!==e.ref.activeFilter){e.ref.activeFilter=r;var i=e.query("GET_FILTERS"),o=r?F(r)?r:an(r)?Object.keys(i).find(function(t){return Ti(i[t].matrix(),r)}):null:"original";Array.from(e.element.querySelectorAll("input")).forEach(function(t){return t.checked=t.value===o})}e.query("IS_ACTIVE_VIEW","filter")?e.ref.tiles.forEach(function(t){t.opacity=1,t.translateY=0}):e.ref.tiles.forEach(function(t){t.opacity=0,t.translateY=-5}),n.filterOpacity=e.ref.tiles.reduce(function(t,e){return t+e.opacity},0)/e.ref.tiles.length}}}),Ri=D({name:"filter-scroller",ignoreRect:!0,ignoreRectUpdate:!0,mixins:{styles:["opacity"],animations:{opacity:{type:"spring"}},apis:["hidden","filterOpacity"]},create:function(t){var e,n=t.root,r=t.props;n.ref.filters=n.appendChildView(n.createChildView(xi,{id:r.id})),n.element.isScrollContainer=!0,Zr()&&(n.ref.handleMouseWheel=function(t){var e=n.element.scrollLeft,r=n.ref.scrollWidth-n.rect.element.width,i=e+t.deltaX;(i<0||i>r)&&(n.element.scrollLeft=Math.min(Math.max(i,0),r),t.preventDefault())},n.element.addEventListener("mousewheel",n.ref.handleMouseWheel),n.element.dataset.dragState="end",n.ref.dragger=qn(n.ref.filters.element,function(){n.element.dataset.dragState="start",e=n.element.scrollLeft},function(t,r){n.element.scrollLeft=e-r.x,vt({x:0,y:0},r)>0&&(n.element.dataset.dragState="dragging")},function(){n.element.dataset.dragState="end"},{stopPropagation:!0}))},destroy:function(t){var e=t.root;e.ref.handleMouseWheel&&e.element.removeEventListener("mousewheel",e.ref.handleMouseWheel),e.ref.dragger&&e.ref.dragger.destroy()},read:function(t){var e=t.root;e.ref.scrollWidth=e.element.scrollWidth},write:function(t){var e=t.root,n=t.props;e.ref.filters.hidden=n.hidden,n.filterOpacity=e.ref.filters.filterOpacity}}),Ai=D({name:"filter",ignoreRect:!0,mixins:{apis:["viewId","stagePosition","hidden"]},create:function(t){var e=t.root,n=t.props;n.viewId="filter",n.hidden=!1,e.ref.isHiding=!1,e.ref.filters=e.appendChildView(e.createChildView(Ri,{id:n.id}))},read:function(t){var e=t.root,n=t.props;if(e.ref.filters&&!n.hidden){var r=e.rect;if(0!==r.element.width&&0!==r.element.height){var i=e.ref.filters.rect,o=0===i.element.top,a=o?r.element.top+i.element.height+i.element.marginBottom:r.element.top,l=o?r.element.height-i.element.height-i.element.marginBottom:r.element.height-i.element.height-r.element.top;n.stagePosition={x:r.element.left,y:a,width:r.element.width,height:l}}}},shouldUpdateChildViews:function(t){var e=t.props,n=t.actions;return!e.hidden||e.hidden&&n&&n.length},write:k({SHOW_VIEW:function(t){var e=t.root,n=t.action,r=t.props;e.ref.filters&&(n.id===r.viewId?(e.ref.isHiding=!1,r.hidden=!1,e.ref.filters.hidden=!1):e.ref.isHiding=!0)}},function(t){var e=t.root,n=t.props;e.ref.filters.opacity=e.ref.isHiding||e.ref.filters.hidden?0:1,e.ref.isHiding&&e.ref.filters.filterOpacity<=0&&(e.ref.isHiding=!1,n.hidden=!0,e.ref.filters.hidden=!0)})}),Ci=D({name:"color",ignoreRect:!0,mixins:{apis:["viewId","stagePosition","hidden"]},create:function(t){var e=t.root,n=t.props;n.viewId="color",n.hidden=!1,e.ref.isHiding=!1,e.ref.form=e.appendChildView(e.createChildView(mi,{opacity:0,id:n.id}))},read:function(t){var e=t.root,n=t.props;if(!n.hidden){var r=e.rect;if(0!==r.element.width&&0!==r.element.height){var i=e.ref.form.rect,o=i.element.height,a=0===i.element.top,l=a?r.element.top+o:r.element.top,c=a?r.element.height-o:r.element.height-o-r.element.top;n.stagePosition={x:r.element.left+i.element.left,y:l,width:r.element.width-i.element.left,height:c}}}},shouldUpdateChildViews:function(t){var e=t.props,n=t.actions;return!e.hidden||e.hidden&&n&&n.length},write:k({SHOW_VIEW:function(t){var e=t.root,n=t.action,r=t.props;n.id===r.viewId?(e.ref.isHiding=!1,e.ref.form.opacity=1,r.hidden=!1):(e.ref.isHiding=!0,e.ref.form.opacity=0)}},function(t){var e=t.root,n=t.props;e.ref.isHiding&&0===e.ref.form.opacity&&(e.ref.isHiding=!1,n.hidden=!0)})}),Ii=D({ignoreRect:!0,tag:"div",name:"markup-color",mixins:{animations:{opacity:"spring"},styles:["opacity"],apis:["onSelect","selectedValue"]},create:function(t){var e=t.root,n=t.props,r=n.colors,i=n.name,o=n.onSelect;e.ref.handleChange=function(t){o(t.target.value),t.stopPropagation()},e.element.addEventListener("change",e.ref.handleChange);var a=v("ul");if(e.ref.inputs=r.map(function(t){var e="doka--color-"+$(),n=v("li"),r=v("input",{id:e,name:i,type:"radio",value:t[1]}),o=v("label",{for:e,title:t[0],style:"background-color: "+(t[2]||t[1])});return o.textContent=t[0],y(n)(r),y(n)(o),y(a)(n),r}),e.element.appendChild(a),e.query("GET_MARKUP_ALLOW_CUSTOM_COLOR")&&function(){try{var t=v("input",{type:"color"});return!!ut()||"color"===t.type&&"number"!=typeof t.selectionStart}catch(t){return!1}}()){var l=v("div",{class:"doka--color-input"}),u="doka--color-"+$(),s=v("label",{for:u});s.textContent="Choose color";var f=v("input",{id:u,name:i,type:"color"}),d=v("span",{class:"doka--color-visualizer"}),h=v("span",{class:"doka--color-brightness"});e.ref.handleCustomColorChange=function(){var t=Yt(f.value),e=function(t,e,n){var r,i=Math.max(t,e,n),o=Math.min(t,e,n),a=i-o,l=0===i?0:a/i,c=i/255;switch(i){case o:r=0;break;case t:r=e-n+a*(e<n?6:0),r/=6*a;break;case e:r=n-t+2*a,r/=6*a;break;case n:r=t-e+4*a,r/=6*a}return[r,l,c]}.apply(void 0,c(t)),n=360*e[0]-90,r=.625*e[1],i=1-e[2];d.style.backgroundColor=f.value,d.style.transform="rotateZ(".concat(n,"deg) translateX(").concat(r,"em)"),h.style.opacity=i,o(f.value)};var p=!0;e.ref.handleCustomColorSelect=function(t){p?o(t.target.value):e.ref.handleCustomColorChange(),p=!1},f.addEventListener("click",e.ref.handleCustomColorSelect),f.addEventListener("input",e.ref.handleCustomColorChange),y(l)(f),y(l)(s),y(l)(d),y(l)(h),e.appendChild(l),e.ref.customInput=f}},write:function(t){var e=t.root,n=t.props;if(n.selectedValue!==e.ref.activeSelectedValue){e.ref.activeSelectedValue=n.selectedValue;var r=!1;if(e.ref.inputs.forEach(function(t){t.checked=t.value===n.selectedValue,t.checked&&(r=!0)}),!e.ref.customInput)return;e.ref.customInput.dataset.selected=e.ref.inputs.length&&!r,r||(e.ref.customInput.value=n.selectedValue,e.ref.handleCustomColorChange())}},destroy:function(t){var e=t.root;e.element.removeEventListener("change",e.ref.handleChange),e.ref.customInput&&(e.ref.customInput.removeEventListener("click",e.ref.handleCustomColorSelect),e.ref.customInput.removeEventListener("input",e.ref.handleCustomColorChange))}}),Oi=["fontFamily","fontSize","fontWeight","textAlign","backgroundColor","fontColor","borderColor","borderWidth","borderStyle","lineColor","lineWidth","lineDecoration","lineJoin","lineCap"],Mi=function(t){return'<svg width="23" height="23" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false">'.concat(t,"</svg>")},Si=D({name:"markup-tools",ignoreRect:!0,mixins:{apis:["onUpdate"],animations:{translateY:"spring",opacity:"spring"},styles:["translateY","opacity"]},create:function(t){var e=t.root,n=t.props.onUpdate;e.ref.colorSelect=e.appendChildView(e.createChildView(Ii,{onSelect:function(t){e.ref.colorSelect.selectedValue=t,n("color",t)},name:"color-select",colors:e.query("GET_MARKUP_COLOR_OPTIONS")})),e.ref.shapeStyleSelect=e.appendChildView(e.createChildView(Mr,{onSelect:function(t){e.ref.shapeStyleSelect.selectedValue=t,n("shapeStyle",[t[1],t[2]])},name:"tool",label:e.query("GET_LABEL_MARKUP_SELECT_SHAPE_STYLE"),direction:"up",options:e.query("GET_MARKUP_SHAPE_STYLE_OPTIONS").map(function(t){return{value:t,label:t[0],icon:function(t){var e=0===t?"currentColor":"none",n=t;return Mi('<rect stroke="'.concat(0===t?"none":"currentColor",'" fill="').concat(e,'" stroke-width="').concat(n,'" x="2" y="3" width="17" height="17" rx="3"/>'))}(t[3])}})})),e.ref.lineStyleSelect=e.appendChildView(e.createChildView(Mr,{onSelect:function(t){e.ref.lineStyleSelect.selectedValue=t,n("lineStyle",[t[1],t[2]])},name:"tool",label:e.query("GET_LABEL_MARKUP_SELECT_LINE_STYLE"),direction:"up",options:e.query("GET_MARKUP_LINE_STYLE_OPTIONS").map(function(t){return{value:t,label:t[0],icon:function(t){return Mi('<line stroke-linecap="round" stroke-linejoin="round" stroke="currentColor" stroke-width="'.concat(t,'" x1="3" y1="12" x2="20" y2="12"/>'))}(t[3])}})})),e.ref.lineDecorationSelect=e.appendChildView(e.createChildView(Mr,{onSelect:function(t){e.ref.lineDecorationSelect.selectedValue=t,n("lineDecoration",t)},name:"tool",label:e.query("GET_LABEL_MARKUP_SELECT_LINE_DECORATION"),direction:"up",options:e.query("GET_MARKUP_LINE_DECORATION_OPTIONS").map(function(t){return{value:t[1],label:t[0]}})})),e.ref.fontFamilySelect=e.appendChildView(e.createChildView(Mr,{onSelect:function(t){e.ref.fontFamilySelect.selectedValue=t,n("fontFamily",t)},name:"tool",label:e.query("GET_LABEL_MARKUP_SELECT_FONT_FAMILY"),direction:"up",options:e.query("GET_MARKUP_FONT_FAMILY_OPTIONS").map(function(t){return{value:t[1],label:'<span style="font-family:'.concat(t[1],';font-weight:600;">').concat(t[0],"</span>")}})})),e.ref.fontSizeSelect=e.appendChildView(e.createChildView(Mr,{onSelect:function(t){e.ref.fontSizeSelect.selectedValue=t,n("fontSize",t)},name:"tool",label:e.query("GET_LABEL_MARKUP_SELECT_FONT_SIZE"),direction:"up",options:e.query("GET_MARKUP_FONT_SIZE_OPTIONS").map(function(t){return{value:t[1],label:t[0]}})}))},write:k({SWITCH_MARKUP_UTIL:function(t){var e=t.root;if("draw"===t.action.util){var n=e.ref,r=n.colorSelect,i=n.fontFamilySelect,o=n.fontSizeSelect,a=n.shapeStyleSelect,l=n.lineStyleSelect;[i,o,a,n.lineDecorationSelect].forEach(function(t){t.element.dataset.active="false"}),[r,l].forEach(function(t){t.element.dataset.active="true"})}},MARKUP_SELECT:function(t){var e=t.root,n=t.action,r=e.ref,i=r.colorSelect,o=r.fontFamilySelect,a=r.fontSizeSelect,c=r.shapeStyleSelect,u=r.lineStyleSelect,s=r.lineDecorationSelect,f=n.id?e.query("GET_MARKUP_BY_ID",n.id):null,d=[i,o,a,c,u,s],h=[];if(f){var p=l(f,2),g=p[0],m=p[1],v=Array.isArray(m.allowEdit)?m.allowEdit:!1===m.allowEdit?[]:Oi,y=Oi.reduce(function(t,e){return t[e]=-1!==v.indexOf(e),t},{});if(y.color=!!v.find(function(t){return/[a-z]Color/.test(t)}),"image"!==g&&y.color&&(i.selectedValue=bi(m),h.push(i)),"text"===g&&(y.fontFamily&&(o.selectedValue=m.fontFamily,h.push(o)),y.fontSize&&(a.selectedValue=m.fontSize,h.push(a))),("rect"===g||"ellipse"===g)&&y.borderStyle){var E=e.query("GET_MARKUP_SHAPE_STYLE_OPTIONS").find(function(t){var e=m.borderWidth===t[1],n=m.borderStyle===t[2]||Ti(m.borderStyle,t[2]);return e&&n});c.selectedValue=E,h.push(c)}if("line"===g||"path"===g){if(y.lineWidth){var _=e.query("GET_MARKUP_LINE_STYLE_OPTIONS").find(function(t){var e=m.lineWidth===t[1],n=m.lineStyle===t[2]||Ti(m.lineStyle,t[2]);return e&&n});u.selectedValue=_,h.push(u)}"line"===g&&y.lineDecoration&&(s.selectedValue=m.lineDecoration,h.push(s))}d.forEach(function(t){t.element.dataset.active="false"}),h.forEach(function(t){t.element.dataset.active="true"})}},MARKUP_UPDATE:function(t){var e=t.root,n=t.action,r=n.style,i=n.value;e.ref[r+"Select"]&&(e.ref[r+"Select"].selectedValue=i)}})}),bi=function(t){var e=t.fontColor,n=t.backgroundColor,r=t.lineColor,i=t.borderColor;return e||n||r||i},Li={crop:li,resize:hi,filter:Ai,color:Ci,markup:D({name:"markup",ignoreRect:!0,mixins:{apis:["viewId","stagePosition","hidden"]},create:function(t){var e=t.root,n=t.props;n.viewId="markup",n.hidden=!1,e.ref.isHiding=!1;var r=[["select",{label:e.query("GET_LABEL_MARKUP_TOOL_SELECT"),icon:Mn('<g fill="none" fill-rule="evenodd"><path d="M7 13H5a1 1 0 01-1-1V5a1 1 0 011-1h7a1 1 0 011 1v2" stroke="currentColor" stroke-width="2" stroke-linecap="round"/><path d="M10.22 8.914l12.58 5.18a1 1 0 01.012 1.844l-4.444 1.904a1 1 0 00-.526.526l-1.904 4.444a1 1 0 01-1.844-.013l-5.18-12.58a1 1 0 011.305-1.305z" fill="currentColor"/></g>',26)}],["draw",{label:e.query("GET_LABEL_MARKUP_TOOL_DRAW"),icon:Mn('<g fill="currentColor"><path d="M17.86 5.71a2.425 2.425 0 013.43 3.43L9.715 20.714 5 22l1.286-4.715L17.86 5.71z"/></g>',26)}],["line",{label:e.query("GET_LABEL_MARKUP_TOOL_LINE"),icon:Mn('<g transform="translate(3 4.5)" fill-rule="nonzero" fill="currentColor" stroke="none"><path d="M15.414 9.414l-6.01 6.01a2 2 0 1 1-2.829-2.828L9.172 10H2a2 2 0 1 1 0-4h7.172L6.575 3.404A2 2 0 1 1 9.404.575l6.01 6.01c.362.363.586.863.586 1.415s-.224 1.052-.586 1.414z"/></g>',26)}],["text",{label:e.query("GET_LABEL_MARKUP_TOOL_TEXT"),icon:Mn('<g transform="translate(5 5)" fill="currentColor" fill-rule="evenodd"><path d="M10 4v11a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V4H1a1 1 0 0 1-1-1V1a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-5z"/></g>',26)}],["rect",{label:e.query("GET_LABEL_MARKUP_TOOL_RECT"),icon:Mn('<g fill="currentColor"><rect x="5" y="5" width="16" height="16" rx="2"/></g>',26)}],["ellipse",{label:e.query("GET_LABEL_MARKUP_TOOL_ELLIPSE"),icon:Mn('<g fill="currentColor"><circle cx="13" cy="13" r="9"/></g>',26)}]];e.ref.utils=v("fieldset"),e.ref.utils.className="doka--markup-utils",e.ref.utilsList=v("ul");var i="markup-utils-".concat($());e.ref.inputs=r.map(function(t){var n=t[0],r=t[1],o="doka--markup-tool-"+$(),a=v("li"),l=v("input");l.id=o,l.setAttribute("type","radio"),l.setAttribute("name",i),l.value=n;var c=v("label");return c.setAttribute("for",o),c.className="doka--button-tool",c.innerHTML=r.icon+"<span>"+r.label+"</span>",c.title=r.label,a.appendChild(l),a.appendChild(c),e.ref.utilsList.appendChild(a),l}),e.ref.utils.appendChild(e.ref.utilsList),e.ref.utilsList.addEventListener("change",function(t){e.dispatch("SET_MARKUP_UTIL",{value:t.target.value})}),e.query("GET_MARKUP_ALLOW_ADD_MARKUP")&&(e.ref.menu=e.appendChildView(e.createChildView(Ir("toolbar",["opacity"],{opacity:{type:"spring",mass:15,delay:50}}),{opacity:0,element:e.ref.utils}))),e.ref.tools=e.appendChildView(e.createChildView(Si,{opacity:0,onUpdate:function(t,n){e.dispatch("MARKUP_UPDATE",{style:t,value:n})}})),e.ref.menuItemsRequiredWidth=null},read:function(t){var e=t.root,n=t.props;if(n.hidden)e.ref.menuItemsRequiredWidth=null;else{var r=e.rect;if(0!==r.element.width&&0!==r.element.height){if(e.ref.menu&&null===e.ref.menuItemsRequiredWidth){var i=e.ref.menu.rect.element.width;e.ref.menuItemsRequiredWidth=0===i?null:i}var o=e.ref.menu&&e.ref.menu.rect,a=e.ref.tools.rect.element.height,l=o?o.element.height:a,c=!o||0===o.element.top,u=c?r.element.top+l:r.element.top,s=c?r.element.height-l:r.element.height-l-r.element.top;n.stagePosition={x:r.element.left+20,y:u,width:r.element.width-40,height:s-a}}}},shouldUpdateChildViews:function(t){var e=t.props,n=t.actions;return!e.hidden||e.hidden&&n&&n.length},write:k({SHOW_VIEW:function(t){var e=t.root,n=t.action,r=t.props;n.id===r.viewId?(r.hidden=!1,e.ref.isHiding=!1,e.ref.menu&&(e.ref.menu.opacity=1)):(e.ref.isHiding=!0,e.ref.menu&&(e.ref.menu.opacity=0),e.ref.tools.opacity=0,e.ref.tools.translateY=5)},MARKUP_SELECT:function(t){var e=t.root,n=t.action;e.ref.tools.opacity=n.id?1:0,e.ref.tools.translateY=n.id?0:5,e.ref.tools.element.dataset.active=n.id?"true":"false"},DID_SET_MARKUP_UTIL:function(t){var e=t.root,n=t.action;e.ref.inputs.forEach(function(t){t.checked=t.value===n.value}),"draw"===n.value&&(e.ref.tools.opacity=1,e.ref.tools.translateY=0,e.ref.tools.element.dataset.active="true")}},function(t){var e=t.root,n=t.props;e.ref.isHiding&&e.ref.menu&&0===e.ref.menu.opacity&&(e.ref.isHiding=!1,n.hidden=!0),n.hidden||(e.ref.menu.element.dataset.layout=e.ref.menuItemsRequiredWidth>e.rect.element.width?"compact":"spacious")})})},Pi=D({name:"view-stack",ignoreRect:!0,mixins:{apis:["offsetTop"]},create:function(t){var e=t.root;e.ref.activeView=null,e.ref.activeStagePosition=null,e.ref.shouldFocus=!1},write:k({SHOW_VIEW:function(t){var e=t.root,n=t.props,r=t.action,i=0===e.childViews.length,o=e.childViews.find(function(t){return t.viewId===r.id});o||(o=e.appendChildView(e.createChildView(Li[r.id],a({},n)))),e.ref.activeView=o,e.childViews.map(function(t){return t.element}).forEach(function(t){t.dataset.viewActive="false",t.removeAttribute("tabindex")});var l=e.ref.activeView.element;l.dataset.viewActive="true",l.setAttribute("tabindex",-1),e.ref.shouldFocus=!i},DID_PRESENT_IMAGE:function(t){var e=t.root;e.dispatch("CHANGE_VIEW",{id:e.query("GET_UTIL")||e.query("GET_UTILS")[0]})},DID_SET_UTILS:function(t){var e=t.root;e.dispatch("CHANGE_VIEW",{id:e.query("GET_UTIL")||e.query("GET_UTILS")[0]})}},function(t){var e=t.root,n=t.props,r=e.ref,i=r.activeView,o=r.previousStagePosition;if(i&&i.stagePosition&&(e.childViews.forEach(function(t){t.offsetTop=n.offsetTop,t.element.viewHidden!==t.hidden&&(t.element.viewHidden=t.hidden,t.element.dataset.viewHidden=t.hidden)}),function(t,e){return!t||!e||!Tt(t,e)}(i.stagePosition,o))){var a=i.stagePosition,l=a.x,c=a.y,u=a.width,s=a.height;if(0===u&&0===s)return;e.dispatch("DID_RESIZE_STAGE",{offset:{x:l,y:c},size:{width:u,height:s},animate:!0}),e.ref.previousStagePosition=i.stagePosition}}),didWriteView:function(t){var e=t.root;e.ref.shouldFocus&&(e.ref.activeView.element.focus({preventScroll:!0}),e.ref.shouldFocus=!1)}}),Gi=D({name:"content",ignoreRect:!0,mixins:{styles:["opacity"],animations:{opacity:{type:"tween",duration:250}}},create:function(t){var e=t.root,n=t.props;e.opacity=1,e.ref.viewStack=e.appendChildView(e.createChildView(Pi,{id:n.id})),e.ref.image=null},write:k({DID_LOAD_IMAGE:function(t){var e=t.root,n=t.props;e.ref.image=e.appendChildView(e.createChildView(Cr,{id:n.id}))}},function(t){var e=t.root,n=e.ref,r=n.image,i=n.viewStack;if(r){var o=e.rect.element.top;i.offsetTop=o,r.offsetTop=o}})}),Di=D({name:"utils",create:function(t){var e=t.root,n={crop:{title:e.query("GET_LABEL_BUTTON_UTIL_CROP"),icon:Mn('<g fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round" stroke="currentColor" stroke-width="2"><path d="M23 17H9a2 2 0 0 1-2-2v-5m0-3V1"/><path d="M1 7h14a2 2 0 0 1 2 2v7m0 4v3"/></g>')},filter:{title:e.query("GET_LABEL_BUTTON_UTIL_FILTER"),icon:Mn('<g fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18.347 9.907a6.5 6.5 0 1 0-1.872 3.306M3.26 11.574a6.5 6.5 0 1 0 2.815-1.417"/><path d="M10.15 17.897A6.503 6.503 0 0 0 16.5 23a6.5 6.5 0 1 0-6.183-8.51"/></g>')},color:{title:e.query("GET_LABEL_BUTTON_UTIL_COLOR"),icon:Mn('<g fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 1v5.5m0 3.503V23M12 1v10.5m0 3.5v8M20 1v15.5m0 3.5v3M2 7h4M10 12h4M18 17h4"/></g>')},markup:{title:e.query("GET_LABEL_BUTTON_UTIL_MARKUP"),icon:Mn('<g fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17.086 2.914a2.828 2.828 0 1 1 4 4l-14.5 14.5-5.5 1.5 1.5-5.5 14.5-14.5z"/></g>')},resize:{title:e.query("GET_LABEL_BUTTON_UTIL_RESIZE"),icon:Mn('<g fill="none" fill-rule="evenodd" stroke-width="2" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="12" width="10" height="10" rx="2"/><path d="M4 11.5V4a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-5.5"/><path d="M14 10l3.365-3.365M14 6h4v4" class="doka--icon-resize-arrow-ne"/><path d="M14 10l3.365-3.365M14 6v4h4" class="doka--icon-resize-arrow-sw"/></g>')}};e.ref.utils=Object.keys(n).map(function(t){return a({id:t},n[t])}),e.ref.utilMenuRequiredWidth=null},read:function(t){var e=t.root;if(null===e.ref.utilMenuRequiredWidth){var n=e.childViews.reduce(function(t,e){return t+e.rect.outer.width},0);e.ref.utilMenuRequiredWidth=0===n?null:n}},write:k({DID_SET_UTILS:function(t){var e=t.root,n=c(e.query("GET_UTILS"));e.childViews.forEach(function(t){return e.removeChildView(t)}),e.element.dataset.utilCount=n.length,1===n.length&&(n.length=0),n.forEach(function(t){var n=e.ref.utils.find(function(e){return e.id===t}),r=e.appendChildView(e.createChildView(Sn,{name:"tab",view:Sn,label:n.title,opacity:1,icon:n.icon,id:n.id,action:function(){return e.dispatch("CHANGE_VIEW",{id:n.id})}}));e.ref["util_button_".concat(n.id)]=r})},SHOW_VIEW:function(t){var e=t.root,n=t.action;e.childViews.forEach(function(t){t.element.dataset.active=t.id===n.id})}},function(t){var e=t.root,n=t.props,r=t.timestamp,i=e.query("GET_CROP",n.id,r);if(i){var o=i.cropStatus;e.ref.util_button_resize&&function(t,e){t.element.dataset.scaleDirection=null===e||e>1?"up":"down"}(e.ref.util_button_resize,o.image.width?o.image.width/o.crop.width:null),e.element.dataset.layout=e.ref.utilMenuRequiredWidth>e.rect.element.width?"compact":"spacious"}})}),ki=P()&&function(){try{var t={antialias:!1,alpha:!1},e=document.createElement("canvas");return!!window.WebGLRenderingContext&&(e.getContext("webgl",t)||e.getContext("experimental-webgl",t))}catch(t){return!1}}(),Ui=D({name:"container",create:function(t){var e=t.root,n=[{view:Sn,opacity:0,label:e.query("GET_LABEL_BUTTON_RESET"),didCreateView:function(t){return e.ref.btnReset=t},name:"app action-reset icon-only",icon:Mn('<g fill="currentColor" fill-rule="nonzero"><path d="M6.036 13.418L4.49 11.872A.938.938 0 1 0 3.163 13.2l2.21 2.209a.938.938 0 0 0 1.326 0l2.209-2.21a.938.938 0 0 0-1.327-1.326l-1.545 1.546zM12 10.216a1 1 0 0 1 2 0V13a1 1 0 0 1-2 0v-2.784z"/><path d="M15.707 14.293a1 1 0 0 1-1.414 1.414l-2-2a1 1 0 0 1 1.414-1.414l2 2z"/><path d="M8.084 19.312a1 1 0 0 1 1.23-1.577 6 6 0 1 0-2.185-3.488 1 1 0 0 1-1.956.412 8 8 0 1 1 2.912 4.653z"/></g>',26),action:function(){return e.dispatch("EDIT_RESET")}}];e.query("GET_ALLOW_BUTTON_CANCEL")&&n.unshift({view:Sn,label:e.query("GET_LABEL_BUTTON_CANCEL"),name:"app action-cancel icon-fallback",opacity:1,icon:Mn('<g fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"><path d="M18 6L6 18M6 6l12 12"/></g>'),didCreateView:function(t){e.ref.btnCancel=t},action:function(){e.dispatch("EDIT_CANCEL")}}),n.push({view:Di}),e.query("GET_ALLOW_BUTTON_CONFIRM")&&n.push({view:Sn,label:e.query("GET_LABEL_BUTTON_CONFIRM"),name:"app action-confirm icon-fallback",opacity:1,icon:Mn('<polyline fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" points="20 6 9 17 4 12"></polyline>'),didCreateView:function(t){e.ref.btnConfirm=t},action:function(){e.dispatch("EDIT_CONFIRM")}}),e.ref.menu=e.appendChildView(e.createChildView(Ir("menu"),{controls:n})),e.ref.menu.opacity=0,e.ref.status=e.appendChildView(e.createChildView(Vn)),e.ref.hasWebGL=ki,e.ref.hasWebGL?e.dispatch("AWAIT_IMAGE"):e.dispatch("MISSING_WEBGL"),e.ref.handleFocusOut=function(){var t=e.ref.status;"busy"===t.element.dataset.viewStatus&&t.element.focus()},e.ref.handleFocusIn=function(t){var n=e.ref,r=n.menu,i=n.content,o=t.target;if(!r.element.contains(o)&&i&&i.element.contains(o)){if(!Array.from(e.element.querySelectorAll("[data-view-active=false]")).reduce(function(t,e){return e.contains(o)&&(t=!0),t},!1))return;r.element.querySelector("button,input,[tabindex]").focus()}},e.element.addEventListener("focusin",e.ref.handleFocusIn),e.element.addEventListener("focusout",e.ref.handleFocusOut),e.ref.previousState=null},destroy:function(t){var e=t.root;e.element.removeEventListener("focusin",e.ref.handleFocusIn),e.element.removeEventListener("focusout",e.ref.handleFocusOut)},write:k({UNLOAD_IMAGE:function(t){var e=t.root;e.ref.content&&(e.ref.content.opacity=0,e.ref.menu.opacity=0)},DID_UNLOAD_IMAGE:function(t){var e=t.root;e.removeChildView(e.ref.content),e.ref.content=null},DID_LOAD_IMAGE:function(t){var e=t.root,n=t.props;e.ref.hasWebGL&&(e.ref.content=e.appendChildView(e.createChildView(Gi,{opacity:null,id:n.id})),e.ref.menu.opacity=1)},SHOW_VIEW:function(t){var e=t.root,n=t.action;e.element.dataset.limitOverflow="resize"===n.id}},function(t){var e=t.root,n=t.props,r=t.timestamp,i=e.query("GET_CROP",n.id,r);if(i){var o=i.cropStatus,l=o.props,c={crop:{center:{x:pt(l.center.x,5),y:pt(l.center.y,5)},rotation:pt(l.rotation,5),zoom:pt(l.zoom,5),aspectRatio:pt(l.aspectRatio,5),flip:{horizontal:l.flip.horizontal,vertical:l.flip.vertical},scaleToFit:l.scaleToFit,width:o.currentWidth,height:o.currentHeight}};Vi(e.ref.previousState,c)&&(e.dispatch("DID_UPDATE",{state:a({},c)}),e.ref.previousState=c);var u=e.ref,s=u.btnCancel,f=u.content,d=i.canReset;if(e.ref.btnReset.opacity=d?1:0,s&&e.query("GET_UTILS").length>1){var h=e.query("GET_ROOT_SIZE");s.opacity=d&&h.width<600?0:1}f&&0===f.opacity&&e.dispatch("DID_UNLOAD_IMAGE")}})}),Vi=function(t,e){if(!t)return!0;var n=t.crop,r=e.crop;return n.width!==r.width||n.height!==r.height||n.center.x!==r.center.x||n.center.y!==r.center.y||n.rotation!==r.rotation||n.scaleToFit!==r.scaleToFit||n.zoom!==r.zoom||n.aspectRatio!==r.aspectRatio||n.flip.horizontal!==r.flip.horizontal||n.flip.vertical!==r.flip.vertical},Bi=function(t){"gesturestart"!==t.type&&$r(t.target,function(t){return t.isScrollContainer})||t.preventDefault()},Ni=D({name:"editor",ignoreRect:!0,mixins:{styles:["opacity"],animations:{opacity:{type:"tween",duration:350}},apis:["markedForRemoval"]},create:function(t){var e=t.root,n=t.props;n.markedForRemoval=!1,ut()&&(e.element.addEventListener("touchmove",Bi,{passive:!1}),e.element.addEventListener("gesturestart",Bi)),e.ref.pointerPolyfill=function(t){var e={destroy:function(){}};if("onpointerdown"in window||t.pointersPolyfilled)return e;t.pointersPolyfilled=!0;var n=0,r=[],i=function(t,e,n){var r=new UIEvent(e.type,{view:window,bubbles:!n});Object.keys(e).forEach(function(t){Object.defineProperty(r,t,{value:e[t],writable:!1})}),t.dispatchEvent(r)},o=function(t,e,o){return Array.from(e.changedTouches).map(function(a){var l=r[a.identifier],c={type:t,pageX:a.pageX,pageY:a.pageY,pointerId:a.identifier,isPrimary:l?l.isPrimary:0===n,preventDefault:function(){return e.preventDefault()}};return i(a.target,c,o),c})},a=function(t){o("pointerdown",t).forEach(function(t){r[t.pointerId]=t,n++})},l=function(t){o("pointermove",t)},c=function(t){o("pointerup",t).forEach(function(t){delete r[t.pointerId],n--})},u=function(t,e,n){var r={type:t,pageX:e.pageX,pageY:e.pageY,pointerId:0,isPrimary:!0,preventDefault:function(){return e.preventDefault()}};return i(e.target,r,n),r},s=function(t){u("pointerdown",t)},f=function(t){u("pointermove",t)},d=function(t){u("pointerup",t)};return"ontouchstart"in window?(t.addEventListener("touchstart",a),t.addEventListener("touchmove",l),t.addEventListener("touchend",c)):"onmousedown"in window&&(t.addEventListener("mousedown",s),t.addEventListener("mousemove",f),t.addEventListener("mouseup",d)),e.destroy=function(){r.length=0,t.pointersPolyfilled=!1,t.removeEventListener("touchstart",a),t.removeEventListener("touchmove",l),t.removeEventListener("touchend",c),t.removeEventListener("mousedown",s),t.removeEventListener("mousemove",f),t.removeEventListener("mouseup",d)},e}("root"===e.query("GET_POINTER_EVENTS_POLYFILL_SCOPE")?e.element:document.documentElement),e.appendChildView(e.createChildView(Ui,a({},n)))},destroy:function(t){var e=t.root;e.ref.pointerPolyfill.destroy(),e.element.removeEventListener("touchmove",Bi,!0),e.element.removeEventListener("gesturestart",Bi)}}),zi=function(t){return t.ref.isFullscreen},Fi=function(t){return/fullscreen/.test(t.query("GET_STYLE_LAYOUT_MODE"))},Wi=function(t){return/fullscreen|preview/.test(t.query("GET_STYLE_LAYOUT_MODE"))},qi=function(t){return/modal/.test(t.query("GET_STYLE_LAYOUT_MODE"))},Hi=function(t){return t.query("GET_ALLOW_AUTO_CLOSE")},Yi=Wi,Zi=Wi,Xi=function(t){var e=t.ref,n=e.environment,r=e.isSingleUtil,i=e.canBeControlled;t.element.dataset.styleViewport=Ji(t.rect.element.width,t.rect.element.height)+" "+n.join(" ")+(r?" single-util":" multi-util")+(i?" flow-controls":" no-flow-controls")},Ki=function(t){var e=t.element,n=t.ref,r=n.handleFullscreenUpdate,i=n.handleEscapeKey;e.setAttribute("tabindex",-1),r(),t.ref.focusTrap=function(t){var e=function(e){if(9===e.keyCode){var n=Array.from(t.querySelectorAll("button,input,[tabindex]")).filter(function(t){return"hidden"!==t.style.visibility&&-1!==t.tabIndex}),r=n[0],i=n[n.length-1];e.shiftKey?document.activeElement===r&&(i.focus(),e.preventDefault()):document.activeElement===i&&(r.focus(),e.preventDefault())}};return t.addEventListener("keydown",e),{destroy:function(){t.removeEventListener("keydown",e)}}}(e),e.addEventListener("keydown",i),window.addEventListener("resize",r),window.innerWidth-document.documentElement.clientWidth>0&&document.body.classList.add("doka--parent"),document.body.appendChild(e);var o=document.querySelector("meta[name=viewport]");t.ref.defaultViewportContent=o?o.getAttribute("content"):null,o||((o=document.createElement("meta")).setAttribute("name","viewport"),document.head.appendChild(o)),o.setAttribute("content","width=device-width, height=device-height, initial-scale=1, maximum-scale=1, user-scalable=0"),t.opacity=1,t.element.contains(document.activeElement)||e.focus(),t.dispatch("INVALIDATE_VIEWPORT"),t.ref.isFullscreen=!0},ji=function(t){var e=t.element,n=t.ref,r=n.handleFullscreenUpdate,i=n.focusTrap,o=n.handleEscapeKey;e.removeAttribute("tabindex"),i.destroy(),e.removeEventListener("keydown",o),window.removeEventListener("resize",r),document.body.classList.remove("doka--parent");var a=document.querySelector("meta[name=viewport]");t.ref.defaultViewportContent?(a.setAttribute("content",t.ref.defaultViewportContent),t.ref.defaultViewportContent=null):a.parentNode.removeChild(a),t.ref.isFullscreen=!1},Qi=D({name:"root",ignoreRect:!0,mixins:{styles:["opacity"],animations:{opacity:{type:"tween",duration:350}}},create:function(t){var e=t.root,n=t.props;e.element.id=e.query("GET_ID")||"doka-".concat(n.id);var r=e.query("GET_CLASS_NAME");r&&e.element.classList.add(r),e.ref.environment=[],e.ref.shouldBeDestroyed=!1,e.ref.isClosing=!1,e.ref.isClosed=!1,e.ref.isFullscreen=!1,e.query("GET_ALLOW_DROP_FILES")&&(e.ref.catcher=function(t){var e,n={browseEnabled:!1},r=function(){e.files.length&&i.fire("drop",Array.from(e.files))},i=a({},tt(),{enableBrowse:function(){n.browseEnabled||((e=document.createElement("input")).style.display="none",e.setAttribute("type","file"),e.addEventListener("change",r),t.appendChild(e),t.addEventListener("click",o),n.browseEnabled=!0)},disableBrowse:function(){n.browseEnabled&&(e.removeEventListener("change",r),e.parentNode.removeChild(e),t.removeEventListener("click",o),n.browseEnabled=!1)},destroy:function(){t.removeEventListener("dragover",l),t.removeEventListener("drop",c),t.removeEventListener("click",o),e&&e.removeEventListener("change",r)}}),o=function(){return e.click()},l=function(t){return t.preventDefault()},c=function(t){t.preventDefault();var e=Array.from(t.dataTransfer.items||t.dataTransfer.files).map(function(t){return t.getAsFile&&"file"===t.kind?t.getAsFile():t});i.fire("drop",e)};return t.addEventListener("dragover",l),t.addEventListener("drop",c),i}(e.element),e.ref.catcher.on("drop",function(t){t.forEach(function(t){e.dispatch("REQUEST_LOAD_IMAGE",{source:t})})})),e.ref.touchDetector=function(){function t(){e.fire("touch-detected"),window.removeEventListener("touchstart",t,!1)}var e=a({},tt(),{destroy:function(){window.removeEventListener("touchstart",t,!1)}});return window.addEventListener("touchstart",t,!1),e}(),e.ref.touchDetector.onOnce("touch-detected",function(){e.ref.environment.push("touch")}),e.ref.editor=e.appendChildView(e.createChildView(Ni,{id:n.id})),e.query("GET_STYLES").filter(function(t){return!V(t.value)}).map(function(t){var n=t.name,r=t.value;e.element.dataset[n]=r}),e.ref.updateViewport=function(){e.dispatch("INVALIDATE_VIEWPORT")},window.addEventListener("resize",e.ref.updateViewport),window.addEventListener("scroll",e.ref.updateViewport),e.ref.isSingleUtil=1===e.query("GET_UTILS").length,e.ref.canBeControlled=e.query("GET_ALLOW_BUTTON_CONFIRM")||e.query("GET_ALLOW_BUTTON_CANCEL"),Xi(e);var i=document.createElement("div");i.style.cssText="position:fixed;height:100vh;top:0;",e.ref.measure=i,document.body.appendChild(i),e.ref.handleEscapeKey=function(t){27===t.keyCode&&e.dispatch("EDIT_CANCEL")},e.ref.initialScreenMeasureHeight=null,e.ref.handleFullscreenUpdate=function(){e.element.dataset.styleFullscreen=window.innerHeight===e.ref.initialScreenMeasureHeight},e.ref.clientRect={left:0,top:0},qi(e)&&(e.ref.handleModalTap=function(t){t.target===e.element&&e.dispatch("EDIT_CANCEL")},e.element.addEventListener("pointerdown",e.ref.handleModalTap))},read:function(t){var e=t.root,n=e.ref.measure;n&&(e.ref.initialScreenMeasureHeight=n.offsetHeight,n.parentNode.removeChild(n),e.ref.measure=null),e.ref.clientRect=e.element.getBoundingClientRect(),e.ref.clientRect.leftScroll=e.ref.clientRect.left+(window.scrollX||window.pageXOffset),e.ref.clientRect.topScroll=e.ref.clientRect.top+(window.scrollY||window.pageYOffset)},write:k({ENTER_FULLSCREEN:function(t){var e=t.root;Ki(e)},EXIT_FULLSCREEN:function(t){var e=t.root;ji(e)},SHOW_VIEW:function(t){var e=t.root,n=t.action;e.element.dataset.view=n.id},DID_SET_STYLE_LAYOUT_MODE:function(t){var e=t.root,n=t.action;e.element.dataset.styleLayoutMode=n.value||"none",/fullscreen/.test(n.value)&&!/fullscreen/.test(n.prevValue)&&e.dispatch("ENTER_FULLSCREEN")},AWAITING_IMAGE:function(t){var e=t.root;e.ref.catcher&&e.query("GET_ALLOW_BROWSE_FILES")&&e.ref.catcher.enableBrowse()},DID_REQUEST_LOAD_IMAGE:function(t){var e=t.root;if(e.ref.catcher&&e.query("GET_ALLOW_BROWSE_FILES")&&e.ref.catcher.disableBrowse(),0===e.opacity&&(e.opacity=1),e.ref.isClosing=!1,e.ref.isClosed=!1,!Fi(e)||zi(e)){var n=e.query("GET_STYLE_LAYOUT_MODE");null!==n&&"modal"!==n||e.element.parentNode||e.dispatch("SET_STYLE_LAYOUT_MODE",{value:("fullscreen "+(n||"")).trim()})}else e.dispatch("ENTER_FULLSCREEN")},DID_CANCEL:function(t){var e=t.root;Yi(e)&&Hi(e)&&e.dispatch("EDIT_CLOSE")},DID_CONFIRM:function(t){var e=t.root;Yi(e)&&Hi(e)&&e.dispatch("EDIT_CLOSE")},EDIT_CLOSE:function(t){var e=t.root;Zi(e)&&(e.opacity=e.opacity||1,e.opacity=0,e.ref.isClosed=!1,e.ref.isClosing=!0,e.query("GET_ALLOW_AUTO_DESTROY")&&(e.ref.shouldBeDestroyed=!0),zi(e)&&e.dispatch("EXIT_FULLSCREEN"))},DID_SET_UTILS:function(t){var e=t.root;e.ref.isSingleUtil=1===e.query("GET_UTILS").length}},function(t){var e=t.root;Xi(e);var n=e.query("GET_ROOT"),r=e.rect.element;n.width===r.width&&n.height===r.height&&n.y===e.ref.clientRect.top&&n.topScroll===e.ref.clientRect.topScroll||e.dispatch("UPDATE_ROOT_RECT",{rect:{x:e.ref.clientRect.left,y:e.ref.clientRect.top,left:e.ref.editor.rect.element.left,top:e.ref.editor.rect.element.top,leftScroll:e.ref.clientRect.leftScroll,topScroll:e.ref.clientRect.topScroll,width:e.rect.element.width,height:e.rect.element.height}})}),didWriteView:function(t){var e=t.root,n=e.ref,r=n.isClosed,i=n.isClosing,o=n.shouldBeDestroyed;!r&&i&&0===e.opacity&&(e.dispatch("DID_CLOSE"),e.ref.isClosed=!0,e.ref.isClosing=!1,Fi(e)&&e.element.parentNode&&document.body.removeChild(e.element),o&&e.dispatch("EDIT_DESTROY"))},destroy:function(t){var e=t.root;zi(e)&&ji(e),qi(e)&&e.element.removeEventListener("pointerdown",e.ref.handleModalTap),Fi(e)&&e.element.parentNode&&document.body.removeChild(e.element),window.removeEventListener("resize",e.ref.updateViewport),e.ref.touchDetector.destroy(),e.ref.catcher&&e.ref.catcher.destroy()}}),Ji=function(t,e){var n="";return 0===t&&0===e?"detached":(n+=e>t?"portrait":"landscape",(n+=t<=600?" x-cramped":t<=1e3?" x-comfortable":" x-spacious").trim())},$i=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=st(),n=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=a({},t),i=[],o=[],l=function(t,e,n){n?o.push({type:t,data:e}):(f[t]&&f[t](e),i.push({type:t,data:e}))},c=function(t){for(var e,n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return s[t]?(e=s)[t].apply(e,r):null},u={getState:function(){return a({},r)},processActionQueue:function(){var t=[].concat(i);return i.length=0,t},processDispatchQueue:function(){var t=[].concat(o);o.length=0,t.forEach(function(t){var e=t.type,n=t.data;l(e,n)})},dispatch:l,query:c},s={};e.forEach(function(t){s=a({},t(r),s)});var f={};return n.forEach(function(t){f=a({},t(l,c,r),f)}),u}(function(t){var e={noImageTimeout:null,options:j(t)};return Q(e),e}(e),[$t,function(t){return function(e){var n={};return d(t,function(t){n["GET_".concat(J(t,"_").toUpperCase())]=function(){return e.options[t]}}),n}}(e)],[On,function(t){return function(e,n,r){var i={};return d(t,function(t){var n=J(t,"_").toUpperCase();i["SET_".concat(n)]=function(i){var o;try{o=r.options[t],r.options[t]=i.value}catch(t){}e("DID_SET_".concat(n),{value:r.options[t],prevValue:o})}}),i}}(e)]);n.dispatch("SET_OPTIONS",{options:t});var r=function(){document.hidden||n.dispatch("KICK")};document.addEventListener("visibilitychange",r);var i=$();n.dispatch("SET_UID",{id:i});var o=null,l=Qi(n,{id:i}),c=!1,p={_read:function(){c||l._read()},_write:function(t){var e=n.processActionQueue().filter(function(t){return!/^SET_/.test(t.type)});c&&!e.length||(v(e),(c=l._write(t,e))&&n.processDispatchQueue(),e.find(function(t){return"EDIT_DESTROY"===t.type})&&y())}},g=function(t){return function(e){var n={type:t};return e?(e.hasOwnProperty("error")&&(n.error=f(e.error)?a({},e.error):e.error||null),e.hasOwnProperty("output")&&(n.output=e.output),e.hasOwnProperty("image")&&(n.image=e.image),e.hasOwnProperty("source")&&(n.source=e.source),e.hasOwnProperty("state")&&(n.state=e.state),n):n}},m={DID_CONFIRM:g("confirm"),DID_CANCEL:g("cancel"),DID_REQUEST_LOAD_IMAGE:g("loadstart"),DID_LOAD_IMAGE:g("load"),DID_LOAD_IMAGE_ERROR:g("loaderror"),DID_UPDATE:g("update"),DID_CLOSE:g("close"),DID_DESTROY:g("destroy"),DID_INIT:g("init")},v=function(t){t.length&&t.forEach(function(t){if(m[t.type]){var e=m[t.type];(Array.isArray(e)?e:[e]).forEach(function(e){setTimeout(function(){!function(t){var e=a({doka:E},t);delete e.type,l&&l.element.dispatchEvent(new CustomEvent("Doka:".concat(t.type),{detail:e,bubbles:!0,cancelable:!0,composed:!0}));var r=[];t.hasOwnProperty("error")&&r.push(t.error);var i=["type","error"];Object.keys(t).filter(function(t){return!i.includes(t)}).forEach(function(e){return r.push(t[e])}),E.fire.apply(E,[t.type].concat(r));var o=n.query("GET_ON".concat(t.type.toUpperCase()));o&&o.apply(void 0,r)}(e(t.data))},0)})}})},y=function(){E.fire("destroy",l.element),document.removeEventListener("visibilitychange",r),l._destroy(),n.dispatch("DID_DESTROY")},E=a({},tt(),p,function(t,e){var n={};return d(e,function(r){var i=F(e[r])?e[r]:r;n[r]={get:function(){return t.getState().options[i]},set:function(e){t.dispatch("SET_".concat(J(i,"_").toUpperCase()),{value:e})}}}),n}(n,e),{setOptions:function(t){return n.dispatch("SET_OPTIONS",{options:t})},setData:function(t){n.dispatch("SET_DATA",t)},getData:function(t){return new Promise(function(e,r){n.dispatch("GET_DATA",a({},t,{success:e,failure:r}))})},open:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise(function(r,i){t&&n.dispatch("REQUEST_LOAD_IMAGE",{source:t,options:e,success:r,failure:i,resolveOnConfirm:!!e&&e.resolveOnConfirm})})},edit:function(t,e){return E.open(t,a({},e,{resolveOnConfirm:!0}))},save:function(t){return new Promise(function(e,r){n.dispatch("GET_DATA",a({},t,{success:e,failure:r}))})},clear:function(){return n.dispatch("REQUEST_REMOVE_IMAGE")},close:function(){return n.dispatch("EDIT_CLOSE")},destroy:y,insertBefore:function(t){u(l.element,t)},insertAfter:function(t){s(l.element,t)},appendTo:function(t){t.appendChild(l.element)},replaceElement:function(t){u(l.element,t),t.parentNode.removeChild(t),o=t},restoreElement:function(){o&&(s(o,l.element),l.element.parentNode.removeChild(l.element),o=null)},isAttachedTo:function(t){return!!l&&(l.element===t||o===t)},element:{get:function(){return l?l.element:null}}});return n.dispatch("DID_INIT"),h(E)},to=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=st(),n={};return d(e,function(t,e){F(e)||(n[t]=e[0])}),$i(a({},n,t))},eo=function(t){return function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"-";return t.replace(new RegExp("".concat(e,"."),"g"),function(t){return t.charAt(1).toUpperCase()})}(t.replace(/^data-/,""))},no=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=[];d(t.attributes,function(e){return n.push(t.attributes[e])});var r=n.filter(function(t){return t.name}).reduce(function(e,n){var r=p(t,n.name);return e[eo(n.name)]=r===n.name||r,e},{});return function t(e,n){d(n,function(n,r){d(e,function(t,i){var o=new RegExp(n);if(o.test(t)&&(delete e[t],!1!==r))if(F(r))e[r]=i;else{var a=r.group;f(r)&&!e[a]&&(e[a]={}),e[a][function(t){return t.charAt(0).toLowerCase()+t.slice(1)}(t.replace(o,""))]=i}}),r.mapping&&t(e[r.group],r.mapping)})}(r,e),r},ro=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return function(t){return t instanceof HTMLElement}(e[0])?function(t){var e=a({},arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}),n=no(t,{"^class$":"className"});Object.keys(n).forEach(function(t){f(n[t])?(f(e[t])||(e[t]={}),Object.assign(e[t],n[t])):e[t]=n[t]}),"CANVAS"!==t.nodeName&&"IMG"!==t.nodeName||(e.src=t.dataset.dokaSrc?t.dataset.dokaSrc:t);var r=to(e);return r.replaceElement(t),r}.apply(void 0,e):to.apply(void 0,c(e.filter(function(t){return t})))},io=["fire","_read","_write"],oo=function(t){var e={};return function(t,e,n){Object.getOwnPropertyNames(t).filter(function(t){return!n.includes(t)}).forEach(function(n){return Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})}(t,e,io),e},ao=function(){var t=P()&&!("[object OperaMini]"===Object.prototype.toString.call(window.operamini))&&"visibilityState"in document&&"Promise"in window&&"slice"in Blob.prototype&&"URL"in window&&"createObjectURL"in window.URL&&"performance"in window;return function(){return t}}(),lo={apps:[]},co=function(){},uo={},so=co,fo=co,ho=co,po=co,go=co,mo=co;if(ao()){!function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:60,r="__framePainter";if(window[r])return window[r].readers.push(t),void window[r].writers.push(e);window[r]={readers:[t],writers:[e]};var i=window[r],o=1e3/n,a=null,l=null,c=null,u=null,s=function(){document.hidden?(c=function(){return window.setTimeout(function(){return f(performance.now())},o)},u=function(){return window.clearTimeout(l)}):(c=function(){return window.requestAnimationFrame(f)},u=function(){return window.cancelAnimationFrame(l)})};document.addEventListener("visibilitychange",function(){u&&u(),s(),f(performance.now())});var f=function t(e){l=c(t),a||(a=e);var n=e-a;n<=o||(a=e-n%o,i.readers.forEach(function(t){return t()}),i.writers.forEach(function(t){return t(e)}))};s(),f(performance.now())}(function(){lo.apps.forEach(function(t){return t._read()})},function(t){lo.apps.forEach(function(e){return e._write(t)})});var vo=function t(){document.dispatchEvent(new CustomEvent("doka:loaded",{detail:{supported:ao,create:so,destroy:fo,parse:ho,find:po,setOptions:mo}})),document.removeEventListener("DOMContentLoaded",t)};"loading"!==document.readyState?setTimeout(function(){return vo()},0):document.addEventListener("DOMContentLoaded",vo);e.OptionTypes=uo={},d(st(),function(t,e){uo[t]=e[1]}),e.create=so=function(){var t=ro.apply(void 0,arguments);return t.on("destroy",fo),lo.apps.push(t),oo(t)},e.destroy=fo=function(t){var e=lo.apps.findIndex(function(e){return e.isAttachedTo(t)});return e>=0&&(lo.apps.splice(e,1)[0].restoreElement(),!0)},e.parse=ho=function(t){return Array.from(t.querySelectorAll(".".concat("doka"))).filter(function(t){return!lo.apps.find(function(e){return e.isAttachedTo(t)})}).map(function(t){return so(t)})},e.find=po=function(t){var e=lo.apps.find(function(e){return e.isAttachedTo(t)});return e?oo(e):null},e.getOptions=go=function(){var t={};return d(st(),function(e,n){t[e]=n[0]}),t},e.setOptions=mo=function(t){return f(t)&&(lo.apps.forEach(function(e){e.setOptions(t)}),function(t){d(t,function(t,e){dt[t]&&ft(t,e)})}(t)),go()}}e.supported=ao,e.OptionTypes=uo,e.create=so,e.destroy=fo,e.parse=ho,e.find=po,e.getOptions=go,e.setOptions=mo}]);