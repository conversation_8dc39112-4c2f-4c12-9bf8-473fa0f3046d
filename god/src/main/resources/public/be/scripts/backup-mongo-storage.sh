#!/bin/bash

## start me up
echo ... this is the beginning of the backup procedure
START_TIME=$SECONDS

## Database name parameter
DB_NAME="{{projectName}}"

## ---------------
## MONGODB SECTION
## ---------------

## assure to work on correct folder
if [ ! -d "/backup/mongodb/$DB_NAME" ]; then
  echo creating main mongodb backup folder in /backup/mongodb/$DB_NAME
  mkdir -p /backup/mongodb/$DB_NAME
fi

echo switch to /backup/mongodb/$DB_NAME folder
cd /backup/mongodb/$DB_NAME

## suffix is: day of week - hour (thus limiting backups to one per hour for each day)
echo grabbing suffix
SUFFIX=$(date +%u-%H)

## prepare temporary file
echo assign name to temporary file
TMPFILE="$DB_NAME-mongodb-$SUFFIX.archive.gz"

if [ -e $TMPFILE ]; then
  echo removing pre-existing temporary file $TMPFILE
  rm -f $TMPFILE
fi

## backup mongodb
/usr/bin/mongodump --gzip --archive=$TMPFILE --db $DB_NAME

## check backup
if [ ! -e $TMPFILE ]; then
  echo error: cannot find backup file
  exit 1
fi

## ftp upload to dominiofaidate
curl -T $TMPFILE "ftp://{{serverIp}}/backup/mongodb/$DB_NAME/" --user {{serverUsername}}:{{serverPassword}} --ftp-create-dirs

## save backup locally
echo "Saving MongoDB backup locally"
LOCAL_MONGODB_BACKUP_DIR="/backup/mongodb/$DB_NAME/"
if [ ! -d "$LOCAL_MONGODB_BACKUP_DIR" ]; then
  echo "Creating local MongoDB backup directory: $LOCAL_MONGODB_BACKUP_DIR"
  mkdir -p "$LOCAL_MONGODB_BACKUP_DIR"
fi
cp $TMPFILE "$LOCAL_MONGODB_BACKUP_DIR/"
echo "MongoDB backup saved locally in $LOCAL_MONGODB_BACKUP_DIR/$TMPFILE"

## ---------------
## STORAGE SECTION
## ---------------

## storage folder
BACKUP="/opt/$DB_NAME/storage"

if [ -d $BACKUP ]; then

    ## assure to work on correct folder
    if [ ! -d "/backup/storage/$DB_NAME" ]; then
      echo creating main storage backup folder in /backup/storage/$DB_NAME
      mkdir -p /backup/storage/$DB_NAME
    fi

    echo switch to /backup/storage/$DB_NAME folder
    cd /backup/storage/$DB_NAME

    ## prepare temporary file
    echo assign name to temporary file
    TMPFILE="$DB_NAME-storage-$SUFFIX.archive.gz"

    if [ -e $TMPFILE ]; then
      echo removing pre-existing temporary file $TMPFILE
      rm -f $TMPFILE
    fi

    ## gzip storage
    echo "gzip folder $BACKUP into archive $TMPFILE"
    tar -zcvf $TMPFILE $BACKUP/ 
    if [ $? -ne 0 ]; then
        echo error: cannot gzip files into storage folder
        exit 1
    fi

    ## check backup
    if [ ! -e $TMPFILE ]; then
        echo error: cannot find backup file
        exit 1
    fi

    ## ftp upload to dominiofaidate
    curl -T $TMPFILE "ftp://{{serverIp}}/backup/storage/$DB_NAME/" --user {{serverUsername}}:{{serverPassword}} --ftp-create-dirs

    ## save backup locally
    echo "Saving storage backup locally"
    LOCAL_STORAGE_BACKUP_DIR="/opt/$DB_NAME/backup/storage"
    if [ ! -d "$LOCAL_STORAGE_BACKUP_DIR" ]; then
      echo "Creating local storage backup directory: $LOCAL_STORAGE_BACKUP_DIR"
      mkdir -p "$LOCAL_STORAGE_BACKUP_DIR"
    fi
    cp $TMPFILE "$LOCAL_STORAGE_BACKUP_DIR/"
    echo "Storage backup saved locally in $LOCAL_STORAGE_BACKUP_DIR/$TMPFILE"
fi

## let me down
echo success!!!
ELAPSED_TIME=$(($SECONDS - $START_TIME))
echo done in $ELAPSED_TIME seconds