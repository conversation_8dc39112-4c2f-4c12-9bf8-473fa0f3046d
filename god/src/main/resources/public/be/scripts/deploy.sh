#!/bin/bash

# Funzione per mostrare la sintassi
function mostra_sintassi() {
    echo
    echo "--war        deploy war"
    echo "--static     deploy static"
    echo "--nominify   non minificare css, js e html"
    echo
}

# Inizializzazione variabili
deploywar=false
deploystatic=false
nominify=false
showsyntax=false

# Parsing della riga di comando
for arg in "$@"
do
    case $arg in
        --war)
            deploywar=true
            ;;
        --static)
            deploystatic=true
            ;;
        --nominify)
            nominify=true
            ;;
        --help)
            showsyntax=true
            ;;
        *)
            echo "Opzione non valida: $arg"
            mostra_sintassi
            exit 1
            ;;
    esac
done

# Mostra sintassi se nessuna opzione valida è stata fornita
if [[ "$deploywar" = "false" && "$deploystatic" = "false" && "$showsyntax" = "false" ]]; then
    showsyntax=true
fi

# Mostra la sintassi e esce se richiesto
if $showsyntax; then
    mostra_sintassi
    exit 0
fi

# Funzione per il deployment del WAR
function deploy_war() {
    echo "... inizio della procedura di deployment"
    START_TIME=$SECONDS

    if [ ! -d "/deploy/{{projectName}}" ]; then
        echo "creazione della cartella principale di deploy in /deploy/{{projectName}}"
        mkdir -p /deploy/{{projectName}}
    fi

    cd /deploy/{{projectName}} || exit

    NOW=$(date +%Y%m%d-%H%M%S)
    TMPFOLDER="tmp-$NOW"

    if [ -d "$TMPFOLDER" ]; then
        echo "rimozione della cartella temporanea pre-esistente $TMPFOLDER"
        rm -rv "$TMPFOLDER"
    fi

    mkdir "$TMPFOLDER"
    cd "$TMPFOLDER" || exit

    echo "clonazione del repository"
    <NAME_EMAIL>:{{gitName}}/{{projectName}}.git

    if [ ! -d "{{projectName}}/{{projectName}}" ]; then
        echo "errore: impossibile trovare la cartella {{projectName}}/{{projectName}}"
        exit 1
    fi

    cd {{projectName}}/{{projectName}} || exit

	# Minify CSS
    if [[ "$nominify" = "false" ]]; then
        if [ -d src/main/resources/public ]; then
            shopt -s globstar
			for f in src/main/resources/public/fe/*.css src/main/resources/public/fe/**/*.css
			do
                if [[ -f $f && $f != *".min"* ]]; then
                    echo "Minificazione CSS $f"
                    postcss $f --use cssnano --replace
                    if [ $? -ne 0 ]; then
                        echo "Errore: impossibile minificare il CSS su $f"
                        exit 1
                    fi
                fi
            done
        fi
    fi

    # Minify JavaScript
    if [[ "$nominify" = "false" ]]; then
        if [ -d src/main/resources/public ]; then
            shopt -s globstar
            for f in src/main/resources/public/fe/*.js src/main/resources/public/fe/**/*.js
			do
                if [[ -f $f && $f != *".min"* ]]; then
                    echo "Minificazione JS $f"
                    terser $f --compress --mangle --output $f
                    if [ $? -ne 0 ]; then
                        echo "Errore: impossibile minificare il JS su $f"
                        exit 1
                    fi
                fi
            done
        fi
    fi

    # Minify HTML
    if [[ "$nominify" = "false" ]]; then
        if [ -d src/main/webapp ]; then
            echo "Minificazione HTML"
            html-minifier-terser --input-dir src/main/webapp/fe --output-dir src/main/webapp/fe --file-ext html -c /opt/script/html-minifier.conf
            if [ $? -ne 0 ]; then
                echo "Errore: impossibile ottimizzare l'HTML"
                exit 1
            fi
        fi
    fi

    echo "costruzione del target"
    mvn package

    if [ ! -e "target/{{projectName}}.war" ]; then
        echo "errore: impossibile trovare il file target/{{projectName}}.war"
        exit 1
    fi

    DEPLOYFOLDER="deploy-$NOW"

    if [ -d "$DEPLOYFOLDER" ]; then
        echo "errore: la cartella di deploy esiste già"
        exit 1
    fi

    mkdir "/deploy/{{projectName}}/$DEPLOYFOLDER"
    cp target/{{projectName}}.war "/deploy/{{projectName}}/$DEPLOYFOLDER"

    echo "undeploy di {{projectName}}.war"
    curl http://{{tomcatUsername}}:{{tomcatPassword}}@localhost:8080/manager/text/undeploy?path=/{{projectName}}

    echo "deploy di {{projectName}}.war"
    cp target/{{projectName}}.war /opt/tomcat/webapps

    cd /deploy/{{projectName}} || exit
    rm -rv "$TMPFOLDER"

    echo "successo!!!"
    ELAPSED_TIME=$(($SECONDS - $START_TIME))
    echo "fatto in $ELAPSED_TIME secondi"
}

# Funzione per il deployment statico
function deploy_static() {
    echo "... inizio della procedura di deployment statico"
    START_TIME=$SECONDS

    if [ ! -d "/deploy/{{projectName}}-static" ]; then
        echo "creazione della cartella principale di deploy in /deploy/{{projectName}}-static"
        mkdir -p /deploy/{{projectName}}-static
    fi

    cd /deploy/{{projectName}}-static || exit

    NOW=$(date +%Y%m%d-%H%M%S)
    TMPFOLDER="tmp-$NOW"

    if [ -d "$TMPFOLDER" ]; then
        echo "rimozione della cartella temporanea pre-esistente $TMPFOLDER"
        rm -rv "$TMPFOLDER"
    fi

    mkdir "$TMPFOLDER"
    cd "$TMPFOLDER" || exit

    echo "clonazione del repository"
    <NAME_EMAIL>:{{gitName}}/{{projectName}}.git

    if [ ! -d "{{projectName}}/static" ]; then
        echo "errore: impossibile trovare la cartella {{projectName}}/static"
        exit 1
    fi

    DEPLOYFOLDER="deploy-$NOW"

    if [ -d "$DEPLOYFOLDER" ]; then
        echo "errore: la cartella di deploy esiste già"
        exit 1
    fi

    mkdir "/deploy/{{projectName}}-static/$DEPLOYFOLDER"
    cp -r ./{{projectName}}/static "/deploy/{{projectName}}-static/$DEPLOYFOLDER"

    # Creazione delle cartelle necessarie in /var/www/ con permessi 777
    if [ ! -d "/var/www/{{projectName}}" ]; then
        echo "creazione della cartella principale del progetto in /var/www/{{projectName}}"
        mkdir -p /var/www/{{projectName}}
        chmod 777 /var/www/{{projectName}}
        echo "permessi 777 assegnati a /var/www/{{projectName}}"
    fi

    if [ ! -d "/var/www/{{projectName}}/static" ]; then
        echo "creazione della cartella static in /var/www/{{projectName}}/static"
        mkdir -p /var/www/{{projectName}}/static
        chmod 777 /var/www/{{projectName}}/static
        echo "permessi 777 assegnati a /var/www/{{projectName}}/static"
    else
        echo "pulizia della cartella statica in /var/www/{{projectName}}/static"
        rm -rv /var/www/{{projectName}}/static
        mkdir -p /var/www/{{projectName}}/static
        chmod 777 /var/www/{{projectName}}/static
        echo "permessi 777 assegnati a /var/www/{{projectName}}/static"
    fi

    echo "copia delle risorse statiche nella cartella di destinazione /var/www/{{projectName}}/static"
    cp -r ./{{projectName}}/static /var/www/{{projectName}}

    cd /deploy/{{projectName}}-static || exit
    rm -rv "$TMPFOLDER"

    echo "successo!!!"
    ELAPSED_TIME=$(($SECONDS - $START_TIME))
    echo "fatto in $ELAPSED_TIME secondi"
}

# Esecuzione delle funzioni di deploy
if $deploywar; then
    deploy_war
fi

if $deploystatic; then
    deploy_static
fi