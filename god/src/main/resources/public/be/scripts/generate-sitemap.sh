#!/bin/bash


## command line parsing
generate=false
showsyntax=false

if [[ $1 == --site=* ]] ; then site=${1#*=} ; fi
if [[ $2 == --site=* ]] ; then site=${1#*=} ; fi

if [[ $1 == --folder=* ]] ; then folder=${2#*=} ; fi
if [[ $2 == --folder=* ]] ; then folder=${2#*=} ; fi

if [ ! -z "$site" ] ; then generate=true ; fi

if [[ $1 = "--help" ]] ; then showsyntax=true ; fi
if [[ $2 = "--help" ]] ; then showsyntax=true ; fi

if [ -z "$site" ] ; then showsyntax=true ; fi


## ############################################################
## ############################################################
## 
## start of "sitemap generation"
## 

if $generate; then


	## start me up
	echo ... this is the beginning of the generate procedure
	echo time: $(date -Iseconds)
	START_TIME=$SECONDS


	## domain
	domain=${site%:*}
	
	## check domain
	echo checking domain for $site
	if [[ ! $site =~ "." ]]; then
		echo error: cannot find domain before dot for site $site
		exit 1
	fi
	if [[ $site == *"www"* ]]; then
		echo error: you MUST specify a naked site, i.e. siteria.it
		exit 1
	fi	
	if [ -z "$domain" ]; then
		echo error: cannot find domain for site $site
		exit 1
	fi

	
	## folder
	if [ -z "$folder" ] ; then folder=$site ; fi
	foldername=${folder%:*}
	if [ -z "$foldername" ]; then
		echo error: cannot find foldername for site $site
		exit 1
	fi
	
	
	## fully qualified folder
	fld="/var/www/$foldername/static"

	
	
	## check folder
	echo checking static folder $fld
	if [ ! -d "$fld" ]; then
	  echo missing static folder $fld
	  ##exit 1
	fi
	
	
	## check tool
	echo checking tool sitemap-generator-cli
	nodejs /usr/lib/node_modules/sitemap-generator-cli -V
	if [ $? -ne 0 ]; then
		echo error: cannot find tool sitemap-generator-cli
		exit 1
	fi


	## now
	echo grabbing date and time
	NOW=$(date +%Y%m%d-%H%M%S)


	## prepare temporary sitemap
	echo assign name to temporary sitemap
	TMPSITEMAP="/var/www/$foldername/static/sitemap-$NOW.xml"
	PRDSITEMAP="/var/www/$foldername/static/sitemap.xml"
	
	
	## generate temporary sitemap
	echo generating $TMPSITEMAP
	echo for site https://www.$site
	nodejs /usr/lib/node_modules/sitemap-generator-cli --verbose -q -v -l -p "1.0,0.8,0.6,0.4" -f $TMPSITEMAP https://www.$site
	if [ $? -ne 0 ]; then
		echo error: cannot generate sitemap
		exit 1
	fi

	
	## check temporary sitemap
	echo checking temporary sitemap presence $TMPSITEMAP
	if [ ! -e "$TMPSITEMAP" ]; then
	  echo error: cannot find temporary sitemap $TMPSITEMAP
	  exit 1
	fi
	
	
	## copy temporary sitemap to production
	echo coping temporary sitemap $TMPSITEMAP to production sitemap $PRDSITEMAP
	cp $TMPSITEMAP $PRDSITEMAP
	
	
	## removing temporary sitemap
	rm $TMPSITEMAP
	
	
	## let me down
	echo success!!!
	ELAPSED_TIME=$(($SECONDS - $START_TIME))
	echo done in $ELAPSED_TIME seconds

fi

## 
## end of "sitemap generation"
## 
## ############################################################
## ############################################################




## ############################################################
## ############################################################
## 
## start of "show syntax"
## 

if $showsyntax; then

	## inform user on operation
	echo Usage: generate-sitemap [OPTION]
	echo Procedure for generation of sitemap.xml to static folder
	echo
	echo --site=name    naked sitename: no schema, no query, no www, no final slash, i.e. siteria.it
	echo --folder=name  naked foldername: no schema, no query, no www, no final slash, i.e. siteria.it
	echo --help         this message
	echo

fi

## 
## end of "show syntax"
## 
## ############################################################
## ############################################################
