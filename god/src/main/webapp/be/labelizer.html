{% extends "be/include/base.html" %}

{% set area = 'SETTINGS' %}
{% set page = 'LABELIZER' %}
{% set title = 'Labelizer' %}
{% set postUrl = routes('BE_LABELIZER_RUN') %}

{% block extrahead %}
<title>{{ title }}</title>

<!-- Page labelizer -->
{% include "be/include/snippets/plugins/ckeditor.html" %}
{% include "be/include/snippets/plugins/validate.html" %}
{% include "be/include/snippets/plugins/maxlength.html" %}
{% include "be/include/snippets/plugins/sweetalert.html" %}
<script src="{{ contextPath }}/be/js/pages/labelizer.js?{{ buildNumber }}"></script>
<!-- /page labelizer -->
{% endblock %}

{% block content %}
<script class="reload-labelizer-on-load">
    addRoute('BE_LABELIZER', '{{ routes("BE_LABELIZER") }}');
    addRoute('BE_LABELIZER_PROGRESS', '{{ routes("BE_LABELIZER_PROGRESS") }}');
    addRoute('BE_LABELIZER_EXPORT', '{{ routes("BE_LABELIZER_EXPORT") }}');
</script>
<div class="row">
    <div class="col-xl-12">
        <form id="labelizer" class="form-validate-jquery" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">

            <div class="card">
                <div class="card-header d-sm-flex align-items-sm-center py-sm-0">
                    <h5 class="py-sm-3 mb-sm-auto">{{ title }}</h5>
                </div>

                <div class="card-body">
                    <fieldset>
                        <legend class="fs-base fw-bold border-bottom pb-2 mb-3">Dati principali</legend>

                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Path: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <input id="path" name="path" type="text" class="form-control maxlength"
                                       placeholder="C:\projects\god\god\src\main\webapp\be\tests" maxlength="64" required>
                                <div class="form-text text-muted">Path directory da tradurre</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Nome:</label>
                            <div class="col-lg-10">
                                <input id="name" name="name" type="text" class="form-control maxlength"
                                       placeholder="Agora" maxlength="64">
                                <div class="form-text text-muted">Nome per la visualizzazione</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Lingue da tradurre: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="it" id="langIt" checked>
                                    <label class="form-check-label" for="langIt">Italiano</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="en" id="langEn" checked>
                                    <label class="form-check-label" for="langEn">Inglese</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="fr" id="langFr">
                                    <label class="form-check-label" for="langFr">Francese</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="es" id="langEs">
                                    <label class="form-check-label" for="langEs">Spagnolo</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="de" id="langDe">
                                    <label class="form-check-label" for="langDe">Tedesco</label>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                    <legend class="fs-base fw-bold border-bottom pb-2 mb-3">Ultime 5 richieste</legend>
                    <div class="accordion" id="accordion_expanded">
                        {% if entries is not empty %}
                        {% for entry in entries %}
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button fw-semibold" type="button" data-bs-toggle="collapse" data-bs-target="#expanded_item{{ loop.index }}">
                                    {{ entry.request.name }}
                                </button>
                            </h2>
                            <div id="expanded_item{{ loop.index }}" class="accordion-collapse collapse {{ loop.index == 0 ? 'show' : '' }}" data-bs-parent="#accordion_expanded">
                                <div class="accordion-body">
                                    <div class="row mb-3">
                                        <div class="col-lg-3">
                                            <div class="card bg-primary text-white">
                                                <div class="card-body p-2">
                                                    <div class="d-flex align-items-center">
                                                        <i class="ph-ticket me-2"></i>
                                                        <div>Token Utilizzati: <span class="fw-bold">{{ entry.totalTokens }}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="card bg-success text-white mt-2">
                                                <div class="card-body p-2">
                                                    <div class="d-flex align-items-center">
                                                        <i class="ph-files me-2"></i>
                                                        <div>File Elaborati: <span class="fw-bold">{{ entry.responsesMap.size() }} ({{ entry.executionTime }})</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="card bg-success text-white mt-2">
                                                <div class="card-body p-2">
                                                    <div class="d-flex align-items-center">
                                                        <i class="ph-translate me-2"></i>
                                                        <div>Lingue Tradotte: <span class="fw-bold">{{ entry.request.languages }}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="card bg-info text-white mt-2">
                                                <div class="card-body p-2">
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <div class="d-flex align-items-center">
                                                            <i class="ph-download me-2"></i>
                                                            <div>Export MongoDB</div>
                                                        </div>
                                                        <button type="button" class="btn btn-light btn-sm export-mongodb-btn" data-request-id="{{ entry.request.id }}">
                                                            <i class="ph-download me-1"></i>
                                                            Scarica JSON
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-9">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6 class="mb-0">File elaborati</h6>
                                                </div>
                                                <div class="card-body p-0" style="max-height: 200px; overflow-y: auto;">
                                                    <div class="table-responsive">
                                                        <table class="table table-hover">
                                                            <thead>
                                                            <tr>
                                                                <th>Path</th>
                                                                <th class="text-center">Token</th>
                                                            </tr>
                                                            </thead>
                                                            <tbody>
                                                            {% for filePath in entry.responsesMap.keySet() %}
                                                            <tr>
                                                                <td>{{ filePath }}</td>
                                                                {% if entry.responsesMap.get(filePath).usage is not empty and entry.responsesMap.get(filePath).usage.total_tokens is not empty %}
                                                                <td class="text-center">{{ entry.responsesMap.get(filePath).usage.total_tokens }}</td>
                                                                {% else %}
                                                                <td class="text-center">N.A.</td>
                                                                {% endif %}
                                                            </tr>
                                                            {% endfor %}
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        {% endif %}
                    </div>
                </div>
                <div class="card-footer d-flex justify-content-between align-items-center py-sm-2">
                    <div class="hstack gap-2 mt-0">
                        <button type="submit" class="btn btn-primary w-100 w-auto">
                            <i class="ph-check me-2"></i>
                            Elabora
                        </button>
                    </div>
                </div>
            </div>

        </form>

        <!-- Progress Display -->
        <div id="progress-container" class="card mt-3" style="display: none;">
            <div class="card-header">
                <h5 class="mb-0">Progresso Elaborazione</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="progress" style="height: 25px;">
                        <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                            0%
                        </div>
                    </div>
                </div>
                <div class="text-center">
                    <p id="progress-text" class="mb-1">Inizializzazione...</p>
                    <p id="current-file" class="text-muted small mb-0"></p>
                </div>
            </div>
        </div>

    </div>
</div>

{% endblock %}