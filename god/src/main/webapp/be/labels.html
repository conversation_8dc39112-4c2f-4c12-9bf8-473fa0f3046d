{% extends "be/include/base.html" %}

{% set area = 'TRANSLATIONS' %}
{% set page = 'TRANSLATIONS_TEXT' %}
{% set title = 'Traduzioni' %}

{% block extrahead %}
<title>{{ title }}</title>

<!-- Page script -->
<link href="https://anubi.us/static/lib/handsontable/handsontable.css" rel="stylesheet" type="text/css">
<script src="https://anubi.us/static/lib/handsontable/handsontable.min.js"></script>
<script src="{{ contextPath }}/be/js/pages/labels.js?{{ buildNumber }}"></script>        
<!-- /page script -->
{% endblock %}

{% block content %}
<script class="reload-script-on-load">
addRoute('BE_LABELS_DATA', '{{ routes("BE_LABELS_DATA") }}');
addRoute('BE_LABELS_SAVE', '{{ routes("BE_LABELS_SAVE") }}');
addVariables('languages', '{{ availableLanguages }}');
</script>
<div class="row">
    <div class="col-xl-12">

        <!-- Checkbox selection -->
        <div class="card">
            <div class="panel panel-white">
                <div class="card-header d-sm-flex align-items-sm-center py-sm-0">
                    <h5 class="py-sm-3 mb-sm-0">Traduzioni testi</h5>
                    <div class="ms-sm-auto my-sm-auto">
                        <a id="hot_save" href="#" class="btn btn-primary w-100">
                            <i class="ph-plus me-2"></i>
                            SALVA TRADUZIONI
                        </a>
                    </div>
                </div>

                <div class="panel-body">
                    <div class="form-group has-feedback has-feedback-left">
                        <input type="text" id="hot_search_basic_input" class="form-control" placeholder="Cerca per chiave o descrizione...">
                        <div class="form-control-feedback">
                            <i class="icon-search4 text-size-small"></i>
                        </div>
                    </div>

                    <div class="hot-container">
                        <div id="hot_search_basic"></div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /checkbox selection -->
    </div>
</div>

{% endblock %}