<!DOCTYPE html>
<html lang="{{ language }}" dir="ltr">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <title></title>

        <!-- Global stylesheets -->
        <link href="https://anubi.us/static/themes/b/1/4.0/assets/fonts/inter/inter.css" rel="stylesheet" type="text/css">
        <link href="https://anubi.us/static/themes/b/1/4.0/assets/icons/phosphor/styles.min.css" rel="stylesheet" type="text/css">
        <link href="https://anubi.us/static/themes/b/1/4.0/html/layout_1/full/assets/css/ltr/all.min.css" id="stylesheet" rel="stylesheet" type="text/css">
        <!-- /global stylesheets -->

        <!-- Core JS files -->	
        <script src="https://anubi.us/static/themes/b/1/4.0/assets/js/bootstrap/bootstrap.bundle.min.js"></script>
        <!-- /core JS files -->

        <!-- Theme JS files -->
        <script src="https://anubi.us/static/themes/b/1/4.0/assets/js/jquery/jquery.min.js"></script>
        <script src="https://anubi.us/static/themes/b/1/4.0/assets/js/vendor/forms/validation/validate.min.js"></script>
        <script src="https://anubi.us/static/themes/b/1/4.0/assets/js/vendor/forms/validation/localization/messages_it.min.js"></script>
        <script src="https://anubi.us/static/themes/b/1/4.0/html/layout_1/full/assets/js/app.js"></script>
        <!-- /theme JS files -->

        <!-- Page JS files -->        
        <script src="{{ contextPath }}/be/js/pages/login.js?{{ buildNumber }}"></script>
        <!-- /page JS files -->

    </head>

    <body class="bg-dark">

        <!-- Page content -->
        <div class="page-content">

            <!-- Main content -->
            <div class="content-wrapper">

                <!-- Inner content -->
                <div class="content-inner">

                    <!-- Content area -->
                    <div class="content d-flex justify-content-center align-items-center">

                        <!-- Login card -->
                        {% set postUrl = routes('BE_LOGIN_DO') %}
                        <form method="POST" action="{{ postUrl }}" class="login-form form-validate-jquery" enctype="multipart/form-data">
                            <div class="card mb-0">
                                <div class="card-body">
                                    <div class="text-center mb-3">
                                        <div class="d-inline-flex align-items-center justify-content-center mb-4 mt-2">
                                            <img src="{{ contextPath }}/be/imgs/logo.svg" class="h-48px" alt="">
                                        </div>
                                        <h5 class="mb-0">Accedi al tuo account</h5>
                                        <span class="d-block text-muted">Inserisci le tue credenziali</span>
                                    </div>

                                    {% if wrongUsernamePassword is not empty and wrongUsernamePassword %}
                                    <div class="alert alert-danger alert-dismissible fade show">
                                        <i class="ph-x-circle me-2"></i>
                                        <span class="fw-semibold">Oh no!</span> Username o Password errati.
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                    {% endif %}

                                    <div class="mb-3">
                                        <label class="form-label">Email</label>
                                        <div class="form-control-feedback form-control-feedback-start">
                                            <input type="email" name="username" id="email" class="form-control" placeholder="Email" required>
                                            <div class="form-control-feedback-icon">
                                                <i class="ph-user-circle text-muted"></i>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Password</label>
                                        <div class="form-control-feedback form-control-feedback-start">
                                            <input type="password" name="password" id="password" class="form-control" placeholder="Password" required>
                                            <div class="form-control-feedback-icon">
                                                <i class="ph-lock text-muted"></i>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex align-items-center mb-3">
                                        <label class="form-check">
                                            <input type="checkbox" name="remember" class="form-check-input" checked>
                                            <span class="form-check-label">Ricordami</span>
                                        </label>

                                        <a href="" class="ms-auto">Password dimenticata?</a>
                                    </div>

                                    <div class="mb-3">
                                        <button type="submit" class="btn btn-primary w-100">Accedi</button>
                                    </div>

                                </div>
                            </div>
                        </form>
                        <!-- /login card -->

                    </div>
                    <!-- /content area -->

                </div>
                <!-- /inner content -->

            </div>
            <!-- /main content -->

        </div>
        <!-- /page content -->        

    </body>
</html>