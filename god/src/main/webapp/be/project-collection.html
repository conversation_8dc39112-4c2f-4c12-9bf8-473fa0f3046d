{% extends "be/include/base.html" %}

{% set area = 'SETTINGS' %}
{% set page = 'PROJECTS' %}
{% set title = '<PERSON>getti' %}

{% block extrahead %}
<title>{{ title }}</title>

<!-- Page project -->
{% include "be/include/snippets/plugins/datatable.html" %}
<script src="{{ contextPath }}/be/js/pages/project-collection.js?{{ buildNumber }}"></script>
<!-- /page project -->
{% endblock %}

{% block content %}
<script class="reload-project-on-load">
addRoute('BE_PROJECT_DATA', '{{ routes("BE_PROJECT_DATA") }}');
addRoute('BE_PROJECT_OPERATE', '{{ routes("BE_PROJECT_OPERATE") }}');
addRoute('BE_PROJECT_CREATE_SCRIPT', '{{ routes("BE_PROJECT_CREATE_SCRIPT") }}');
addRoute('BE_SERVER_EXECUTE_COMMAND', '{{ routes("BE_SERVER_EXECUTE_COMMAND") }}');
</script>
<div class="row">
    <div class="col-xl-12">

        <!-- Checkbox selection -->
        <div class="card">
            <div class="card-header d-sm-flex align-items-sm-center py-sm-0">
                <h5 class="py-sm-3 mb-sm-0">{{ title }}</h5>
                {% if user.profileType equals "system" %}
                <div class="ms-sm-auto my-sm-auto">
                    <a href="{{ routes('BE_PROJECT') }}" class="btn btn-primary w-100">
                        <i class="ph-plus me-2"></i>
                        NUOVO PROGETTO
                    </a>
                </div>
                {% endif %}
            </div>

            <table class="table datatable">
                <thead>
                    <tr>
                        <th></th>
                        <th>Nome</th>
                        <th>Ultima modifica</th>
                        <th>Git Owner</th>
                        <th class="col-2">Backup</th>
                        <th class="col-2">Deploy</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
            </table>
        </div>
        <!-- /checkbox selection -->
    </div>
</div>

{% endblock %}