{% extends "be/include/base.html" %}

{% set area = 'LABELIZER' %}
{% set title = 'Labelizer' %}
{% set postUrl = routes('BE_PROJECT') %}

{% block extrahead %}
<title>{{ title }}</title>

<!-- Page project -->
{% include "be/include/snippets/plugins/ckeditor.html" %}
{% include "be/include/snippets/plugins/select2.html" %}
{% include "be/include/snippets/plugins/daterangepicker.html" %}
{% include "be/include/snippets/plugins/validate.html" %}
{% include "be/include/snippets/plugins/maxlength.html" %}
{% include "be/include/snippets/plugins/sweetalert.html" %}
<script src="{{ contextPath }}/be/js/pages/project.js?{{ buildNumber }}"></script>
<!-- /page project -->
{% endblock %}

{% block content %}
<script class="reload-project-on-load">
addRoute('BE_IMAGE', '{{ routes("BE_IMAGE") }}');
addRoute('BE_PROJECT', '{{ routes("BE_PROJECT") }}');
addRoute('BE_IMAGE_SAVE', '{{ routes("BE_IMAGE_SAVE") }}');
addVariables('projectId', '{{ project.id }}');
</script>
<div class="row justify-content-center">
    <div class="col-xxl-10">
        {% set postUrl = routes('BE_PROJECT_SAVE') %}
        {% if project.id is not empty %}
        {% set postUrl = routes('BE_PROJECT_SAVE') + '?projectId=' + project.id %}
        {% endif %}
        <form id="project" class="form-validate-jquery" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">

            <div class="card">
                <div class="card-header d-sm-flex align-items-sm-center py-sm-0">
                    <h5 class="py-sm-3 mb-sm-auto">{{ title }}</h5>
                    <div class="ms-sm-auto my-sm-auto">

                    </div>
                </div>

                <div class="card-body">
                    <fieldset>

                        <legend class="fs-base fw-bold border-bottom pb-2 mb-3">Dati principali</legend>

                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Nome: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <input id="name" name="name" type="text" class="form-control maxlength" placeholder="Nome" value="{{ project.name }}" maxlength="64" required>
                                <div class="form-text text-muted">Identificativo del progetto</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Server: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <select name="serverId" class="form-control" data-minimum-results-for-search="Infinity" required>
                                    <option value=""></option>
                                    {% set servers = lookup('Server', checkPublished=false, language='false') %}
                                    {% if servers is not empty %}
                                    {% for server in servers %}
                                    <option value="{{ server.id }}" {{ project.serverId equals server.id ? 'selected' : '' }}>{{ server.name }} ({{ server.ip }})</option>
                                    {% endfor %}
                                    {% endif %}
                                </select>
                                <div class="form-text text-muted">Seleziona il server dove verrà poi deployato il progetto.</div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Nome Git: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <input id="gitName" name="gitName" type="text" class="form-control maxlength" placeholder="Furlan87" value="{{ project.gitName }}" maxlength="48" required>
                                <div class="form-text text-muted">Nome dell'owner del progetto</div>
                            </div>
                        </div>
                    </fieldset>
                </div>
                <div class="card-footer d-flex justify-content-between align-items-center py-sm-2">
                    <div class="btn-group w-auto mt-sm-0">
                        <button class="btn btn-danger w-100 w-sm-auto"><i class="ph-trash me-sm-2"></i><span class="d-none d-sm-block">Archivia</span></button>
                        <button class="btn btn-danger dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false"></button>
                        <div class="dropdown-menu dropdown-menu-end" style="">
                            <a href="#" class="dropdown-item"><i class="ph-x me-2"></i> Elimina</a>                            
                        </div>
                    </div>
                    <div class="hstack gap-2 mt-0">
                        <a href="{{ routes('BE_PROJECT_COLLECTION') }}" class="btn btn-light w-auto btn-cancel">
                            <i class="ph-arrow-u-up-left me-sm-2"></i>
                            <span class="d-none d-sm-block">Annulla</span>
                        </a>
                        <button type="submit" class="btn btn-primary w-100 w-auto">
                            <i class="ph-check me-2"></i>
                            Salva
                        </button>
                    </div>
                </div>
            </div>

        </form>
    </div>
</div>

{% endblock %}

{% block sidebar %}
{% endblock %}