{% extends "be/include/base.html" %}

{% set area = 'SETTINGS' %}
{% set page = 'SERVERS' %}
{% set title = 'Server' %}

{% block extrahead %}
<title>{{ title }}</title>

<!-- Page server -->
{% include "be/include/snippets/plugins/datatable.html" %}
<script src="{{ contextPath }}/be/js/pages/server-collection.js?{{ buildNumber }}"></script>
<!-- /page server -->
{% endblock %}

{% block content %}
<script class="reload-server-on-load">
addRoute('BE_SERVER_DATA', '{{ routes("BE_SERVER_DATA") }}');
addRoute('BE_SERVER_OPERATE', '{{ routes("BE_SERVER_OPERATE") }}');
addRoute('BE_SERVER_EXECUTE_COMMAND', '{{ routes("BE_SERVER_EXECUTE_COMMAND") }}');
</script>
<div class="row">
    <div class="col-xl-12">

        <!-- Checkbox selection -->
        <div class="card">
            <div class="card-header d-sm-flex align-items-sm-center py-sm-0">
                <h5 class="py-sm-3 mb-sm-0">{{ title }}</h5>
                {% if user.profileType equals "system" %}
                <div class="ms-sm-auto my-sm-auto">
                    <a href="{{ routes('BE_SERVER') }}" class="btn btn-primary w-100">
                        <i class="ph-plus me-2"></i>
                        NUOVO SERVER
                    </a>
                </div>
                {% endif %}
            </div>

            <table class="table datatable">
                <thead>
                    <tr>
                        <th></th>
                        <th>Nome</th>
                        <th>IP</th>
                        <th>Ultima modifica</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
            </table>
        </div>
        <!-- /checkbox selection -->
    </div>
</div>

{% endblock %}