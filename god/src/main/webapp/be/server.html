{% extends "be/include/base.html" %}

{% set area = 'SERVER' %}
{% set title = server is empty ? 'Nuovo server ' : 'Modifica server' %}
{% set postUrl = routes('BE_SERVER') %}
{% if server.id is not empty %}
{% set postUrl = routes('BE_SERVER') + '?serverId=' + server.id %}
{% endif %}

{% block extrahead %}
<title>{{ title }}</title>

<!-- Page server -->
{% include "be/include/snippets/plugins/ckeditor.html" %}
{% include "be/include/snippets/plugins/select2.html" %}
{% include "be/include/snippets/plugins/daterangepicker.html" %}
{% include "be/include/snippets/plugins/validate.html" %}
{% include "be/include/snippets/plugins/maxlength.html" %}
{% include "be/include/snippets/plugins/sweetalert.html" %}
<script src="{{ contextPath }}/be/js/pages/server.js?{{ buildNumber }}"></script>
<!-- /page server -->
{% endblock %}

{% block content %}
<script class="reload-server-on-load">
addRoute('BE_IMAGE', '{{ routes("BE_IMAGE") }}');
addRoute('BE_SERVER', '{{ routes("BE_SERVER") }}');
addRoute('BE_IMAGE_SAVE', '{{ routes("BE_IMAGE_SAVE") }}');
addVariables('serverId', '{{ server.id }}');
</script>
<div class="row justify-content-center">
    <div class="col-xxl-10">
        {% set postUrl = routes('BE_SERVER_SAVE') %}
        {% if server.id is not empty %}
        {% set postUrl = routes('BE_SERVER_SAVE') + '?serverId=' + server.id %}
        {% endif %}
        <form id="server" class="form-validate-jquery" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">

            <div class="card">
                <div class="card-header d-sm-flex align-items-sm-center py-sm-0">
                    <h5 class="py-sm-3 mb-sm-auto">{{ title }}</h5>
                    <div class="ms-sm-auto my-sm-auto">

                    </div>
                </div>

                <div class="card-body">
                    <fieldset>

                        <legend class="fs-base fw-bold border-bottom pb-2 mb-3">Dati principali</legend>

                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Nome: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <input id="name" name="name" type="text" class="form-control maxlength" placeholder="Nome" value="{{ server.name }}" maxlength="300" required>
                                <div class="form-text text-muted">Identificativo del server</div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">IP: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <input id="ip" name="ip" type="text" class="form-control maxlength" placeholder="IP" value="{{ server.ip }}" maxlength="300" required>
                                <div class="form-text text-muted">IP del server</div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Username: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <input id="username" name="username" type="text" class="form-control maxlength" placeholder="Username" value="{{ server.username }}" maxlength="300" required>
                                <div class="form-text text-muted">Username per SSH</div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Password: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <input id="password" name="password" type="password" class="form-control maxlength" placeholder="Password" value="{{ server.password }}" maxlength="300" required>
                                <div class="form-text text-muted">Password per SSH</div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Username FTP: </label>
                            <div class="col-lg-10">
                                <input id="usernameFtp" name="usernameFtp" type="text" class="form-control maxlength" placeholder="Username" value="{{ server.usernameFtp }}" maxlength="300">
                                <div class="form-text text-muted">Username per FTP (specificare solo se diverso da SSH)</div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Password FTP: </label>
                            <div class="col-lg-10">
                                <input id="passwordFtp" name="passwordFtp" type="password" class="form-control maxlength" placeholder="Password" value="{{ server.passwordFtp }}" maxlength="300">
                                <div class="form-text text-muted">Password per FTP (specificare solo se diverso da SSH)</div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Username Tomcat: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <input id="tomcatUsername" name="tomcatUsername" type="text" class="form-control maxlength" placeholder="Username" value="{{ server.tomcatUsername }}" maxlength="300" required>
                                <div class="form-text text-muted">Username account admin di tomcat (per undeploy)</div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Password Tomcat: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <input id="tomcatPassword" name="tomcatPassword" type="password" class="form-control maxlength" placeholder="Password" value="{{ server.tomcatPassword }}" maxlength="300" required>
                                <div class="form-text text-muted">Password account admin di tomcat (per undeploy)</div>
                            </div>
                        </div>
                    </fieldset>
                </div>
                <div class="card-footer d-flex justify-content-between align-items-center py-sm-2">
                    <div class="btn-group w-auto mt-sm-0">
                        <button class="btn btn-danger w-100 w-sm-auto"><i class="ph-trash me-sm-2"></i><span class="d-none d-sm-block">Archivia</span></button>
                        <button class="btn btn-danger dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false"></button>
                        <div class="dropdown-menu dropdown-menu-end" style="">
                            <a href="#" class="dropdown-item"><i class="ph-x me-2"></i> Elimina</a>                            
                        </div>
                    </div>
                    <div class="hstack gap-2 mt-0">
                        <a href="{{ routes('BE_SERVER_COLLECTION') }}" class="btn btn-light w-auto btn-cancel">
                            <i class="ph-arrow-u-up-left me-sm-2"></i>
                            <span class="d-none d-sm-block">Annulla</span>
                        </a>
                        <button type="submit" class="btn btn-primary w-100 w-auto">
                            <i class="ph-check me-2"></i>
                            Salva
                        </button>
                    </div>
                </div>
            </div>

        </form>
    </div>
</div>

{% endblock %}

{% block sidebar %}
{% endblock %}