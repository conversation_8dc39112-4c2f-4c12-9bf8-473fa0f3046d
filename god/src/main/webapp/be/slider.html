{% extends "be/include/base.html" %}

{% block extrahead %}
<title>Slider</title>
<script src="https://anubi.us/static/themes/b/1/4.0/assets/js/jquery/jquery.min.js"></script>
<script src="https://anubi.us/static/themes/b/1/4.0/assets/js/vendor/tables/datatables/datatables.min.js"></script>
<script src="https://anubi.us/static/themes/b/1/4.0/assets/js/vendor/tables/datatables/extensions/select.min.js"></script>
<script src="https://anubi.us/static/themes/b/1/4.0/assets/js/vendor/tables/datatables/extensions/buttons.min.js"></script>    
<script src="https://anubi.us/static/themes/b/1/4.0/assets/demo/pages/datatables_extension_select.js"></script>
<!--<script src="{{ contextPath }}/be/js/pages/slider.js?{{ buildNumber }}"></script>-->
{% endblock %}

{% block content %}

<!-- Content area -->
<div class="content container pt-0">

    <!-- Checkbox selection -->
    <div class="card">
        <div class="card-header d-sm-flex align-items-sm-center py-sm-0">
            <h5 class="py-sm-3 mb-sm-0">Lista di Slider</h5>
            <div class="ms-sm-auto my-sm-auto">
                <a href="{{ routes('BE_SLIDER_EDIT') }}" class="btn bg-yellow heading-btn"><i class="icon-plus-circle2 position-left"></i>NUOVO SLIDER</a>
            </div>
        </div>

        <table class="table datatable-select-checkbox">
            <thead>
                <tr>
                    <th></th>
                    <th>Title</th>
                    <th>Sub Title</th>
                    <th>Link Text</th>
                    <th>Link</th>
                    <th>Language</th>
                    <th>Page</th>
                    <th class="text-center">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% if sliderList is not empty %}
                {% for entry in sliderList %}
                <tr>
                    <td></td>
                    <td>
                        <a href="{{ routes('BE_SLIDER_EDIT') }}?sliderId={{ entry.id }}">{{ entry.title }}</a>
                    </td>
                    <td>{{ entry.subtitle }}</td>
                    <td>{{ entry.linkText }}</td>
                    <td>{{ entry.link }}</td>
                    <td>{{ entry.language }}</td>
                    <td>{{ entry.page }}</td>
                    <td></td>
                </tr>
                {% endfor %}
                {% endif %}
            </tbody>
        </table>
    </div>
    <!-- /checkbox selection -->
</div>
<!-- /content area -->

{% endblock %}